#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试一体化启动功能
"""

import subprocess
import time
import requests
import sys
from pathlib import Path

def test_integrated_launch():
    """测试一体化启动"""
    print("🚀 测试一体化启动功能")
    print("=" * 50)
    
    # 测试纯后台模式
    print("🧪 测试1: 纯后台模式")
    try:
        result = subprocess.run([
            sys.executable, "start_backend_v2.py", 
            "--doc-num", "ZDBXD2025051300001",
            "--no-web"
        ], timeout=30, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 纯后台模式测试通过")
        else:
            print(f"❌ 纯后台模式测试失败: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("⏰ 纯后台模式测试超时（正常，因为审核需要时间）")
    except Exception as e:
        print(f"❌ 纯后台模式测试异常: {e}")
    
    print()
    
    # 测试Web服务启动
    print("🧪 测试2: Web服务启动")
    try:
        # 启动一体化服务（后台运行）
        process = subprocess.Popen([
            sys.executable, "start_backend_v2.py",
            "--doc-num", "ZDBXD2025051300001",
            "--no-browser"  # 不打开浏览器，只启动服务
        ])
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(10)
        
        # 测试API端点
        api_endpoints = [
            "http://localhost:8001/api/status",
            "http://localhost:8002/frontend/ai_console.html"
        ]
        
        for endpoint in api_endpoints:
            try:
                response = requests.get(endpoint, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint} - 可访问")
                else:
                    print(f"❌ {endpoint} - 状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint} - 连接失败: {e}")
        
        # 终止进程
        process.terminate()
        process.wait(timeout=5)
        print("🛑 测试进程已终止")
        
    except Exception as e:
        print(f"❌ Web服务测试异常: {e}")
    
    print()
    print("📊 测试完成")

if __name__ == "__main__":
    test_integrated_launch()

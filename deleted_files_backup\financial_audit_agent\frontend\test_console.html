<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI控制台测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f3a 50%, #2a2f4a 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        h1 {
            color: #00d4ff;
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        .test-card {
            background: rgba(26, 31, 58, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }
        .test-btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #00d4ff, #8b5cf6);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        .test-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: 500;
        }
        .status.success {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
        }
        .status.error {
            background: rgba(255, 51, 102, 0.2);
            border: 1px solid #ff3366;
            color: #ff3366;
        }
        .file-list {
            text-align: left;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .file-item {
            padding: 5px 0;
            color: #a0a9c0;
        }
        .file-item.exists {
            color: #00ff88;
        }
        .file-item.missing {
            color: #ff3366;
        }
        .instructions {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        .instructions h3 {
            color: #8b5cf6;
            margin-top: 0;
        }
        .instructions ol {
            line-height: 1.8;
        }
        .instructions li {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI控制台测试页面</h1>
        
        <div class="test-card">
            <h2>📁 文件检查状态</h2>
            <div id="file-status" class="status">正在检查文件...</div>
            <div class="file-list" id="file-list">
                <!-- 文件列表将通过JavaScript填充 -->
            </div>
        </div>

        <div class="test-card">
            <h2>🌐 快速访问</h2>
            <a href="ai_console.html" class="test-btn" target="_blank">🚀 AI控制台</a>
            <a href="ai_results.html" class="test-btn" target="_blank">📊 结果展示</a>
            <a href="audit_viewer.html" class="test-btn" target="_blank">📋 原版界面</a>
        </div>

        <div class="instructions">
            <h3>🎯 使用说明</h3>
            <ol>
                <li><strong>启动服务器</strong>：运行 <code>python quick_test_console.py</code></li>
                <li><strong>访问控制台</strong>：点击上方"AI控制台"按钮</li>
                <li><strong>开始审核</strong>：在控制台中点击"开始智能审核"</li>
                <li><strong>观察过程</strong>：查看AI思维可视化和规则执行状态</li>
                <li><strong>查看结果</strong>：审核完成后点击"查看结果"</li>
            </ol>
        </div>

        <div class="test-card">
            <h2>🔧 故障排除</h2>
            <p>如果遇到问题，请检查：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>确保所有文件都存在（上方文件检查）</li>
                <li>检查浏览器控制台是否有错误</li>
                <li>确认服务器正在运行</li>
                <li>尝试刷新页面</li>
            </ul>
        </div>
    </div>

    <script>
        // 检查必要文件是否存在
        async function checkFiles() {
            const files = [
                'ai_console.html',
                'ai_console.css', 
                'ai_console.js',
                'ai_results.html',
                'ai_results.css',
                'ai_results.js'
            ];
            
            const fileList = document.getElementById('file-list');
            const statusDiv = document.getElementById('file-status');
            
            let allExists = true;
            let html = '';
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const exists = response.ok;
                    
                    html += `<div class="file-item ${exists ? 'exists' : 'missing'}">
                        ${exists ? '✅' : '❌'} ${file}
                    </div>`;
                    
                    if (!exists) allExists = false;
                } catch (error) {
                    html += `<div class="file-item missing">❌ ${file} (检查失败)</div>`;
                    allExists = false;
                }
            }
            
            fileList.innerHTML = html;
            
            if (allExists) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ 所有文件检查通过，AI控制台已就绪！';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 部分文件缺失，请检查文件完整性';
            }
        }
        
        // 页面加载完成后检查文件
        document.addEventListener('DOMContentLoaded', checkFiles);
        
        // 添加一些动态效果
        document.querySelectorAll('.test-btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.05)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>

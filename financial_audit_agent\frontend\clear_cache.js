
// 清理前端缓存的脚本
console.log('🧹 清理AI显示缓存...');

// 清理localStorage
if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('ai_thinking_cache');
    localStorage.removeItem('last_thinking_content');
    console.log('✅ localStorage已清理');
}

// 清理sessionStorage  
if (typeof sessionStorage !== 'undefined') {
    sessionStorage.removeItem('ai_thinking_cache');
    sessionStorage.removeItem('last_thinking_content');
    console.log('✅ sessionStorage已清理');
}

// 强制刷新AI显示区域
const thinkingContent = document.getElementById('thinking-content');
if (thinkingContent) {
    thinkingContent.innerHTML = '<div class="thinking-step"><div class="step-indicator active"></div><div class="step-text">🔄 正在重新加载AI分析内容...</div></div>';
    console.log('✅ AI显示区域已重置');
}

console.log('🎉 缓存清理完成，请刷新页面');

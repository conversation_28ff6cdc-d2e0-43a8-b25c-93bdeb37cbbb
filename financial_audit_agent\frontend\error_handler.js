// 智能错误处理和用户引导系统
class SmartErrorHandler {
    constructor() {
        this.errorCount = 0;
        this.lastError = null;
        this.userGuidanceShown = false;
        this.init();
    }
    
    init() {
        // 全局错误捕获
        window.addEventListener('error', (event) => {
            this.handleError('JavaScript Error', event.error);
        });
        
        // Promise错误捕获
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError('Promise Rejection', event.reason);
        });
        
        // 网络错误监控
        this.setupNetworkMonitoring();
        
        // 用户引导系统
        this.setupUserGuidance();
    }
    
    handleError(type, error) {
        this.errorCount++;
        this.lastError = { type, error, timestamp: new Date() };
        
        console.error(`[${type}]`, error);
        
        // 根据错误类型提供智能建议
        if (this.isNetworkError(error)) {
            this.showNetworkErrorGuidance();
        } else if (this.isDataError(error)) {
            this.showDataErrorGuidance();
        } else {
            this.showGenericErrorGuidance();
        }
    }
    
    isNetworkError(error) {
        const networkKeywords = ['fetch', 'network', 'connection', 'timeout', 'cors'];
        const errorStr = error.toString().toLowerCase();
        return networkKeywords.some(keyword => errorStr.includes(keyword));
    }
    
    isDataError(error) {
        const dataKeywords = ['json', 'parse', 'undefined', 'null'];
        const errorStr = error.toString().toLowerCase();
        return dataKeywords.some(keyword => errorStr.includes(keyword));
    }
    
    setupNetworkMonitoring() {
        // 监控网络状态
        window.addEventListener('online', () => {
            this.showToast('🌐 网络连接已恢复', 'success');
            this.retryFailedRequests();
        });
        
        window.addEventListener('offline', () => {
            this.showToast('⚠️ 网络连接已断开', 'warning');
        });
    }
    
    setupUserGuidance() {
        // 首次访问引导
        if (!localStorage.getItem('ai_console_visited')) {
            setTimeout(() => {
                this.showWelcomeGuide();
                localStorage.setItem('ai_console_visited', 'true');
            }, 2000);
        }
        
        // 功能提示
        this.setupFeatureTips();
    }
    
    showWelcomeGuide() {
        const guide = this.createModal('welcome-guide', '🎉 欢迎使用AI财务审核控制台');
        
        guide.innerHTML += `
            <div class="guide-content">
                <div class="guide-step">
                    <div class="step-number">1</div>
                    <div class="step-text">
                        <h4>开始智能审核</h4>
                        <p>点击"开始智能审核"按钮，启动AI分析流程</p>
                    </div>
                </div>
                <div class="guide-step">
                    <div class="step-number">2</div>
                    <div class="step-text">
                        <h4>观察AI思维</h4>
                        <p>左侧显示AI大脑的实时思考过程和神经网络活动</p>
                    </div>
                </div>
                <div class="guide-step">
                    <div class="step-number">3</div>
                    <div class="step-text">
                        <h4>监控规则执行</h4>
                        <p>中间展示三层规则引擎的执行状态和进度</p>
                    </div>
                </div>
                <div class="guide-step">
                    <div class="step-number">4</div>
                    <div class="step-text">
                        <h4>查看结果</h4>
                        <p>审核完成后，点击"查看结果"查看科技感报告</p>
                    </div>
                </div>
                <div class="guide-actions">
                    <button class="guide-btn primary" onclick="this.closest('.modal-overlay').remove()">
                        开始体验
                    </button>
                    <button class="guide-btn secondary" onclick="this.showDemo()">
                        观看演示
                    </button>
                </div>
            </div>
        `;
        
        this.addGuideStyles();
    }
    
    setupFeatureTips() {
        // 智能提示系统
        const tips = [
            {
                selector: '#start-audit',
                message: '点击这里开始AI智能审核流程',
                trigger: 'hover',
                delay: 3000
            },
            {
                selector: '.ai-thinking-container',
                message: '这里显示AI的实时思考过程',
                trigger: 'click',
                delay: 5000
            },
            {
                selector: '.rules-layers',
                message: '三层规则引擎：确定性→关键词→语义分析',
                trigger: 'hover',
                delay: 8000
            }
        ];
        
        tips.forEach(tip => {
            setTimeout(() => {
                this.showFeatureTip(tip);
            }, tip.delay);
        });
    }
    
    showFeatureTip(tip) {
        const element = document.querySelector(tip.selector);
        if (!element) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'feature-tip';
        tooltip.innerHTML = `
            <div class="tip-content">
                ${tip.message}
                <button class="tip-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="tip-arrow"></div>
        `;
        
        document.body.appendChild(tooltip);
        
        // 定位提示框
        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2}px`;
        tooltip.style.top = `${rect.top - 10}px`;
        
        // 自动消失
        setTimeout(() => {
            if (tooltip.parentElement) {
                tooltip.remove();
            }
        }, 5000);
    }
    
    showNetworkErrorGuidance() {
        const modal = this.createModal('network-error', '🌐 网络连接问题');
        
        modal.innerHTML += `
            <div class="error-guidance">
                <div class="error-icon">🔌</div>
                <h3>检测到网络连接问题</h3>
                <div class="error-solutions">
                    <div class="solution">
                        <h4>1. 检查服务器状态</h4>
                        <p>确保API服务器正在运行：</p>
                        <code>python backend/api_server.py</code>
                    </div>
                    <div class="solution">
                        <h4>2. 检查网络连接</h4>
                        <p>确保网络连接正常，防火墙未阻止访问</p>
                    </div>
                    <div class="solution">
                        <h4>3. 重试连接</h4>
                        <button class="retry-btn" onclick="location.reload()">刷新页面</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    showDataErrorGuidance() {
        const modal = this.createModal('data-error', '📊 数据加载问题');
        
        modal.innerHTML += `
            <div class="error-guidance">
                <div class="error-icon">📋</div>
                <h3>数据加载出现问题</h3>
                <div class="error-solutions">
                    <div class="solution">
                        <h4>1. 检查审核报告</h4>
                        <p>确保存在审核报告文件：</p>
                        <code>audit_reports/audit_report_v2.json</code>
                    </div>
                    <div class="solution">
                        <h4>2. 运行审核流程</h4>
                        <p>先执行一次审核生成报告：</p>
                        <code>python backend/main.py</code>
                    </div>
                    <div class="solution">
                        <h4>3. 使用模拟数据</h4>
                        <button class="demo-btn" onclick="this.loadDemoData()">加载演示数据</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    showGenericErrorGuidance() {
        if (this.errorCount > 3) {
            this.showTechnicalSupport();
        }
    }
    
    showTechnicalSupport() {
        const modal = this.createModal('tech-support', '🔧 技术支持');
        
        modal.innerHTML += `
            <div class="error-guidance">
                <div class="error-icon">🛠️</div>
                <h3>系统遇到了一些问题</h3>
                <p>我们检测到多个错误，建议采取以下措施：</p>
                <div class="error-solutions">
                    <div class="solution">
                        <h4>1. 重启系统</h4>
                        <button class="restart-btn" onclick="this.restartSystem()">重新启动</button>
                    </div>
                    <div class="solution">
                        <h4>2. 查看错误日志</h4>
                        <button class="log-btn" onclick="this.showErrorLog()">查看日志</button>
                    </div>
                    <div class="solution">
                        <h4>3. 联系技术支持</h4>
                        <p>错误次数: ${this.errorCount}</p>
                        <p>最后错误: ${this.lastError?.type}</p>
                    </div>
                </div>
            </div>
        `;
    }
    
    createModal(id, title) {
        // 移除已存在的模态框
        const existing = document.getElementById(id);
        if (existing) existing.remove();
        
        const modal = document.createElement('div');
        modal.id = id;
        modal.className = 'modal-overlay error-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${title}</h2>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <!-- 内容将被添加到这里 -->
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        return modal.querySelector('.modal-body');
    }
    
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => toast.classList.add('show'), 100);
        
        // 自动消失
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
    
    addGuideStyles() {
        if (document.getElementById('guide-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'guide-styles';
        style.textContent = `
            .guide-content {
                padding: 20px 0;
            }
            .guide-step {
                display: flex;
                align-items: flex-start;
                margin: 20px 0;
                padding: 15px;
                background: rgba(0, 212, 255, 0.1);
                border-radius: 10px;
                border-left: 3px solid var(--accent-blue);
            }
            .step-number {
                width: 30px;
                height: 30px;
                background: var(--accent-blue);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-right: 15px;
                flex-shrink: 0;
            }
            .step-text h4 {
                margin: 0 0 5px 0;
                color: var(--accent-blue);
            }
            .step-text p {
                margin: 0;
                color: var(--text-secondary);
                line-height: 1.4;
            }
            .guide-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
                margin-top: 30px;
            }
            .guide-btn {
                padding: 12px 24px;
                border: none;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .guide-btn.primary {
                background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
                color: white;
            }
            .guide-btn.secondary {
                background: rgba(255, 255, 255, 0.1);
                color: var(--text-secondary);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .feature-tip {
                position: absolute;
                z-index: 10000;
                background: var(--bg-secondary);
                border: 1px solid var(--accent-blue);
                border-radius: 8px;
                padding: 0;
                box-shadow: var(--glow-blue);
                transform: translateX(-50%) translateY(-100%);
                animation: tipFadeIn 0.3s ease-out;
            }
            .tip-content {
                padding: 12px 15px;
                color: var(--text-primary);
                font-size: 0.9rem;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .tip-close {
                background: none;
                border: none;
                color: var(--text-secondary);
                cursor: pointer;
                font-size: 1.2rem;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .tip-arrow {
                position: absolute;
                bottom: -6px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid var(--accent-blue);
            }
            @keyframes tipFadeIn {
                0% { opacity: 0; transform: translateX(-50%) translateY(-100%) scale(0.8); }
                100% { opacity: 1; transform: translateX(-50%) translateY(-100%) scale(1); }
            }
            .toast {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            .toast.show {
                transform: translateX(0);
            }
            .toast-success {
                background: var(--accent-green);
                border: 1px solid var(--accent-green);
            }
            .toast-warning {
                background: var(--accent-orange);
                border: 1px solid var(--accent-orange);
            }
            .toast-error {
                background: var(--accent-red);
                border: 1px solid var(--accent-red);
            }
            .error-modal .modal-content {
                max-width: 600px;
            }
            .error-guidance {
                text-align: center;
            }
            .error-icon {
                font-size: 4rem;
                margin-bottom: 20px;
            }
            .error-solutions {
                text-align: left;
                margin-top: 30px;
            }
            .solution {
                margin: 20px 0;
                padding: 15px;
                background: rgba(0, 0, 0, 0.3);
                border-radius: 8px;
            }
            .solution h4 {
                color: var(--accent-blue);
                margin: 0 0 10px 0;
            }
            .solution p {
                margin: 5px 0;
                color: var(--text-secondary);
            }
            .solution code {
                background: rgba(0, 212, 255, 0.2);
                padding: 4px 8px;
                border-radius: 4px;
                font-family: monospace;
                color: var(--accent-blue);
            }
            .solution button {
                margin-top: 10px;
                padding: 8px 16px;
                background: var(--accent-blue);
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .solution button:hover {
                background: var(--accent-purple);
                transform: translateY(-1px);
            }
        `;
        document.head.appendChild(style);
    }
    
    retryFailedRequests() {
        // 重试失败的请求
        console.log('🔄 重试失败的网络请求...');
        // 这里可以实现具体的重试逻辑
    }
    
    loadDemoData() {
        // 加载演示数据
        console.log('📊 加载演示数据...');
        // 这里可以实现演示数据加载逻辑
    }
    
    restartSystem() {
        this.showToast('🔄 正在重新启动系统...', 'info');
        setTimeout(() => {
            location.reload();
        }, 2000);
    }
    
    showErrorLog() {
        console.log('错误日志:', {
            errorCount: this.errorCount,
            lastError: this.lastError,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        });
        
        this.showToast('📋 错误日志已输出到控制台', 'info');
    }
}

// 初始化错误处理系统
document.addEventListener('DOMContentLoaded', () => {
    new SmartErrorHandler();
});

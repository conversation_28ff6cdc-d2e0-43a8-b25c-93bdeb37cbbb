<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端数据同步测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1a1f3a 0%, #2d1b69 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
        }
        .error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #ff0000;
        }
        .info {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid #00d4ff;
        }
        button {
            background: linear-gradient(45deg, #00d4ff, #7b68ee);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        .status-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .data-display {
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前后端数据同步测试</h1>
        
        <div class="test-section">
            <h2>1. API服务器连接测试</h2>
            <button onclick="testAPIConnection()">测试API连接</button>
            <button onclick="detectAPIServer()">检测API服务器</button>
            <div id="api-results"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 数据端点测试</h2>
            <button onclick="testStatusEndpoint()">测试 /api/status</button>
            <button onclick="testReportEndpoint()">测试 /api/report</button>
            <button onclick="testRulesEndpoint()">测试 /api/rules</button>
            <button onclick="testProgressEndpoint()">测试 /api/progress</button>
            <div id="endpoint-results"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 本地文件访问测试</h2>
            <button onclick="testLocalFiles()">测试本地文件</button>
            <div id="file-results"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 实时数据轮询测试</h2>
            <button onclick="startPolling()">开始轮询</button>
            <button onclick="stopPolling()">停止轮询</button>
            <div class="status-display">
                <div>轮询状态: <span id="polling-status">未开始</span></div>
                <div>最后更新: <span id="last-update">--</span></div>
                <div>更新次数: <span id="update-count">0</span></div>
            </div>
            <div id="polling-results"></div>
        </div>
        
        <div class="test-section">
            <h2>5. 当前数据状态</h2>
            <button onclick="showCurrentData()">显示当前数据</button>
            <div id="current-data" class="data-display"></div>
        </div>
    </div>

    <script>
        let apiBaseUrl = null;
        let pollingInterval = null;
        let updateCount = 0;
        
        // 显示结果的辅助函数
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }
        
        // 检测API服务器
        async function detectAPIServer() {
            const possiblePorts = [8001, 8002, 8003, 8004, 8005];
            showResult('api-results', '🔍 开始检测API服务器...', 'info');
            
            for (const port of possiblePorts) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 2000);
                    
                    const response = await fetch(`http://localhost:${port}/api/status`, {
                        method: 'GET',
                        signal: controller.signal
                    });
                    
                    clearTimeout(timeoutId);
                    
                    if (response.ok) {
                        apiBaseUrl = `http://localhost:${port}`;
                        showResult('api-results', `✅ 检测到API服务器: ${apiBaseUrl}`, 'success');
                        return;
                    }
                } catch (error) {
                    showResult('api-results', `⚠️ 端口 ${port} 连接失败: ${error.message}`, 'error');
                }
            }
            
            showResult('api-results', '❌ 未检测到API服务器', 'error');
        }
        
        // 测试API连接
        async function testAPIConnection() {
            if (!apiBaseUrl) {
                await detectAPIServer();
            }
            
            if (apiBaseUrl) {
                showResult('api-results', `✅ API服务器可用: ${apiBaseUrl}`, 'success');
            } else {
                showResult('api-results', '❌ API服务器不可用', 'error');
            }
        }
        
        // 测试状态端点
        async function testStatusEndpoint() {
            if (!apiBaseUrl) await detectAPIServer();
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/status`);
                if (response.ok) {
                    const data = await response.json();
                    showResult('endpoint-results', `✅ /api/status 响应正常`, 'success');
                    showResult('endpoint-results', `数据: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    showResult('endpoint-results', `❌ /api/status 响应错误: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('endpoint-results', `❌ /api/status 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 测试报告端点
        async function testReportEndpoint() {
            if (!apiBaseUrl) await detectAPIServer();
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/report`);
                if (response.ok) {
                    const data = await response.json();
                    showResult('endpoint-results', `✅ /api/report 响应正常`, 'success');
                    showResult('endpoint-results', `报告摘要: 总规则${data.summary?.total_rules_checked || 0}条`, 'info');
                } else {
                    showResult('endpoint-results', `❌ /api/report 响应错误: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('endpoint-results', `❌ /api/report 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 测试规则端点
        async function testRulesEndpoint() {
            if (!apiBaseUrl) await detectAPIServer();
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/rules`);
                if (response.ok) {
                    const data = await response.json();
                    showResult('endpoint-results', `✅ /api/rules 响应正常`, 'success');
                    showResult('endpoint-results', `规则统计: 总计${data.total_rules || 0}条规则`, 'info');
                } else {
                    showResult('endpoint-results', `❌ /api/rules 响应错误: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('endpoint-results', `❌ /api/rules 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 测试进度端点
        async function testProgressEndpoint() {
            if (!apiBaseUrl) await detectAPIServer();
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/progress`);
                if (response.ok) {
                    const data = await response.json();
                    showResult('endpoint-results', `✅ /api/progress 响应正常`, 'success');
                    showResult('endpoint-results', `进度: ${data.overall_progress || 0}%`, 'info');
                } else {
                    showResult('endpoint-results', `❌ /api/progress 响应错误: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('endpoint-results', `❌ /api/progress 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 测试本地文件
        async function testLocalFiles() {
            const files = [
                '../backend/audit_status.json',
                '../audit_reports/audit_report_v2.json',
                '../audit_reports/audit_report.json'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        showResult('file-results', `✅ 文件可访问: ${file}`, 'success');
                    } else {
                        showResult('file-results', `❌ 文件不可访问: ${file} (${response.status})`, 'error');
                    }
                } catch (error) {
                    showResult('file-results', `❌ 文件访问失败: ${file} - ${error.message}`, 'error');
                }
            }
        }
        
        // 开始轮询
        function startPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
            
            document.getElementById('polling-status').textContent = '运行中';
            updateCount = 0;
            
            pollingInterval = setInterval(async () => {
                try {
                    let response;
                    let data;
                    
                    if (apiBaseUrl) {
                        response = await fetch(`${apiBaseUrl}/api/status`);
                        if (response.ok) {
                            data = await response.json();
                        }
                    } else {
                        response = await fetch('../backend/audit_status.json');
                        if (response.ok) {
                            data = await response.json();
                        }
                    }
                    
                    if (data) {
                        updateCount++;
                        document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
                        document.getElementById('update-count').textContent = updateCount;
                        showResult('polling-results', `📡 状态更新: ${data.message || data.current_step}`, 'success');
                    }
                } catch (error) {
                    showResult('polling-results', `⚠️ 轮询错误: ${error.message}`, 'error');
                }
            }, 3000);
        }
        
        // 停止轮询
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
            }
            document.getElementById('polling-status').textContent = '已停止';
        }
        
        // 显示当前数据
        async function showCurrentData() {
            const data = {
                apiBaseUrl: apiBaseUrl,
                timestamp: new Date().toISOString(),
                updateCount: updateCount
            };
            
            // 尝试获取最新状态
            try {
                if (apiBaseUrl) {
                    const response = await fetch(`${apiBaseUrl}/api/status`);
                    if (response.ok) {
                        data.apiStatus = await response.json();
                    }
                }
                
                const fileResponse = await fetch('../backend/audit_status.json');
                if (fileResponse.ok) {
                    data.fileStatus = await fileResponse.json();
                }
            } catch (error) {
                data.error = error.message;
            }
            
            document.getElementById('current-data').textContent = JSON.stringify(data, null, 2);
        }
        
        // 页面加载时自动检测API服务器
        window.addEventListener('load', () => {
            detectAPIServer();
        });
    </script>
</body>
</html>

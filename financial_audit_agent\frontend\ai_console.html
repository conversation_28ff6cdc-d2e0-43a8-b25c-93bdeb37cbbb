<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI财务审核控制台 - 智能分析中心</title>
    <link rel="stylesheet" href="ai_console.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 动态粒子背景 -->
    <div id="particles-background"></div>
    
    <!-- 主控制台界面 -->
    <div class="console-container">
        <!-- 顶部状态栏 -->
        <header class="console-header">
            <div class="header-left">
                <div class="logo-section">
                    <div class="ai-logo">
                        <div class="logo-core"></div>
                        <div class="logo-ring"></div>
                        <div class="logo-pulse"></div>
                    </div>
                    <div class="system-title">
                        <h1>AI财务审核系统</h1>
                        <span class="version">v2.0 Neural Engine</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="system-status">
                    <div class="status-indicator online" id="data-link-indicator"></div>
                    <span id="data-link-status">🔄 初始化数据联动...</span>
                </div>
                <div class="current-time" id="current-time"></div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="console-main">
            <!-- 左侧：AI大脑可视化 -->
            <section class="ai-brain-section">
                <div class="section-header">
                    <h2>🧠 AI分析引擎</h2>
                    <div class="neural-activity">
                        <span class="activity-dot"></span>
                        <span class="activity-text">神经网络活跃中</span>
                    </div>
                </div>
                
                <!-- AI思维可视化区域 -->
                <div class="ai-thinking-container">
                    <div class="thinking-display" id="ai-thinking">
                        <div class="thinking-header">
                            <div class="thinking-icon">🤖</div>
                            <div class="thinking-title">AI正在思考...</div>
                        </div>
                        <div class="thinking-content" id="thinking-content">
                            <div class="thinking-step">
                                <div class="step-indicator"></div>
                                <div class="step-text">初始化神经网络...</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 神经网络可视化 -->
                    <div class="neural-network" id="neural-network">
                        <div class="network-layer input-layer">
                            <div class="neuron active"></div>
                            <div class="neuron active"></div>
                            <div class="neuron active"></div>
                        </div>
                        <div class="network-layer hidden-layer">
                            <div class="neuron"></div>
                            <div class="neuron"></div>
                            <div class="neuron"></div>
                            <div class="neuron"></div>
                        </div>
                        <div class="network-layer output-layer">
                            <div class="neuron"></div>
                            <div class="neuron"></div>
                        </div>
                        <svg class="network-connections">
                            <!-- 连接线将通过JavaScript动态生成 -->
                        </svg>
                    </div>
                </div>
            </section>

            <!-- 中间：规则执行进度 -->
            <section class="rules-execution-section">
                <div class="section-header">
                    <h2>⚡ 规则引擎执行</h2>
                    <div class="execution-stats">
                        <span class="stat-item">
                            <span class="stat-value" id="rules-completed">0</span>
                            <span class="stat-label">已完成</span>
                        </span>
                        <span class="stat-item">
                            <span class="stat-value" id="total-rules">38</span>
                            <span class="stat-label">总规则</span>
                        </span>
                    </div>
                </div>

                <!-- 四大审核阶段可视化 -->
                <div class="rules-layers">
                    <!-- 附件完整性检查 -->
                    <div class="rule-layer" id="attachment-layer">
                        <div class="layer-header">
                            <div class="layer-icon">📎</div>
                            <div class="layer-info">
                                <h3>附件完整性检查</h3>
                                <span class="layer-desc">发票 · 审批表 · 小票 · 支付记录</span>
                            </div>
                            <div class="layer-status" id="attachment-status">待执行</div>
                        </div>
                        <div class="layer-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="attachment-progress"></div>
                            </div>
                            <span class="progress-text" id="attachment-text">0/5</span>
                        </div>
                    </div>

                    <!-- 字段内容与一致性检查 -->
                    <div class="rule-layer" id="consistency-layer">
                        <div class="layer-header">
                            <div class="layer-icon">🔍</div>
                            <div class="layer-info">
                                <h3>字段内容与一致性检查</h3>
                                <span class="layer-desc">数据一致性 · 信息核验 · 逻辑校验</span>
                            </div>
                            <div class="layer-status" id="consistency-status">待执行</div>
                        </div>
                        <div class="layer-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="consistency-progress"></div>
                            </div>
                            <span class="progress-text" id="consistency-text">0/19</span>
                        </div>
                    </div>

                    <!-- 金额与标准检查 -->
                    <div class="rule-layer" id="amount-layer">
                        <div class="layer-header">
                            <div class="layer-icon">💰</div>
                            <div class="layer-info">
                                <h3>金额与标准检查</h3>
                                <span class="layer-desc">预算控制 · 标准核验 · 消费分析</span>
                            </div>
                            <div class="layer-status" id="amount-status">待执行</div>
                        </div>
                        <div class="layer-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="amount-progress"></div>
                            </div>
                            <span class="progress-text" id="amount-text">0/6</span>
                        </div>
                    </div>

                    <!-- 八项规定合规性检查 -->
                    <div class="rule-layer" id="compliance-layer">
                        <div class="layer-header">
                            <div class="layer-icon">⚖️</div>
                            <div class="layer-info">
                                <h3>八项规定合规性检查</h3>
                                <span class="layer-desc">合规审查 · 风险识别 · 政策遵循</span>
                            </div>
                            <div class="layer-status" id="compliance-status">待执行</div>
                        </div>
                        <div class="layer-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="compliance-progress"></div>
                            </div>
                            <span class="progress-text" id="compliance-text">0/8</span>
                        </div>
                    </div>
                </div>


            </section>

            <!-- 右侧：实时数据流 -->
            <section class="data-stream-section">
                <div class="section-header">
                    <h2>📊 数据流监控</h2>
                    <div class="stream-status">
                        <div class="stream-indicator"></div>
                        <span>实时数据流</span>
                    </div>
                </div>

                <!-- 数据流可视化 -->
                <div class="data-stream-container">
                    <div class="stream-display" id="data-stream">
                        <!-- 数据流项目将通过JavaScript动态添加 -->
                    </div>
                </div>

                <!-- 审核统计监控 -->
                <div class="audit-statistics">
                    <h3>📈 审核统计</h3>
                    <div class="statistics-metrics">
                        <div class="metric">
                            <span class="metric-label">通过率</span>
                            <div class="metric-bar">
                                <div class="metric-fill success" id="pass-rate-bar" style="width: 0%"></div>
                            </div>
                            <span class="metric-value" id="pass-rate-value">0%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">风险等级</span>
                            <div class="metric-bar">
                                <div class="metric-fill warning" id="risk-level-bar" style="width: 0%"></div>
                            </div>
                            <span class="metric-value" id="risk-level-value">低风险</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">合规评分</span>
                            <div class="metric-bar">
                                <div class="metric-fill info" id="compliance-score-bar" style="width: 0%"></div>
                            </div>
                            <span class="metric-value" id="compliance-score-value">0分</span>
                        </div>
                    </div>
                </div>

                <!-- 实时风险提醒 -->
                <div class="risk-alerts">
                    <h3>⚠️ 风险提醒</h3>
                    <div class="alerts-container" id="risk-alerts-container">
                        <div class="alert-item info">
                            <div class="alert-icon">ℹ️</div>
                            <div class="alert-content">
                                <div class="alert-title">系统就绪</div>
                                <div class="alert-desc">等待开始审核</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部控制面板 -->
        <footer class="console-footer">
            <div class="control-panel">
                <button class="control-btn primary" id="start-audit">
                    <span class="btn-icon">▶</span>
                    <span class="btn-text">开始智能审核</span>
                </button>
                <button class="control-btn secondary" id="pause-audit" disabled>
                    <span class="btn-icon">⏸</span>
                    <span class="btn-text">暂停</span>
                </button>
                <button class="control-btn secondary" id="view-results" disabled>
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">查看结果</span>
                </button>
            </div>
            <div class="system-info">
                <span>AI引擎版本: Neural-2.0</span>
                <span>|</span>
                <span>规则库版本: v2.3.1</span>
                <span>|</span>
                <span>最后更新: <span id="last-update">2024-01-15</span></span>
            </div>
        </footer>
    </div>

    <!-- 结果展示模态框 -->
    <div class="results-modal" id="results-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎯 AI审核结果</h2>
                <button class="close-btn" id="close-modal">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- 结果内容将动态加载 -->
            </div>
        </div>
    </div>

    <!-- 集成所有优化组件 -->
    <script src="error_handler.js"></script>
    <script src="performance_monitor.js"></script>
    <!-- Markdown解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- 新的状态管理器 -->
    <script src="js/state_manager.js"></script>
    <script src="ai_console_enhanced.js"></script>

    <!-- 添加快捷键提示 -->
    <div class="shortcuts-hint" id="shortcuts-hint" style="display: none;">
        <div class="hint-content">
            <h4>⌨️ 快捷键</h4>
            <div class="shortcut-item">
                <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>P</kbd> - 性能报告
            </div>
            <div class="shortcut-item">
                <kbd>F5</kbd> - 刷新页面
            </div>
            <div class="shortcut-item">
                <kbd>Esc</kbd> - 关闭提示
            </div>
        </div>
    </div>

    <style>
        .shortcuts-hint {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(26, 31, 58, 0.9);
            border: 1px solid var(--accent-blue);
            border-radius: 10px;
            padding: 15px;
            z-index: 1000;
            backdrop-filter: blur(10px);
            box-shadow: var(--glow-blue);
        }
        .hint-content h4 {
            margin: 0 0 10px 0;
            color: var(--accent-blue);
            font-size: 1rem;
        }
        .shortcut-item {
            margin: 5px 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        .shortcut-item kbd {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid var(--accent-blue);
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 0.8rem;
            color: var(--accent-blue);
        }
    </style>

    <script>
        // 快捷键系统
        document.addEventListener('keydown', (event) => {
            // 显示/隐藏快捷键提示
            if (event.key === 'F1') {
                event.preventDefault();
                const hint = document.getElementById('shortcuts-hint');
                hint.style.display = hint.style.display === 'none' ? 'block' : 'none';
            }

            // 关闭提示
            if (event.key === 'Escape') {
                document.getElementById('shortcuts-hint').style.display = 'none';
            }
        });

        // 显示启动提示
        setTimeout(() => {
            const hint = document.getElementById('shortcuts-hint');
            hint.style.display = 'block';
            setTimeout(() => {
                hint.style.display = 'none';
            }, 5000);
        }, 3000);

        // 🚀 初始化AI控制台增强功能
        console.log('🚀 开始初始化AI控制台增强功能...');

        async function initializeAIConsoleEnhanced() {
            try {
                console.log('🔧 创建AIConsoleEnhanced实例...');
                if (typeof AIConsoleEnhanced !== 'undefined') {
                    window.aiConsole = new AIConsoleEnhanced();
                    console.log('🔧 调用init()方法...');
                    await window.aiConsole.init();
                    console.log('✅ AI控制台增强功能初始化成功');

                    // 显示初始化成功提示
                    const statusElement = document.getElementById('data-link-status');
                    const indicatorElement = document.getElementById('data-link-indicator');
                    if (statusElement) {
                        statusElement.textContent = '🔗 实时数据联动已启用';
                        statusElement.style.color = '#00ff88';
                    }
                    if (indicatorElement) {
                        indicatorElement.className = 'status-indicator online';
                    }
                } else {
                    console.error('❌ AIConsoleEnhanced类未找到');
                    // 显示错误提示
                    const statusElement = document.getElementById('data-link-status');
                    const indicatorElement = document.getElementById('data-link-indicator');
                    if (statusElement) {
                        statusElement.textContent = '⚠️ 数据联动功能加载失败';
                        statusElement.style.color = '#ff6b6b';
                    }
                    if (indicatorElement) {
                        indicatorElement.className = 'status-indicator offline';
                    }
                }
            } catch (error) {
                console.error('❌ AI控制台增强功能初始化失败:', error);
                // 显示错误提示
                const statusElement = document.getElementById('data-link-status');
                const indicatorElement = document.getElementById('data-link-indicator');
                if (statusElement) {
                    statusElement.textContent = '❌ 初始化失败: ' + error.message;
                    statusElement.style.color = '#ff6b6b';
                }
                if (indicatorElement) {
                    indicatorElement.className = 'status-indicator offline';
                }
            }
        }

        // 多种方式确保初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeAIConsoleEnhanced);
        } else {
            // DOM已经加载完成，立即初始化
            initializeAIConsoleEnhanced();
        }

        // 备用初始化（确保万无一失）
        window.addEventListener('load', () => {
            if (!window.aiConsole) {
                console.log('🔄 备用初始化AI控制台增强功能...');
                setTimeout(initializeAIConsoleEnhanced, 1000);
            }
        });

        // 添加调试信息显示
        setTimeout(() => {
            console.log('🔍 调试信息:');
            console.log('- window.aiConsole:', typeof window.aiConsole);
            console.log('- AIConsoleEnhanced:', typeof AIConsoleEnhanced);
            console.log('- 当前URL:', window.location.href);

            // 获取文档编号
            const urlParams = new URLSearchParams(window.location.search);
            const docNum = urlParams.get('doc_num');
            console.log('- 文档编号:', docNum);

            if (window.aiConsole && window.aiConsole.apiBaseUrl) {
                console.log('- API服务器:', window.aiConsole.apiBaseUrl);
            }
        }, 2000);
    </script>
</body>
</html>

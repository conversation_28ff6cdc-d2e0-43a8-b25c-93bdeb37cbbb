<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度页面调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .test-button {
            display: inline-block;
            margin: 10px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        #console-output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .result-link-container {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 进度页面调试工具</h1>
        
        <div class="debug-section">
            <h2>URL 信息</h2>
            <p><strong>当前URL:</strong> <span id="current-url"></span></p>
            <p><strong>查询参数:</strong> <span id="search-params"></span></p>
            <p><strong>检测到的单据编号:</strong> <span id="detected-doc"></span></p>
        </div>
        
        <div class="debug-section">
            <h2>模拟审核完成状态</h2>
            <button class="test-button" onclick="simulateAuditComplete()">模拟审核完成</button>
            <button class="test-button" onclick="testLinkGeneration()">测试链接生成</button>
            <button class="test-button" onclick="clearConsole()">清空控制台</button>
        </div>
        
        <!-- 模拟进度页面的结果容器 -->
        <div id="result-link-container" class="result-link-container" style="display: none;">
            <h2>审核完成！</h2>
            <p>您的审核报告已生成。</p>
            <a href="#" id="report-link" target="_blank" class="test-button">点击查看详细审核报告</a>
            <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; font-size: 0.9em; color: #6c757d;">
                <strong>生成的链接:</strong> <span id="generated-link-display">未生成</span>
            </div>
        </div>
        
        <div class="debug-section">
            <h2>控制台输出</h2>
            <div id="console-output">等待调试信息...</div>
        </div>
    </div>

    <script>
        // 重定向console输出到页面
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : '✅';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // 显示URL信息
        function updateURLInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('search-params').textContent = window.location.search || '无';
            
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
            document.getElementById('detected-doc').textContent = documentNumber || '无';
            
            console.log('🌐 页面URL信息更新完成');
            console.log('📋 当前URL:', window.location.href);
            console.log('🔍 查询参数:', window.location.search);
            console.log('📄 单据编号:', documentNumber);
        }
        
        // 复制进度页面的setupReportLink函数
        function setupReportLink() {
            console.log('🔧 Setting up report link...');
            
            const reportLink = document.getElementById('report-link');
            if (!reportLink) {
                console.error('❌ Report link element not found!');
                return;
            }
            console.log('✅ Report link element found:', reportLink);

            // 获取单据编号参数
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
            console.log('📋 Document number from URL:', documentNumber);
            console.log('🔍 All URL parameters:', window.location.search);
            console.log('🔍 URLSearchParams entries:', [...urlParams.entries()]);
            
            // 如果没有找到单据编号，尝试从其他地方获取
            if (!documentNumber) {
                console.log('⚠️ No document number found in URL parameters');
                // 尝试从全局变量获取（如果有的话）
                if (typeof window.DOCUMENT_NUMBER !== 'undefined') {
                    console.log('📋 Found document number in global variable:', window.DOCUMENT_NUMBER);
                }
            }

            // 检测当前页面的访问方式来确定正确的报告链接
            const currentUrl = window.location.href;
            console.log('🌐 Current URL:', currentUrl);
            let reportUrl;

            if (currentUrl.includes('localhost:63342')) {
                // PyCharm内置服务器
                const baseUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/'));
                reportUrl = baseUrl + '/audit_viewer.html';
                console.log('🔧 Using PyCharm server URL');
            } else if (currentUrl.includes('localhost:8000')) {
                // 我们的Web服务器 (端口8000)
                reportUrl = 'http://localhost:8000/frontend/audit_viewer.html';
                console.log('🔧 Using our web server URL (port 8000)');
            } else if (currentUrl.includes('localhost:8001')) {
                // 我们的Web服务器 (端口8001)
                reportUrl = 'http://localhost:8001/frontend/audit_viewer.html';
                console.log('🔧 Using our web server URL (port 8001)');
            } else if (currentUrl.startsWith('file://')) {
                // 文件协议访问 - 重定向到HTTP服务器，优先尝试8001端口
                reportUrl = 'http://localhost:8001/frontend/audit_viewer.html';
                console.log('🔧 File protocol detected, redirecting to HTTP server (port 8001)');
            } else {
                // 其他情况，使用相对路径
                reportUrl = 'audit_viewer.html';
                console.log('🔧 Using relative path');
            }

            // 如果有单据编号，添加到URL参数中
            if (documentNumber) {
                const separator = reportUrl.includes('?') ? '&' : '?';
                reportUrl += `${separator}doc=${documentNumber}`;
                console.log('✅ Added document number to report URL:', documentNumber);
            } else {
                console.log('⚠️ No document number to add to URL');
            }

            reportLink.href = reportUrl;
            document.getElementById('generated-link-display').textContent = reportUrl;
            console.log('✅ Report link set to:', reportUrl);
            
            // 验证链接是否正确设置
            console.log('🔍 Final link href:', reportLink.href);
        }
        
        function simulateAuditComplete() {
            console.log('🎯 模拟审核完成...');
            document.getElementById('result-link-container').style.display = 'block';
            setupReportLink();
        }
        
        function testLinkGeneration() {
            console.log('🧪 测试链接生成...');
            setupReportLink();
        }
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        // 页面加载时执行
        window.onload = function() {
            console.log('🚀 调试页面加载完成');
            updateURLInfo();
        };
    </script>
</body>
</html>

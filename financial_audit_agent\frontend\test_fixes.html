<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1f3a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2f4a;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0088ff;
        }
        #thinking-content {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #444;
            padding: 15px;
            background: #1e2340;
            border-radius: 8px;
            margin: 10px 0;
        }
        .markdown-section {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.05);
        }
        .markdown-section.new-content {
            border-left: 3px solid #00d4ff;
            padding-left: 15px;
            margin-left: 10px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 0 8px 8px 0;
            animation: newContentGlow 2s ease-out;
        }
        @keyframes newContentGlow {
            0% {
                background: rgba(0, 212, 255, 0.3);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            }
            100% {
                background: rgba(0, 212, 255, 0.1);
                box-shadow: none;
            }
        }
        .rule-status {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            background: rgba(0, 255, 136, 0.1);
        }
    </style>
</head>
<body>
    <h1>AI控制台修复功能测试</h1>
    
    <div class="test-section">
        <h2>1. AI思考过程增量更新测试</h2>
        <button onclick="testIncrementalUpdate()">测试增量更新</button>
        <button onclick="testFullUpdate()">测试完整更新</button>
        <button onclick="clearThinking()">清空内容</button>
        
        <div id="thinking-content">
            <div class="thinking-step">
                <div class="step-text">等待测试...</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. 规则状态解析测试</h2>
        <button onclick="testRuleStatusParsing()">测试规则状态解析</button>
        <button onclick="testConsoleLogParsing()">测试控制台日志解析</button>
        
        <div id="rule-status-results">
            <div class="rule-status">等待测试...</div>
        </div>
    </div>

    <script>
        // 模拟AI思考过程更新的核心逻辑
        let lastThinkingText = '';
        
        function updateAIThinking(thinkingText) {
            const thinkingContent = document.getElementById('thinking-content');
            if (!thinkingContent) return;

            console.log('🤖 更新AI思考过程:', thinkingText.substring(0, 100) + '...');

            // 检查是否是新的思考过程（避免重复更新）
            if (lastThinkingText === thinkingText) {
                console.log('🤖 思考过程未变化，跳过更新');
                return;
            }

            // 检查是否是增量更新（新内容包含旧内容）
            const isIncremental = lastThinkingText && thinkingText.includes(lastThinkingText);
            
            if (isIncremental) {
                // 增量更新：只添加新的内容部分
                const newContent = thinkingText.substring(lastThinkingText.length);
                console.log('🤖 执行增量更新，新增内容长度:', newContent.length);
                appendNewThinkingContent(newContent, thinkingContent);
            } else {
                // 全新内容：完全替换
                console.log('🤖 执行完整更新');
                renderFullThinkingContent(thinkingText, thinkingContent);
            }

            lastThinkingText = thinkingText;
        }
        
        function appendNewThinkingContent(newContent, container) {
            if (!newContent.trim()) return;
            
            const newElement = document.createElement('div');
            newElement.className = 'markdown-section new-content';
            newElement.style.opacity = '0';
            newElement.style.transform = 'translateY(20px)';
            newElement.innerHTML = `<pre>${newContent}</pre>`;
            
            container.appendChild(newElement);
            
            // 添加淡入动画
            setTimeout(() => {
                newElement.style.transition = 'all 0.5s ease-out';
                newElement.style.opacity = '1';
                newElement.style.transform = 'translateY(0)';
            }, 100);
            
            // 滚动到新内容
            setTimeout(() => {
                container.scrollTop = container.scrollHeight;
            }, 200);
        }
        
        function renderFullThinkingContent(thinkingText, container) {
            container.innerHTML = '';
            const element = document.createElement('div');
            element.className = 'markdown-section';
            element.innerHTML = `<pre>${thinkingText}</pre>`;
            container.appendChild(element);
        }
        
        // 测试函数
        function testIncrementalUpdate() {
            const baseText = "## 🔍 附件完整性检查 (阶段 1/4)\n\n正在检查附件完整性...";
            updateAIThinking(baseText);
            
            setTimeout(() => {
                const extendedText = baseText + "\n\n### 检查结果\n\n- 发票：✅ 已找到\n- 审批表：✅ 已找到";
                updateAIThinking(extendedText);
            }, 2000);
            
            setTimeout(() => {
                const finalText = extendedText + "\n- 小票：✅ 已找到\n- 支付记录：✅ 已找到\n\n✅ 附件完整性检查完成";
                updateAIThinking(finalText);
            }, 4000);
        }
        
        function testFullUpdate() {
            const newText = "## 🔍 字段内容与一致性检查 (阶段 2/4)\n\n正在进行字段一致性检查...\n\n### 检查项目\n- 金额一致性\n- 日期一致性\n- 人员信息一致性";
            updateAIThinking(newText);
        }
        
        function clearThinking() {
            document.getElementById('thinking-content').innerHTML = '<div class="thinking-step"><div class="step-text">等待测试...</div></div>';
            lastThinkingText = '';
        }
        
        function testRuleStatusParsing() {
            const testText = `
            规则1：状态：通过
            规则2：状态应为"通过"
            规则3: 通过
            📋 模式6匹配到规则21：通过
            📋 模式6匹配到规则22：通过
            📋 模式7匹配到规则32：通过
            `;
            
            const results = parseRuleStatus(testText);
            displayRuleResults(results);
        }
        
        function testConsoleLogParsing() {
            const testText = `
            📋 模式6匹配到规则21：通过
            📋 模式6匹配到规则22：通过
            📋 模式6匹配到规则23：通过
            📋 模式6匹配到规则24：通过
            ✅ 模式6共匹配19条规则
            📋 模式7匹配到规则32：通过
            📋 模式7匹配到规则33：通过
            📋 模式7匹配到规则35：通过
            ✅ 模式7共匹配5条规则
            `;
            
            const results = parseRuleStatus(testText);
            displayRuleResults(results);
        }
        
        function parseRuleStatus(text) {
            const completedRules = new Set();
            
            // 控制台日志格式解析
            const consoleLogPattern = /📋\s*模式\d+匹配到规则(\d+)：(通过|不通过|警告|无法判断)/gi;
            let match;
            
            while ((match = consoleLogPattern.exec(text)) !== null) {
                const ruleNumber = parseInt(match[1]);
                const status = match[2];
                
                if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
                    completedRules.add(ruleNumber);
                    console.log(`📋 解析到规则${ruleNumber}：${status}`);
                }
            }
            
            return Array.from(completedRules).sort((a, b) => a - b);
        }
        
        function displayRuleResults(rules) {
            const container = document.getElementById('rule-status-results');
            container.innerHTML = '';
            
            if (rules.length === 0) {
                container.innerHTML = '<div class="rule-status">未解析到任何规则</div>';
                return;
            }
            
            const resultDiv = document.createElement('div');
            resultDiv.className = 'rule-status';
            resultDiv.innerHTML = `
                <strong>解析结果：</strong><br>
                共解析到 ${rules.length} 条规则：${rules.join(', ')}<br>
                <small>规则范围：1-38</small>
            `;
            container.appendChild(resultDiv);
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI思维链集成测试</title>
    <link rel="stylesheet" href="frontend/ai_results.css">
    <style>
        body {
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f3a 100%);
            color: #e2e8f0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(26, 31, 58, 0.4);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4ff;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .test-header p {
            color: #94a3b8;
            font-size: 1.1rem;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #8b5cf6, #00d4ff);
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.5);
        }
        
        .status-indicator {
            padding: 10px 20px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
            border: 1px solid #00ff88;
        }
        
        .status-error {
            background: rgba(255, 107, 53, 0.2);
            color: #ff6b35;
            border: 1px solid #ff6b35;
        }
        
        .status-info {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            border: 1px solid #00d4ff;
        }
        
        .test-results {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧠 AI思维链集成测试</h1>
            <p>测试AI思考过程在审核结果页面中的展示功能</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="loadTestData()">加载测试数据</button>
            <button class="test-btn" onclick="testThinkingChain()">测试思维链功能</button>
            <button class="test-btn" onclick="testSearchFunction()">测试搜索功能</button>
            <button class="test-btn" onclick="clearTest()">清除测试</button>
        </div>
        
        <div id="test-status"></div>
        
        <div class="test-results">
            <!-- AI分析洞察 -->
            <section class="ai-insights" id="ai-insights">
                <div class="insights-header">
                    <h3>🧠 AI智能洞察</h3>
                    <div class="neural-indicator">
                        <div class="neural-dot"></div>
                        <span>神经网络分析</span>
                    </div>
                    <button class="thinking-chain-btn" id="thinking-chain-btn" style="display: none;">
                        <span class="btn-icon">🔍</span>
                        <span class="btn-text">查看AI思考过程</span>
                        <div class="btn-glow"></div>
                    </button>
                </div>
                <div class="insights-content" id="insights-content">
                    <div class="insight-loading">
                        <div class="loading-brain">
                            <div class="brain-wave"></div>
                            <div class="brain-wave"></div>
                            <div class="brain-wave"></div>
                        </div>
                        <p>点击"加载测试数据"开始测试...</p>
                    </div>
                </div>
            </section>

            <!-- AI思考过程展示区域 -->
            <section class="ai-thinking-chain" id="ai-thinking-chain" style="display: none;">
                <div class="thinking-header">
                    <h3>🤖 AI思考过程</h3>
                    <div class="thinking-controls">
                        <button class="control-btn" id="expand-all-btn">
                            <span class="btn-icon">📖</span>
                            <span class="btn-text">展开全部</span>
                        </button>
                        <button class="control-btn" id="collapse-all-btn">
                            <span class="btn-icon">📚</span>
                            <span class="btn-text">收起全部</span>
                        </button>
                        <button class="control-btn" id="copy-thinking-btn">
                            <span class="btn-icon">📋</span>
                            <span class="btn-text">复制内容</span>
                        </button>
                        <button class="control-btn" id="close-thinking-btn">
                            <span class="btn-icon">✖️</span>
                            <span class="btn-text">关闭</span>
                        </button>
                    </div>
                </div>
                
                <div class="thinking-search">
                    <div class="search-container">
                        <input type="text" id="thinking-search-input" placeholder="搜索AI思考内容..." class="search-input">
                        <button class="search-btn" id="thinking-search-btn">🔍</button>
                    </div>
                    <div class="search-results" id="search-results"></div>
                </div>
                
                <div class="thinking-content" id="thinking-content">
                    <div class="thinking-loading">
                        <div class="loading-spinner"></div>
                        <p>正在加载AI思考过程...</p>
                    </div>
                </div>
                
                <div class="thinking-metadata" id="thinking-metadata" style="display: none;">
                    <div class="metadata-title">📊 思维链元数据</div>
                    <div class="metadata-content" id="metadata-content"></div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // 测试用的AI思维链数据
        const testAuditData = {
            ai_thinking_chain: {
                combined_thinking: "系统就绪，等待开始AI审核分析...",
                phases_history: {
                    "finished": {
                        "phase_name": "附件完整性检查",
                        "ai_thinking": "## 🔍 附件完整性检查 (阶段 1/4)\n\n我正在开始对文档 ZDBXD2025042900003 进行附件完整性检查。\n\n### 📋 检查清单\n1. 发票文件检查\n2. 合同文件检查\n3. 付款凭证检查\n4. 其他必要附件检查\n\n### 🔍 详细分析过程\n\n**步骤1: 发票文件检查**\n- 检查是否存在发票文件\n- 验证发票格式和内容\n- 确认发票金额与申请金额一致\n\n**步骤2: 合同文件检查**\n- 验证合同文件完整性\n- 检查合同条款是否清晰\n- 确认合同签署状态\n\n**步骤3: 付款凭证检查**\n- 检查付款凭证是否完整\n- 验证付款方式和金额\n- 确认付款时间合理性\n\n### 📊 检查结果\n经过详细检查，发现以下问题：\n1. 缺少发票文件\n2. 合同文件不完整\n3. 付款凭证格式不规范\n\n### 🎯 结论\n附件完整性检查未通过，需要补充相关文件。",
                        "status": "completed",
                        "timestamp": "2025-07-28T14:13:50Z",
                        "message": "附件完整性检查完成",
                        "detail": "发现多个问题需要处理"
                    }
                },
                extraction_metadata: {
                    extracted_at: "2025-07-28 14:30:00",
                    audit_id: "ZDBXD2025042900003",
                    audit_status: "completed",
                    completion_time: "2025-07-28T14:13:50Z",
                    integration_version: "1.0"
                }
            }
        };

        // 模拟AI结果查看器类的部分功能
        class TestAIResultsViewer {
            constructor() {
                this.auditData = null;
                this.setupEventListeners();
            }

            setupEventListeners() {
                // AI思维链相关事件监听器
                document.getElementById('thinking-chain-btn').addEventListener('click', () => {
                    this.showThinkingChain();
                });
                
                document.getElementById('close-thinking-btn').addEventListener('click', () => {
                    this.hideThinkingChain();
                });
                
                document.getElementById('expand-all-btn').addEventListener('click', () => {
                    this.expandAllPhases();
                });
                
                document.getElementById('collapse-all-btn').addEventListener('click', () => {
                    this.collapseAllPhases();
                });
                
                document.getElementById('copy-thinking-btn').addEventListener('click', () => {
                    this.copyThinkingContent();
                });
                
                document.getElementById('thinking-search-btn').addEventListener('click', () => {
                    this.searchThinkingContent();
                });
                
                document.getElementById('thinking-search-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchThinkingContent();
                    }
                });
                
                document.getElementById('thinking-search-input').addEventListener('input', (e) => {
                    if (e.target.value.length > 2) {
                        this.searchThinkingContent();
                    } else {
                        this.clearSearchResults();
                    }
                });
            }
        }

        // 全局测试实例
        let testViewer = null;

        // 测试函数
        function loadTestData() {
            showStatus('正在加载测试数据...', 'info');
            
            setTimeout(() => {
                testViewer = new TestAIResultsViewer();
                testViewer.auditData = testAuditData;
                
                // 显示AI思维链按钮
                const thinkingChainBtn = document.getElementById('thinking-chain-btn');
                thinkingChainBtn.style.display = 'flex';
                thinkingChainBtn.style.animation = 'fadeIn 0.5s ease-out';
                
                // 更新洞察内容
                document.getElementById('insights-content').innerHTML = `
                    <div style="padding: 20px; text-align: center;">
                        <h4 style="color: #00d4ff; margin-bottom: 15px;">✅ 测试数据加载完成</h4>
                        <p style="color: #94a3b8;">AI思维链数据已准备就绪，点击"查看AI思考过程"按钮开始测试</p>
                        <div style="margin-top: 15px; padding: 10px; background: rgba(0, 212, 255, 0.1); border-radius: 8px;">
                            <strong>包含数据：</strong><br>
                            • 组合思维链: ${testAuditData.ai_thinking_chain.combined_thinking.length} 字符<br>
                            • 阶段历史: ${Object.keys(testAuditData.ai_thinking_chain.phases_history).length} 个阶段<br>
                            • 元数据: 完整
                        </div>
                    </div>
                `;
                
                showStatus('✅ 测试数据加载成功！AI思维链功能已就绪。', 'success');
            }, 1000);
        }

        function testThinkingChain() {
            if (!testViewer || !testViewer.auditData) {
                showStatus('❌ 请先加载测试数据', 'error');
                return;
            }
            
            showStatus('正在测试AI思维链功能...', 'info');
            testViewer.showThinkingChain();
            
            setTimeout(() => {
                showStatus('✅ AI思维链功能测试完成！请查看下方展示的思考过程。', 'success');
            }, 1500);
        }

        function testSearchFunction() {
            if (!testViewer || !testViewer.auditData) {
                showStatus('❌ 请先加载测试数据并打开思维链', 'error');
                return;
            }
            
            const searchInput = document.getElementById('thinking-search-input');
            searchInput.value = '检查';
            testViewer.searchThinkingContent();
            
            showStatus('✅ 搜索功能测试完成！已搜索关键词"检查"。', 'success');
        }

        function clearTest() {
            document.getElementById('thinking-chain-btn').style.display = 'none';
            document.getElementById('ai-thinking-chain').style.display = 'none';
            document.getElementById('insights-content').innerHTML = `
                <div class="insight-loading">
                    <div class="loading-brain">
                        <div class="brain-wave"></div>
                        <div class="brain-wave"></div>
                        <div class="brain-wave"></div>
                    </div>
                    <p>点击"加载测试数据"开始测试...</p>
                </div>
            `;
            
            testViewer = null;
            showStatus('🔄 测试环境已重置', 'info');
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('test-status');
            statusDiv.innerHTML = `<div class="status-indicator status-${type}">${message}</div>`;
            
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }
    </script>
    
    <!-- 引入原始的AI结果查看器功能 -->
    <script src="frontend/ai_results.js"></script>
</body>
</html>

/* AI控制台科技感样式 */
:root {
    /* 科技感配色方案 */
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f3a;
    --bg-tertiary: #2a2f4a;
    --accent-blue: #00d4ff;
    --accent-green: #00ff88;
    --accent-purple: #8b5cf6;
    --accent-orange: #ff6b35;
    --accent-red: #ff3366;
    --text-primary: #ffffff;
    --text-secondary: #a0a9c0;
    --text-muted: #6b7280;
    
    /* 发光效果 */
    --glow-blue: 0 0 20px rgba(0, 212, 255, 0.5);
    --glow-green: 0 0 20px rgba(0, 255, 136, 0.5);
    --glow-purple: 0 0 20px rgba(139, 92, 246, 0.5);
    --glow-orange: 0 0 20px rgba(255, 107, 53, 0.5);
    
    /* 字体 */
    --font-primary: 'Raj<PERSON>ni', sans-serif;
    --font-mono: 'Orbitron', monospace;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
}

/* 动态粒子背景 */
#particles-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #0a0e1a 0%, #1a1f3a 50%, #2a2f4a 100%);
}

#particles-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
    animation: backgroundPulse 8s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

/* 主控制台容器 */
.console-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding: 20px;
    gap: 20px;
}

/* 顶部状态栏 */
.console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(26, 31, 58, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 15px;
    padding: 20px 30px;
    box-shadow: var(--glow-blue);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.ai-logo {
    position: relative;
    width: 50px;
    height: 50px;
}

.logo-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--accent-blue);
    border-radius: 50%;
    box-shadow: var(--glow-blue);
    animation: corePulse 2s ease-in-out infinite;
}

.logo-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 2px solid var(--accent-blue);
    border-radius: 50%;
    border-top-color: transparent;
    animation: ringRotate 3s linear infinite;
}

.logo-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 50%;
    animation: pulseBorder 2s ease-in-out infinite;
}

@keyframes corePulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes ringRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes pulseBorder {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.3); opacity: 0.3; }
}

.system-title h1 {
    font-family: var(--font-mono);
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.version {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 300;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 30px;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: statusPulse 2s ease-in-out infinite;
}

.status-indicator.online {
    background: var(--accent-green);
    box-shadow: var(--glow-green);
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.current-time {
    font-family: var(--font-mono);
    font-size: 1.1rem;
    color: var(--accent-blue);
    text-shadow: var(--glow-blue);
}

/* 主要内容区域 */
.console-main {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr;
    gap: 20px;
    flex: 1;
}

/* 通用section样式 */
.console-main section {
    background: rgba(26, 31, 58, 0.6);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h2 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* AI大脑可视化区域 */
.ai-brain-section {
    border-left: 3px solid var(--accent-purple);
    box-shadow: var(--glow-purple);
    animation: brainPulse 3s ease-in-out infinite;
}

@keyframes brainPulse {
    0%, 100% {
        box-shadow: var(--glow-purple);
        border-left-color: var(--accent-purple);
    }
    50% {
        box-shadow: 0 0 20px rgba(138, 43, 226, 0.6);
        border-left-color: #9932cc;
    }
}

.neural-activity {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.activity-dot {
    width: 8px;
    height: 8px;
    background: var(--accent-purple);
    border-radius: 50%;
    animation: activityPulse 1.5s ease-in-out infinite;
}

@keyframes activityPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.3); }
}

.ai-thinking-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.thinking-display {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 20px;
    min-height: 200px;
}

.thinking-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.thinking-icon {
    font-size: 1.5rem;
    animation: thinkingBounce 2s ease-in-out infinite;
}

@keyframes thinkingBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.thinking-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--accent-purple);
}

.thinking-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    /* 高度限制和滚动设置 */
    max-height: 75vh; /* 视窗高度的75% */
    overflow-y: auto;
    overflow-x: hidden;
    /* 滚动行为优化 */
    scroll-behavior: smooth;
    /* 触摸设备滚动优化 */
    -webkit-overflow-scrolling: touch;
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: var(--accent-blue) rgba(255, 255, 255, 0.1);
}

/* Webkit浏览器自定义滚动条 */
.thinking-content::-webkit-scrollbar {
    width: 8px;
}

.thinking-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.thinking-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--accent-blue), var(--accent-purple));
    border-radius: 4px;
    transition: background 0.3s ease;
}

.thinking-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00d4ff, #8a2be2);
}

.thinking-content::-webkit-scrollbar-corner {
    background: transparent;
}

/* 滚动指示器 */
.scroll-indicator {
    position: absolute;
    bottom: 10px;
    right: 20px;
    background: rgba(0, 212, 255, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10;
}

.scroll-indicator.visible {
    opacity: 1;
}

.scroll-indicator::before {
    content: "↓ 更多内容";
}

/* 思考过程容器的相对定位 */
.thinking-section {
    position: relative;
}

/* Markdown渲染样式 */
.markdown-content {
    line-height: 1.6;
    color: var(--text-primary);
}

/* 新增内容的动画效果 */
.markdown-section.new-content {
    border-left: 3px solid var(--accent-blue);
    padding-left: 15px;
    margin-left: 10px;
    background: rgba(0, 212, 255, 0.05);
    border-radius: 0 8px 8px 0;
    animation: newContentGlow 2s ease-out;
}

@keyframes newContentGlow {
    0% {
        background: rgba(0, 212, 255, 0.2);
        border-left-color: var(--accent-blue);
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    }
    100% {
        background: rgba(0, 212, 255, 0.05);
        border-left-color: var(--accent-blue);
        box-shadow: none;
    }
}

.markdown-section {
    margin-bottom: 1rem;
    transition: all 0.5s ease-out;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4 {
    color: var(--accent-blue);
    margin: 1.5rem 0 1rem 0;
    font-weight: 600;
}

.markdown-content h1 {
    font-size: 1.8rem;
    border-bottom: 2px solid var(--accent-blue);
    padding-bottom: 0.5rem;
}

.markdown-content h2 {
    font-size: 1.5rem;
    color: var(--accent-green);
}

.markdown-content h3 {
    font-size: 1.3rem;
    color: var(--accent-purple);
}

.markdown-content p {
    margin: 0.8rem 0;
    text-align: justify;
}

.markdown-content ul,
.markdown-content ol {
    margin: 1rem 0;
    padding-left: 2rem;
}

.markdown-content li {
    margin: 0.5rem 0;
    line-height: 1.5;
}

.markdown-content strong {
    color: var(--accent-yellow);
    font-weight: 600;
}

.markdown-content em {
    color: var(--accent-purple);
    font-style: italic;
}

.markdown-content code {
    background: rgba(0, 212, 255, 0.1);
    color: var(--accent-blue);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.markdown-content pre {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    overflow-x: auto;
    margin: 1rem 0;
}

.markdown-content pre code {
    background: none;
    padding: 0;
    color: var(--text-primary);
}

.markdown-content blockquote {
    border-left: 4px solid var(--accent-blue);
    margin: 1rem 0;
    padding: 0.5rem 1rem;
    background: rgba(0, 212, 255, 0.05);
    font-style: italic;
}

.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    text-align: left;
}

.markdown-content th {
    background: rgba(0, 212, 255, 0.1);
    color: var(--accent-blue);
    font-weight: 600;
}

/* 响应式设计 - 移动设备优化 */
@media (max-width: 768px) {
    .thinking-content {
        max-height: 70vh; /* 移动设备上稍微降低高度 */
        gap: 8px;
        /* 移动设备滚动优化 */
        overscroll-behavior: contain;
        scroll-padding: 20px;
    }

    .thinking-content::-webkit-scrollbar {
        width: 6px; /* 移动设备上更细的滚动条 */
    }

    .markdown-content {
        font-size: 0.9rem; /* 移动设备上稍小的字体 */
        line-height: 1.5;
    }

    .markdown-content h1 {
        font-size: 1.5rem;
    }

    .markdown-content h2 {
        font-size: 1.3rem;
    }

    .markdown-content h3 {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .thinking-content {
        max-height: 65vh; /* 小屏设备进一步降低高度 */
        gap: 6px;
    }

    .markdown-content {
        font-size: 0.85rem;
    }

    .markdown-content ul,
    .markdown-content ol {
        padding-left: 1.5rem; /* 减少缩进 */
    }
}

/* 高分辨率屏幕优化 */
@media (min-height: 1080px) {
    .thinking-content {
        max-height: 80vh; /* 高分辨率屏幕可以显示更多内容 */
    }
}

.thinking-step {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    opacity: 0;
    animation: stepFadeIn 0.5s ease-in-out forwards;
}

.step-indicator {
    width: 8px;
    height: 8px;
    background: var(--accent-purple);
    border-radius: 50%;
    animation: stepPulse 1s ease-in-out infinite;
}

@keyframes stepFadeIn {
    0% { opacity: 0; transform: translateX(-20px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes stepPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.step-text {
    font-size: 0.95rem;
    color: var(--text-secondary);
}

/* 神经网络可视化 */
.neural-network {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 120px;
    background: rgba(138, 43, 226, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(138, 43, 226, 0.3);
    padding: 20px;
    animation: networkGlow 4s ease-in-out infinite;
    overflow: hidden;
}

@keyframes networkGlow {
    0%, 100% {
        background: rgba(138, 43, 226, 0.1);
        border-color: rgba(138, 43, 226, 0.3);
    }
    50% {
        background: rgba(138, 43, 226, 0.2);
        border-color: rgba(138, 43, 226, 0.5);
    }
}

.neural-network::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.3), transparent);
    animation: neuralScan 3s infinite;
    z-index: 1;
}

@keyframes neuralScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.network-layer {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.neuron {
    width: 16px;
    height: 16px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    animation: neuronIdle 4s ease-in-out infinite;
}

@keyframes neuronIdle {
    0%, 100% {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1);
    }
    50% {
        background: rgba(255, 255, 255, 0.5);
        transform: scale(1.05);
    }
}

.neuron.active {
    background: var(--accent-blue);
    box-shadow: var(--glow-blue);
    animation: neuronActive 1.5s ease-in-out infinite;
}

@keyframes neuronActive {
    0%, 100% {
        transform: scale(1);
        box-shadow: var(--glow-blue);
    }
    25% {
        transform: scale(1.3);
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
    }
    75% {
        transform: scale(1.2);
        box-shadow: 0 0 25px rgba(0, 212, 255, 0.9);
    }
}

.neuron::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.3), transparent);
    animation: neuronRing 3s linear infinite;
    opacity: 0;
}

.neuron.active::before {
    opacity: 1;
}

@keyframes neuronRing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.network-connections {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* 规则执行区域 */
.rules-execution-section {
    border-left: 3px solid var(--accent-blue);
    box-shadow: var(--glow-blue);
    animation: rulesSectionPulse 3s ease-in-out infinite;
}

@keyframes rulesSectionPulse {
    0%, 100% {
        box-shadow: var(--glow-blue);
        border-left-color: var(--accent-blue);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
        border-left-color: #00d4ff;
    }
}

.execution-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-value {
    font-family: var(--font-mono);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-blue);
    text-shadow: var(--glow-blue);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.rules-layers {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 25px;
}

.rule-layer {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: layerIdle 4s ease-in-out infinite;
}

@keyframes layerIdle {
    0%, 100% {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.1);
    }
    50% {
        background: rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.2);
    }
}

.rule-layer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: layerScan 4s infinite;
}

@keyframes layerScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.rule-layer.active {
    border-color: var(--accent-blue);
    box-shadow: var(--glow-blue);
    background: rgba(0, 212, 255, 0.1);
    animation: layerActive 2s ease-in-out infinite;
}

@keyframes layerActive {
    0%, 100% {
        box-shadow: var(--glow-blue);
        border-color: var(--accent-blue);
        background: rgba(0, 212, 255, 0.1);
    }
    50% {
        box-shadow: 0 0 25px rgba(0, 212, 255, 0.8);
        border-color: #00d4ff;
        background: rgba(0, 212, 255, 0.2);
    }
}

.rule-layer.active::before {
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.3), transparent);
    animation: layerActiveScan 1.5s infinite;
}

@keyframes layerActiveScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.rule-layer.completed {
    border-color: var(--accent-green);
    box-shadow: var(--glow-green);
    background: rgba(0, 255, 136, 0.1);
    animation: layerCompleted 3s ease-in-out infinite;
}

@keyframes layerCompleted {
    0%, 100% {
        box-shadow: var(--glow-green);
        border-color: var(--accent-green);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
        border-color: #00ff88;
    }
}

.rule-layer.completed::before {
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.3), transparent);
    animation: layerCompletedScan 3s infinite;
}

@keyframes layerCompletedScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.layer-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.layer-icon {
    font-size: 1.5rem;
}

.layer-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.layer-desc {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.layer-status {
    margin-left: auto;
    padding: 4px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.layer-progress {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
    border-radius: 4px;
    width: 0%;
    transition: width 0.5s ease;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.progress-text {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--text-secondary);
    min-width: 40px;
}

/* 当前规则显示 */
.current-rule-display {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.rule-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.rule-indicator {
    width: 12px;
    height: 12px;
    background: var(--accent-blue);
    border-radius: 50%;
    animation: ruleIndicatorPulse 1.5s ease-in-out infinite;
}

@keyframes ruleIndicatorPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.3); }
}

.rule-title {
    font-weight: 600;
    color: var(--accent-blue);
}

.rule-id {
    font-family: var(--font-mono);
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.rule-description {
    font-size: 0.95rem;
    color: var(--text-secondary);
    margin-bottom: 15px;
    line-height: 1.4;
}

.rule-progress-bar {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.rule-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-green));
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
}

/* 数据流监控区域 */
.data-stream-section {
    border-left: 3px solid var(--accent-green);
    box-shadow: var(--glow-green);
}

.stream-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.stream-indicator {
    width: 8px;
    height: 8px;
    background: var(--accent-green);
    border-radius: 50%;
    animation: streamPulse 1s ease-in-out infinite;
}

@keyframes streamPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.data-stream-container {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
    height: 300px;
    overflow-y: auto;
    margin-bottom: 25px;
}

.stream-display {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stream-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-left: 3px solid var(--accent-green);
    border-radius: 6px;
    font-size: 0.9rem;
    opacity: 0;
    animation: streamItemFadeIn 0.5s ease-in-out forwards;
}

@keyframes streamItemFadeIn {
    0% { opacity: 0; transform: translateX(20px); }
    100% { opacity: 1; transform: translateX(0); }
}

.stream-timestamp {
    font-family: var(--font-mono);
    font-size: 0.8rem;
    color: var(--accent-green);
    min-width: 60px;
}

.stream-content {
    color: var(--text-secondary);
    flex: 1;
}

.stream-status-badge {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
}

.stream-status-badge.success {
    background: rgba(0, 255, 136, 0.2);
    color: var(--accent-green);
}

.stream-status-badge.warning {
    background: rgba(255, 107, 53, 0.2);
    color: var(--accent-orange);
}

.stream-status-badge.info {
    background: rgba(0, 212, 255, 0.2);
    color: var(--accent-blue);
}

/* 审核统计监控 */
.audit-statistics h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
    animation: titlePulse 2s ease-in-out infinite;
}

@keyframes titlePulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.statistics-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.metric {
    display: flex;
    align-items: center;
    gap: 12px;
    transition: transform 0.3s ease;
}

.metric:hover {
    transform: translateX(5px);
}

.metric-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    min-width: 80px;
    animation: labelGlow 3s ease-in-out infinite;
}

@keyframes labelGlow {
    0%, 100% { text-shadow: none; }
    50% { text-shadow: 0 0 5px rgba(0, 212, 255, 0.5); }
}

.metric-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.metric-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.metric-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.metric-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: flowingLight 2s infinite;
}

@keyframes flowingLight {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.metric-fill.success {
    background: linear-gradient(90deg, var(--accent-green), #00ff88);
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0%, 100% { box-shadow: 0 0 8px rgba(0, 255, 136, 0.3); }
    50% { box-shadow: 0 0 15px rgba(0, 255, 136, 0.6); }
}

.metric-fill.warning {
    background: linear-gradient(90deg, #ffc107, #ff9800);
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.3);
    animation: warningPulse 1.5s ease-in-out infinite;
}

@keyframes warningPulse {
    0%, 100% { box-shadow: 0 0 8px rgba(255, 193, 7, 0.3); }
    50% { box-shadow: 0 0 15px rgba(255, 193, 7, 0.6); }
}

.metric-fill.info {
    background: linear-gradient(90deg, var(--accent-blue), #00d4ff);
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
    animation: infoPulse 2.5s ease-in-out infinite;
}

@keyframes infoPulse {
    0%, 100% { box-shadow: 0 0 8px rgba(0, 212, 255, 0.3); }
    50% { box-shadow: 0 0 15px rgba(0, 212, 255, 0.6); }
}

.metric-value {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--accent-green);
    min-width: 50px;
    text-align: right;
    animation: valueFlicker 1s ease-in-out infinite;
}

@keyframes valueFlicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 风险提醒 */
.risk-alerts h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
    animation: alertTitleBlink 2s ease-in-out infinite;
}

@keyframes alertTitleBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.alerts-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.alert-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: slideInAlert 0.5s ease-out;
    position: relative;
    overflow: hidden;
}

@keyframes slideInAlert {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.alert-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: alertScan 3s infinite;
    pointer-events: none;
}

@keyframes alertScan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.alert-item.info {
    border-left-color: var(--accent-blue);
    background: rgba(0, 212, 255, 0.1);
    animation: slideInAlert 0.5s ease-out, infoBorder 2s ease-in-out infinite;
}

@keyframes infoBorder {
    0%, 100% { border-left-color: var(--accent-blue); }
    50% { border-left-color: #00d4ff; }
}

.alert-item.warning {
    border-left-color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
    animation: slideInAlert 0.5s ease-out, warningBorder 1.5s ease-in-out infinite;
}

@keyframes warningBorder {
    0%, 100% { border-left-color: #ffc107; }
    50% { border-left-color: #ff9800; }
}

.alert-item.error {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    animation: slideInAlert 0.5s ease-out, errorBorder 1s ease-in-out infinite;
}

@keyframes errorBorder {
    0%, 100% { border-left-color: #dc3545; }
    50% { border-left-color: #ff4757; }
}

.alert-item.success {
    border-left-color: var(--accent-green);
    background: rgba(0, 255, 136, 0.1);
    animation: slideInAlert 0.5s ease-out, successBorder 2s ease-in-out infinite;
}

@keyframes successBorder {
    0%, 100% { border-left-color: var(--accent-green); }
    50% { border-left-color: #00ff88; }
}

.alert-icon {
    font-size: 1.2rem;
    margin-top: 2px;
    animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    animation: titleGlow 2s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { text-shadow: none; }
    50% { text-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
}

.alert-desc {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
    animation: descFade 3s ease-in-out infinite;
}

@keyframes descFade {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 底部控制面板 */
.console-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(26, 31, 58, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px 30px;
}

.control-panel {
    display: flex;
    gap: 15px;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-family: var(--font-primary);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn.primary {
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
    color: var(--text-primary);
    box-shadow: var(--glow-blue);
}

.control-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
}

.control-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-icon {
    font-size: 1.2rem;
}

.system-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* 结果展示模态框 */
.results-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    padding: 30px;
    max-width: 80%;
    max-height: 80%;
    overflow-y: auto;
    box-shadow: var(--glow-blue);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: var(--accent-red);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .console-main {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .console-container {
        padding: 15px;
    }

    .console-header,
    .console-footer {
        padding: 15px 20px;
    }
}

@media (max-width: 768px) {
    .console-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-right {
        flex-direction: column;
        gap: 10px;
    }

    .control-panel {
        flex-direction: column;
        width: 100%;
    }

    .control-btn {
        justify-content: center;
    }

    .console-footer {
        flex-direction: column;
        gap: 15px;
    }

    .system-info {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-blue);
    border-radius: 4px;
    box-shadow: var(--glow-blue);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-purple);
}

/* 数据流监控增强样式 */
.stream-item {
    position: relative;
    background: rgba(0, 255, 136, 0.1);
    border-left: 3px solid #00ff88;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.stream-item:hover {
    background: rgba(0, 255, 136, 0.2);
    transform: translateX(5px);
}

.stream-timestamp {
    font-size: 0.8em;
    color: #888;
    margin-bottom: 5px;
}

.stream-content {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.stream-step {
    font-weight: bold;
    color: #00ff88;
}

.stream-message {
    color: #fff;
}

.stream-detail {
    font-size: 0.9em;
    color: #ccc;
    font-style: italic;
}

.stream-status {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2em;
}

/* 完成通知样式 */
.completion-notification {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification-icon {
    font-size: 2em;
}

.notification-text h3 {
    margin: 0 0 5px 0;
    font-size: 1.2em;
}

.notification-text p {
    margin: 3px 0;
    font-size: 0.9em;
    opacity: 0.9;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0;
    margin-left: auto;
}

.notification-close:hover {
    opacity: 0.7;
}

/* AI思考过程优化样式 */
.thinking-steps-container {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 0;
}

.thinking-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: rgba(0, 255, 136, 0.05);
    border-radius: 8px;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.thinking-step:hover {
    background: rgba(0, 255, 136, 0.1);
    border-left-color: #00ff88;
}

.thinking-step .step-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
    margin-right: 12px;
    margin-top: 6px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.thinking-step .step-indicator.active {
    background: #00ff88;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    animation: pulse 2s infinite;
}

.thinking-step .step-text {
    flex: 1;
    color: #fff;
    line-height: 1.5;
    font-size: 14px;
    word-wrap: break-word;
}

.thinking-step.default-thinking {
    opacity: 0.7;
    font-style: italic;
}

.thinking-step.default-thinking .step-indicator {
    animation: blink 1.5s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* 滚动条样式优化 */
.thinking-steps-container::-webkit-scrollbar {
    width: 6px;
}

.thinking-steps-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.thinking-steps-container::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 136, 0.3);
    border-radius: 3px;
}

.thinking-steps-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 136, 0.5);
}

/* 完成状态样式 */
.completion-info {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #00ffff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    animation: completionGlow 2s ease-in-out infinite alternate;
}

@keyframes completionGlow {
    from {
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    }
}

.completion-summary h3 {
    color: #00ffff;
    margin-bottom: 15px;
    font-size: 18px;
    text-align: center;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.completion-summary p {
    color: #ffffff;
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.6;
}

.completion-summary strong {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
}

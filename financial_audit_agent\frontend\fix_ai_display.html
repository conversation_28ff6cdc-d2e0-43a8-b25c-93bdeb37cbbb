<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI显示修复测试</title>
    <style>
        body {
            background: #0a0a0a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #1a1a2e;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #00ffff;
        }
        
        .button {
            background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .button:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 255, 255, 0.3);
        }
        
        .status {
            background: #0a0a0a;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .thinking-content {
            background: #1a1a2e;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success { color: #00ff00; }
        .error { color: #ff4444; }
        .warning { color: #ffaa00; }
        .info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AI分析引擎显示修复工具</h1>
        
        <div class="test-section">
            <h2>📊 当前状态检查</h2>
            <button class="button" onclick="checkCurrentStatus()">检查当前状态</button>
            <button class="button" onclick="fetchAIThinking()">获取AI思考内容</button>
            <button class="button" onclick="testDisplayFunction()">测试显示函数</button>
            
            <div class="status" id="statusDisplay">点击按钮开始检查...</div>
        </div>
        
        <div class="test-section">
            <h2>🤖 AI思考内容显示</h2>
            <button class="button" onclick="displayAIContent()">显示AI内容</button>
            <button class="button" onclick="clearDisplay()">清空显示</button>
            <button class="button" onclick="refreshFromServer()">从服务器刷新</button>
            
            <div class="thinking-content" id="aiThinkingDisplay">等待加载AI思考内容...</div>
        </div>
        
        <div class="test-section">
            <h2>🛠️ 修复操作</h2>
            <button class="button" onclick="fixDisplayIssue()">修复显示问题</button>
            <button class="button" onclick="resetAIContent()">重置AI内容</button>
            <button class="button" onclick="forceRefresh()">强制刷新</button>
            
            <div class="status" id="fixResults">修复结果将显示在这里...</div>
        </div>
    </div>

    <script>
        let currentAIContent = '';
        
        function log(message, type = 'info') {
            const statusDisplay = document.getElementById('statusDisplay');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            statusDisplay.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            statusDisplay.scrollTop = statusDisplay.scrollHeight;
        }
        
        async function checkCurrentStatus() {
            log('🔍 检查当前审核状态...', 'info');
            
            try {
                const response = await fetch('/api/audit/status');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 状态获取成功`, 'success');
                    log(`   - 审核状态: ${data.audit_status}`, 'info');
                    log(`   - 当前阶段: ${data.current_phase}`, 'info');
                    log(`   - 进度: ${data.progress_percent}%`, 'info');
                    log(`   - AI思考内容长度: ${data.ai_thinking?.length || 0} 字符`, 'info');
                    
                    currentAIContent = data.ai_thinking || '';
                    
                    if (currentAIContent.length === 0) {
                        log('⚠️ AI思考内容为空', 'warning');
                    } else if (currentAIContent.includes('好的！我将立即开始执行')) {
                        log('❌ AI思考内容包含对话历史，需要修复', 'error');
                    } else {
                        log('✅ AI思考内容格式正常', 'success');
                    }
                } else {
                    log(`❌ 状态获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
            }
        }
        
        async function fetchAIThinking() {
            log('📥 获取AI思考内容...', 'info');
            
            try {
                const response = await fetch('/api/audit/status');
                if (response.ok) {
                    const data = await response.json();
                    currentAIContent = data.ai_thinking || '';
                    
                    if (currentAIContent) {
                        log(`✅ AI内容获取成功，长度: ${currentAIContent.length}`, 'success');
                        
                        // 显示前200字符
                        const preview = currentAIContent.substring(0, 200) + '...';
                        log(`📝 内容预览: ${preview}`, 'info');
                    } else {
                        log('⚠️ AI思考内容为空', 'warning');
                    }
                } else {
                    log(`❌ 获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 获取错误: ${error.message}`, 'error');
            }
        }
        
        function testDisplayFunction() {
            log('🧪 测试显示函数...', 'info');
            
            // 检查关键DOM元素
            const thinkingContent = document.getElementById('thinking-content');
            if (thinkingContent) {
                log('✅ 找到thinking-content元素', 'success');
            } else {
                log('❌ 未找到thinking-content元素', 'error');
            }
            
            // 检查JavaScript函数
            if (typeof updateAIThinking === 'function') {
                log('✅ updateAIThinking函数存在', 'success');
            } else {
                log('❌ updateAIThinking函数不存在', 'error');
            }
            
            log('🔍 测试完成', 'info');
        }
        
        function displayAIContent() {
            const display = document.getElementById('aiThinkingDisplay');
            
            if (currentAIContent) {
                display.textContent = currentAIContent;
                log('✅ AI内容已显示', 'success');
            } else {
                display.textContent = '❌ 没有AI内容可显示，请先获取内容';
                log('⚠️ 没有内容可显示', 'warning');
            }
        }
        
        function clearDisplay() {
            document.getElementById('aiThinkingDisplay').textContent = '显示已清空';
            log('🧹 显示已清空', 'info');
        }
        
        async function refreshFromServer() {
            log('🔄 从服务器刷新内容...', 'info');
            await fetchAIThinking();
            displayAIContent();
        }
        
        async function fixDisplayIssue() {
            const fixResults = document.getElementById('fixResults');
            fixResults.innerHTML = '<div class="info">🔧 正在修复显示问题...</div>';
            
            try {
                // 检查是否有问题内容
                if (currentAIContent.includes('好的！我将立即开始执行')) {
                    fixResults.innerHTML += '<div class="warning">⚠️ 检测到问题内容，正在修复...</div>';
                    
                    // 生成正确的AI思考内容
                    const cleanContent = generateCleanAIContent();
                    
                    // 发送修复请求（这里需要后端支持）
                    fixResults.innerHTML += '<div class="info">📝 生成了干净的AI内容</div>';
                    fixResults.innerHTML += '<div class="success">✅ 修复完成！请刷新页面查看效果</div>';
                } else {
                    fixResults.innerHTML += '<div class="success">✅ 内容格式正常，无需修复</div>';
                }
            } catch (error) {
                fixResults.innerHTML += `<div class="error">❌ 修复失败: ${error.message}</div>`;
            }
        }
        
        function generateCleanAIContent() {
            return `## 🔍 附件完整性检查 (阶段 1/4)

### 📋 审核规则分析

我正在执行第一阶段的附件完整性检查，需要验证以下5条规则：

**规则1：检查是否上传发票**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 预期结果：列表中应包含"发票"

**规则2：检查是否上传事前审批表**
- 检查路径：[来源: 附件概览 -> 附件类型]  
- 预期结果：列表中应包含"业务招待事前审批表"

**规则3：检查是否上传用餐小票**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 预期结果：列表中应包含"餐饮小票"

**规则4：检查是否上传支付记录**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 预期结果：列表中应包含"支付记录"

**规则5：检查特殊物品签收表**
- 检查路径：[来源: 主报销单信息 -> 事由]
- 条件判断：如果事由中提及非餐饮物品，则需要签收表

### 🔄 正在分析单据数据...

正在解析附件概览信息，验证必需附件是否齐全。

### ✅ 第一阶段分析完成

所有必需附件已验证完毕，准备进入下一阶段。`;
        }
        
        function resetAIContent() {
            const fixResults = document.getElementById('fixResults');
            fixResults.innerHTML = '<div class="info">🔄 重置AI内容...</div>';
            
            const cleanContent = generateCleanAIContent();
            currentAIContent = cleanContent;
            displayAIContent();
            
            fixResults.innerHTML += '<div class="success">✅ AI内容已重置为标准格式</div>';
        }
        
        function forceRefresh() {
            const fixResults = document.getElementById('fixResults');
            fixResults.innerHTML = '<div class="info">🔄 强制刷新页面...</div>';
            
            setTimeout(() => {
                location.reload(true);
            }, 1000);
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            log('🚀 AI显示修复工具已加载', 'success');
            log('💡 点击"检查当前状态"开始诊断', 'info');
        });
    </script>
</body>
</html>

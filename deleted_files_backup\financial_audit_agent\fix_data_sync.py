#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前后端数据同步修复脚本
自动检测和修复前后端数据联动问题
"""

import os
import sys
import json
import time
import requests
import subprocess
from pathlib import Path


class DataSyncFixer:
    """数据同步修复器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.issues = []
        self.fixes = []
        
    def check_api_server(self):
        """检查API服务器状态"""
        print("🔍 检查API服务器状态...")
        
        ports = [8001, 8002, 8003]
        api_available = False
        
        for port in ports:
            try:
                response = requests.get(f"http://localhost:{port}/api/status", timeout=2)
                if response.status_code == 200:
                    print(f"✅ API服务器运行正常: http://localhost:{port}")
                    api_available = True
                    break
            except requests.RequestException:
                continue
        
        if not api_available:
            self.issues.append("API服务器未运行")
            print("❌ API服务器未运行")
            return False
        
        return True
    
    def check_status_files(self):
        """检查状态文件"""
        print("\n🔍 检查状态文件...")
        
        status_file = self.project_root / "backend" / "audit_status.json"
        
        if not status_file.exists():
            self.issues.append("状态文件不存在")
            print("❌ 状态文件不存在")
            
            # 创建默认状态文件
            default_status = {
                "current_step": "ready",
                "status": "idle",
                "message": "系统就绪",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "progress": 0
            }
            
            try:
                with open(status_file, 'w', encoding='utf-8') as f:
                    json.dump(default_status, f, ensure_ascii=False, indent=2)
                print("✅ 已创建默认状态文件")
                self.fixes.append("创建默认状态文件")
            except Exception as e:
                print(f"❌ 创建状态文件失败: {e}")
                return False
        else:
            print("✅ 状态文件存在")
        
        return True
    
    def check_report_files(self):
        """检查审核报告文件"""
        print("\n🔍 检查审核报告文件...")
        
        reports_dir = self.project_root / "audit_reports"
        
        if not reports_dir.exists():
            reports_dir.mkdir(exist_ok=True)
            print("✅ 已创建审核报告目录")
            self.fixes.append("创建审核报告目录")
        
        # 检查是否有审核报告
        report_files = list(reports_dir.glob("*.json"))
        
        if not report_files:
            self.issues.append("无审核报告文件")
            print("⚠️ 未找到审核报告文件")
            
            # 创建示例报告
            sample_report = {
                "summary": {
                    "total_rules_checked": 38,
                    "passed_count": 35,
                    "failed_count": 1,
                    "warning_count": 2,
                    "audit_date": time.strftime("%Y-%m-%d"),
                    "audit_time": time.strftime("%H:%M:%S")
                },
                "details": [
                    {
                        "rule_id": "规则1",
                        "rule_name": "检查是否上传发票",
                        "status": "PASS",
                        "message": "发票文件已上传",
                        "category": "deterministic"
                    },
                    {
                        "rule_id": "规则7",
                        "rule_name": "敏感关键词检测",
                        "status": "WARNING",
                        "message": "检测到可能的敏感内容",
                        "category": "keyword"
                    },
                    {
                        "rule_id": "规则15",
                        "rule_name": "AI语义一致性分析",
                        "status": "FAIL",
                        "message": "内容存在逻辑不一致",
                        "category": "semantic"
                    }
                ],
                "meta": {
                    "generated_by": "数据同步修复脚本",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "version": "sample"
                }
            }
            
            sample_file = reports_dir / "audit_report_sample.json"
            try:
                with open(sample_file, 'w', encoding='utf-8') as f:
                    json.dump(sample_report, f, ensure_ascii=False, indent=2)
                print("✅ 已创建示例审核报告")
                self.fixes.append("创建示例审核报告")
            except Exception as e:
                print(f"❌ 创建示例报告失败: {e}")
                return False
        else:
            print(f"✅ 找到 {len(report_files)} 个审核报告文件")
        
        return True
    
    def check_frontend_files(self):
        """检查前端文件"""
        print("\n🔍 检查前端文件...")
        
        frontend_files = [
            "frontend/ai_console_enhanced.js",
            "frontend/error_handler.js", 
            "frontend/performance_monitor.js",
            "frontend/ai_console.html"
        ]
        
        missing_files = []
        for file_path in frontend_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.issues.append(f"缺少前端文件: {missing_files}")
            print(f"❌ 缺少前端文件: {missing_files}")
            return False
        else:
            print("✅ 前端文件完整")
            return True
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("\n🔍 测试API端点...")
        
        endpoints = [
            "/api/status",
            "/api/report", 
            "/api/rules",
            "/api/progress"
        ]
        
        base_url = "http://localhost:8001"  # 假设API在8001端口
        
        failed_endpoints = []
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint} 响应正常")
                else:
                    print(f"⚠️ {endpoint} 响应异常: {response.status_code}")
                    failed_endpoints.append(endpoint)
            except requests.RequestException as e:
                print(f"❌ {endpoint} 请求失败: {e}")
                failed_endpoints.append(endpoint)
        
        if failed_endpoints:
            self.issues.append(f"API端点失败: {failed_endpoints}")
            return False
        
        return True
    
    def fix_cors_issues(self):
        """修复CORS问题"""
        print("\n🔧 检查CORS配置...")
        
        # 检查API服务器是否正确设置了CORS头
        try:
            response = requests.options("http://localhost:8001/api/status", timeout=2)
            cors_headers = response.headers.get('Access-Control-Allow-Origin')
            
            if cors_headers != '*':
                print("⚠️ CORS配置可能有问题")
                self.issues.append("CORS配置问题")
            else:
                print("✅ CORS配置正常")
        except requests.RequestException:
            print("⚠️ 无法检查CORS配置")
    
    def generate_report(self):
        """生成修复报告"""
        print("\n" + "="*60)
        print("📊 数据同步检查报告")
        print("="*60)
        
        print(f"\n🔍 检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if self.issues:
            print(f"\n❌ 发现问题 ({len(self.issues)} 个):")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("\n✅ 未发现问题")
        
        if self.fixes:
            print(f"\n🔧 已执行修复 ({len(self.fixes)} 个):")
            for i, fix in enumerate(self.fixes, 1):
                print(f"  {i}. {fix}")
        
        print(f"\n💡 建议:")
        print("  1. 确保前端和后台服务都在运行")
        print("  2. 检查浏览器控制台是否有JavaScript错误")
        print("  3. 验证API端点是否可访问")
        print("  4. 确认审核报告文件已生成")
        
        print(f"\n🌐 测试地址:")
        print("  • 数据同步测试: http://localhost:8002/frontend/test_data_sync.html")
        print("  • AI控制台: http://localhost:8002/frontend/ai_console.html")
    
    def run_fix(self):
        """运行完整的修复流程"""
        print("🚀 开始前后端数据同步检查和修复")
        print("="*60)
        
        # 执行各项检查
        checks = [
            self.check_api_server,
            self.check_status_files,
            self.check_report_files,
            self.check_frontend_files,
            self.test_api_endpoints,
            self.fix_cors_issues
        ]
        
        for check in checks:
            try:
                check()
            except Exception as e:
                print(f"❌ 检查过程中出现异常: {e}")
                self.issues.append(f"检查异常: {str(e)}")
        
        # 生成报告
        self.generate_report()
        
        return len(self.issues) == 0


def main():
    """主函数"""
    fixer = DataSyncFixer()
    success = fixer.run_fix()
    
    if success:
        print("\n🎉 数据同步检查完成，系统状态良好！")
        return 0
    else:
        print("\n⚠️ 发现问题，请根据报告进行修复")
        return 1


if __name__ == "__main__":
    sys.exit(main())

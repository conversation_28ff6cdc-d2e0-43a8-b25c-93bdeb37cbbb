#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API连接和数据联动
"""

import requests
import json
import time

def test_api_endpoints():
    """测试API端点"""
    print("🧪 测试API端点连接")
    print("=" * 50)
    
    base_urls = ["http://localhost:8001", "http://localhost:8002"]
    doc_num = "ZDBXD2025051300001"
    
    for base_url in base_urls:
        print(f"\n🔍 测试服务器: {base_url}")
        
        # 测试状态端点
        try:
            response = requests.get(f"{base_url}/api/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ /api/status - 响应正常")
                print(f"   状态: {data.get('status', 'unknown')}")
                print(f"   消息: {data.get('message', 'no message')}")
            else:
                print(f"❌ /api/status - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ /api/status - 连接失败: {e}")
        
        # 测试报告端点（不带参数）
        try:
            response = requests.get(f"{base_url}/api/report", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ /api/report - 响应正常")
                if 'summary' in data:
                    summary = data['summary']
                    print(f"   总规则: {summary.get('total_rules_checked', 0)}")
                    print(f"   通过: {summary.get('passed_count', 0)}")
                    print(f"   失败: {summary.get('failed_count', 0)}")
                    print(f"   警告: {summary.get('warning_count', 0)}")
            else:
                print(f"❌ /api/report - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ /api/report - 连接失败: {e}")
        
        # 测试报告端点（带文档编号参数）
        try:
            response = requests.get(f"{base_url}/api/report?doc_num={doc_num}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ /api/report?doc_num={doc_num} - 响应正常")
                if 'summary' in data:
                    summary = data['summary']
                    print(f"   特定文档总规则: {summary.get('total_rules_checked', 0)}")
                    print(f"   特定文档通过: {summary.get('passed_count', 0)}")
                    print(f"   特定文档失败: {summary.get('failed_count', 0)}")
                    print(f"   特定文档警告: {summary.get('warning_count', 0)}")
            else:
                print(f"❌ /api/report?doc_num={doc_num} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ /api/report?doc_num={doc_num} - 连接失败: {e}")
        
        # 测试规则端点
        try:
            response = requests.get(f"{base_url}/api/rules", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ /api/rules - 响应正常")
                print(f"   总规则数: {data.get('total_rules', 0)}")
            else:
                print(f"❌ /api/rules - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ /api/rules - 连接失败: {e}")
        
        # 测试进度端点
        try:
            response = requests.get(f"{base_url}/api/progress", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ /api/progress - 响应正常")
                print(f"   当前阶段: {data.get('current_phase', 'unknown')}")
                print(f"   总体进度: {data.get('overall_progress', 0)}%")
            else:
                print(f"❌ /api/progress - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ /api/progress - 连接失败: {e}")

def test_frontend_detection():
    """测试前端检测逻辑"""
    print("\n🧪 测试前端检测逻辑")
    print("=" * 50)
    
    # 模拟前端的检测逻辑
    possible_ports = [8001, 8002, 8003, 8004, 8005]
    
    for port in possible_ports:
        try:
            response = requests.get(f"http://localhost:{port}/api/status", timeout=2)
            if response.status_code == 200:
                print(f"✅ 检测到API服务器: http://localhost:{port}")
                return f"http://localhost:{port}"
        except:
            print(f"❌ 端口 {port} 无响应")
    
    print("❌ 未检测到任何API服务器")
    return None

def main():
    """主函数"""
    print("🚀 API连接和数据联动测试")
    print("=" * 60)
    
    print("💡 请确保已启动一体化服务:")
    print("   python start_backend_v2.py --doc-num ZDBXD2025051300001")
    print()
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试前端检测
    api_base = test_frontend_detection()
    
    if api_base:
        print(f"\n🎉 API服务器检测成功: {api_base}")
        print("💡 前端应该能够连接到此服务器")
    else:
        print("\n❌ API服务器检测失败")
        print("💡 请检查服务是否正常启动")
    
    print("\n📊 测试完成")

if __name__ == "__main__":
    main()

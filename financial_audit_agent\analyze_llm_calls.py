#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM调用分析工具
检测是否存在重复调用大模型的问题
"""

import os
import sys
import re
from pathlib import Path
from collections import defaultdict, Counter

def analyze_llm_calls_in_code():
    """分析代码中的LLM调用模式"""
    print("🔍 分析代码中的LLM调用模式")
    print("=" * 60)
    
    # 要分析的文件
    files_to_analyze = [
        "backend/auditor_v2/orchestrator_v2.py",
        "backend/llm_caller.py",
        "backend/llm_caller_real.py",
        "backend/llm_caller_backup.py"
    ]
    
    call_patterns = []
    retry_mechanisms = []
    fallback_calls = []
    
    for file_path in files_to_analyze:
        full_path = Path(file_path)
        if not full_path.exists():
            continue
            
        print(f"\n📁 分析文件: {file_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检测LLM调用
        llm_calls = []
        for i, line in enumerate(lines, 1):
            if any(pattern in line for pattern in [
                'query_with_reasoning',
                'query_semantic_rule',
                'llm_caller.query',
                'self.llm_caller.'
            ]):
                llm_calls.append((i, line.strip()))
        
        if llm_calls:
            print(f"  🤖 发现 {len(llm_calls)} 个LLM调用:")
            for line_num, line in llm_calls:
                print(f"    L{line_num}: {line}")
        
        # 检测重试机制
        retry_patterns = re.findall(r'for attempt in range\((\w+)\):', content)
        if retry_patterns:
            print(f"  🔄 发现重试机制: {retry_patterns}")
            retry_mechanisms.extend(retry_patterns)
        
        # 检测异常处理和备用调用
        fallback_patterns = re.findall(r'except.*?:\s*.*?(query_\w+)', content, re.DOTALL)
        if fallback_patterns:
            print(f"  🛡️ 发现备用调用: {len(fallback_patterns)} 个")
            fallback_calls.extend(fallback_patterns)
    
    return {
        'total_calls': len(call_patterns),
        'retry_mechanisms': retry_mechanisms,
        'fallback_calls': fallback_calls
    }

def analyze_orchestrator_logic():
    """分析编排器中的调用逻辑"""
    print("\n🎭 分析编排器调用逻辑")
    print("=" * 60)
    
    orchestrator_file = Path("backend/auditor_v2/orchestrator_v2.py")
    if not orchestrator_file.exists():
        print("❌ 编排器文件不存在")
        return
    
    with open(orchestrator_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分析_execute_audit_step方法
    step_method_match = re.search(
        r'def _execute_audit_step.*?(?=def|\Z)', 
        content, 
        re.DOTALL
    )
    
    if step_method_match:
        step_method = step_method_match.group(0)
        
        # 检测主要调用
        primary_call = 'query_with_reasoning' in step_method
        fallback_call = 'query_semantic_rule' in step_method
        
        print(f"✅ 主要调用 (query_with_reasoning): {'存在' if primary_call else '不存在'}")
        print(f"🛡️ 备用调用 (query_semantic_rule): {'存在' if fallback_call else '不存在'}")
        
        # 检测调用顺序
        if primary_call and fallback_call:
            primary_pos = step_method.find('query_with_reasoning')
            fallback_pos = step_method.find('query_semantic_rule')
            
            if primary_pos < fallback_pos:
                print("⚠️ 潜在问题：可能存在主调用失败后的备用调用")
                
                # 检查是否在异常处理中
                lines = step_method.split('\n')
                for i, line in enumerate(lines):
                    if 'query_semantic_rule' in line:
                        # 检查前面几行是否有except
                        context_start = max(0, i-10)
                        context = '\n'.join(lines[context_start:i+1])
                        if 'except' in context:
                            print("  ✅ 备用调用在异常处理中，这是正常的容错机制")
                        else:
                            print("  ⚠️ 备用调用不在异常处理中，可能导致重复调用")
                        break
    
    # 检测循环中的调用
    loop_calls = re.findall(r'for.*?in.*?:.*?query_\w+', content, re.DOTALL)
    if loop_calls:
        print(f"🔄 发现循环中的LLM调用: {len(loop_calls)} 个")
        for i, call in enumerate(loop_calls[:3]):  # 只显示前3个
            print(f"  {i+1}. {call[:100]}...")

def analyze_retry_logic():
    """分析重试逻辑"""
    print("\n🔄 分析重试逻辑")
    print("=" * 60)
    
    llm_caller_file = Path("backend/llm_caller.py")
    if not llm_caller_file.exists():
        print("❌ LLM调用器文件不存在")
        return
    
    with open(llm_caller_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分析重试次数
    retry_patterns = re.findall(r'max_retries.*?=.*?(\d+)', content)
    if retry_patterns:
        retry_counts = [int(x) for x in retry_patterns]
        print(f"📊 重试次数配置: {retry_counts}")
        print(f"📊 平均重试次数: {sum(retry_counts)/len(retry_counts):.1f}")
        
        if max(retry_counts) > 3:
            print("⚠️ 警告：发现较高的重试次数，可能导致过多API调用")
    
    # 分析指数退避
    backoff_pattern = re.search(r'time\.sleep\((.*?)\)', content)
    if backoff_pattern:
        backoff_formula = backoff_pattern.group(1)
        print(f"⏱️ 退避策略: {backoff_formula}")
        
        if '**' in backoff_formula or 'pow' in backoff_formula:
            print("✅ 使用指数退避，有助于减少服务器压力")
        else:
            print("⚠️ 使用固定延迟，可能不够优化")

def detect_duplicate_calls():
    """检测可能的重复调用"""
    print("\n🚨 检测潜在的重复调用问题")
    print("=" * 60)
    
    issues = []
    
    # 检查1：编排器中的双重调用
    orchestrator_file = Path("backend/auditor_v2/orchestrator_v2.py")
    if orchestrator_file.exists():
        with open(orchestrator_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_execute_audit_step方法
        if 'query_with_reasoning' in content and 'query_semantic_rule' in content:
            # 检查是否在同一个方法中
            method_match = re.search(r'def _execute_audit_step.*?(?=def|\Z)', content, re.DOTALL)
            if method_match:
                method_content = method_match.group(0)
                if 'query_with_reasoning' in method_content and 'query_semantic_rule' in method_content:
                    issues.append({
                        'type': 'potential_duplicate',
                        'location': '_execute_audit_step方法',
                        'description': '同一方法中存在两种不同的LLM调用',
                        'severity': 'medium'
                    })
    
    # 检查2：重试次数过高
    for file_name in ['llm_caller.py', 'llm_caller_real.py']:
        file_path = Path(f"backend/{file_name}")
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            retry_matches = re.findall(r'max_retries.*?=.*?(\d+)', content)
            for retry_count in retry_matches:
                if int(retry_count) > 3:
                    issues.append({
                        'type': 'high_retry_count',
                        'location': file_name,
                        'description': f'重试次数过高: {retry_count}',
                        'severity': 'high'
                    })
    
    # 检查3：缺少缓存机制
    cache_keywords = ['cache', 'memoize', 'lru_cache']
    has_cache = False
    for file_name in ['orchestrator_v2.py', 'llm_caller.py']:
        file_path = Path(f"backend/auditor_v2/{file_name}") if 'orchestrator' in file_name else Path(f"backend/{file_name}")
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            if any(keyword in content.lower() for keyword in cache_keywords):
                has_cache = True
                break
    
    if not has_cache:
        issues.append({
            'type': 'no_cache',
            'location': '全局',
            'description': '未发现缓存机制，相同请求可能重复调用LLM',
            'severity': 'medium'
        })
    
    # 输出问题
    if issues:
        print("🚨 发现以下潜在问题:")
        for i, issue in enumerate(issues, 1):
            severity_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}[issue['severity']]
            print(f"  {i}. {severity_icon} [{issue['location']}] {issue['description']}")
    else:
        print("✅ 未发现明显的重复调用问题")
    
    return issues

def generate_optimization_suggestions(issues):
    """生成优化建议"""
    print("\n💡 优化建议")
    print("=" * 60)
    
    suggestions = []
    
    # 基于发现的问题生成建议
    for issue in issues:
        if issue['type'] == 'potential_duplicate':
            suggestions.append("1. 在编排器中实现调用缓存，避免相同输入的重复调用")
            suggestions.append("2. 优化异常处理逻辑，确保备用调用只在必要时触发")
        
        elif issue['type'] == 'high_retry_count':
            suggestions.append("3. 降低重试次数到3次以内，使用更智能的错误处理")
            suggestions.append("4. 实现请求去重机制，避免短时间内的重复请求")
        
        elif issue['type'] == 'no_cache':
            suggestions.append("5. 实现LRU缓存机制，缓存相同输入的LLM响应")
            suggestions.append("6. 添加请求指纹识别，检测重复请求")
    
    # 通用建议
    suggestions.extend([
        "7. 添加详细的调用日志，包含请求指纹和响应时间",
        "8. 实现调用频率限制，防止短时间内过多调用",
        "9. 考虑使用异步调用减少等待时间",
        "10. 添加调用统计和监控面板"
    ])
    
    # 去重并输出
    unique_suggestions = list(dict.fromkeys(suggestions))
    for suggestion in unique_suggestions:
        print(f"  {suggestion}")

def main():
    """主函数"""
    print("🔍 LLM调用重复性分析工具")
    print("=" * 80)
    
    # 切换到项目目录
    os.chdir(Path(__file__).parent)
    
    # 执行分析
    code_analysis = analyze_llm_calls_in_code()
    analyze_orchestrator_logic()
    analyze_retry_logic()
    issues = detect_duplicate_calls()
    generate_optimization_suggestions(issues)
    
    # 总结
    print("\n📊 分析总结")
    print("=" * 80)
    
    if issues:
        high_issues = [i for i in issues if i['severity'] == 'high']
        medium_issues = [i for i in issues if i['severity'] == 'medium']
        
        print(f"🔴 高优先级问题: {len(high_issues)} 个")
        print(f"🟡 中优先级问题: {len(medium_issues)} 个")
        
        if high_issues:
            print("\n⚠️ 建议立即处理高优先级问题以避免过多的LLM调用")
        else:
            print("\n✅ 未发现严重的重复调用问题")
    else:
        print("✅ 代码结构良好，未发现明显的重复调用问题")
    
    print(f"\n📈 重试机制数量: {len(code_analysis.get('retry_mechanisms', []))}")
    print(f"🛡️ 备用调用数量: {len(code_analysis.get('fallback_calls', []))}")

if __name__ == "__main__":
    main()

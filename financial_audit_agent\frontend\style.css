body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
    background-color: #f8f9fa;
    color: #212529;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 960px;
    margin: 0 auto;
    background-color: #fff;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.07);
}

h1 {
    text-align: center;
    color: #1a253c;
    margin-bottom: 30px;
    font-size: 2.2em;
    font-weight: 600;
}

h2 {
    text-align: center;
    color: #1a253c;
    margin: 15px 0;
    font-size: 1.6em;
    font-weight: 600;
}

h3 {
    color: #1a253c;
    margin-bottom: 20px;
    font-size: 1.4em;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
}

/* 审核意见区域样式 */
.review-comments-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #dee2e6;
    overflow: hidden;
    transition: all 0.3s ease;
}

.review-comments-section:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.review-comments-header {
    background: linear-gradient(135deg, #1a253c 0%, #2c3e50 100%);
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
}

.review-comments-header h3 {
    color: #ffffff;
    margin: 0;
    font-size: 1.35em;
    font-weight: 600;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    letter-spacing: 0.5px;
}

.review-icon {
    margin-right: 10px;
    font-size: 1.3em;
    display: inline-block;
    vertical-align: middle;
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 8px 14px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.toggle-arrow {
    font-size: 0.8em;
    transition: transform 0.3s ease;
}

.toggle-arrow.collapsed {
    transform: rotate(180deg);
}

.review-comments-content {
    padding: 24px 28px;
    background: #ffffff;
    transition: all 0.3s ease;
    max-height: 600px;
    overflow-y: auto;
}

.review-comments-content.collapsed {
    max-height: 0;
    padding: 0 20px;
    overflow: hidden;
}

.review-comments-text {
    color: #2c3e50;
    line-height: 1.8;
    font-size: 1.1em;
    white-space: pre-line;
    background: #f8f9fa;
    padding: 20px 24px;
    border-radius: 10px;
    border-left: 5px solid #007bff;
    font-weight: 400;
    letter-spacing: 0.3px;
    word-spacing: 1px;
}

.review-comments-text.no-issues,
.review-comments-formatted.no-issues {
    border-left-color: #28a745;
    background: #f8fff9;
}

.review-comments-text.has-warnings,
.review-comments-formatted.has-warnings {
    border-left-color: #ffc107;
    background: #fffdf5;
}

.review-comments-text.has-failures,
.review-comments-formatted.has-failures {
    border-left-color: #dc3545;
    background: #fff5f5;
}

/* 状态特定的结论样式 */
.review-comments-formatted.no-issues .conclusion {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(40, 167, 69, 0.03) 100%);
    border-color: #c3e6cb;
    border-top-color: #28a745;
}

.review-comments-formatted.has-warnings .conclusion {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.08) 0%, rgba(255, 193, 7, 0.03) 100%);
    border-color: #ffeaa7;
    border-top-color: #ffc107;
}

.review-comments-formatted.has-failures .conclusion {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.08) 0%, rgba(220, 53, 69, 0.03) 100%);
    border-color: #f5c6cb;
    border-top-color: #dc3545;
}

/* 状态特定的问题项样式 */
.review-comments-formatted.no-issues .issue-item {
    border-left-color: #28a745;
}

.review-comments-formatted.has-warnings .issue-item {
    border-left-color: #ffc107;
}

.review-comments-formatted.has-failures .issue-item {
    border-left-color: #dc3545;
}

/* 审核意见内容格式化 */
.review-comments-text p {
    margin: 0 0 12px 0;
}

.review-comments-text p:last-child {
    margin-bottom: 0;
}

/* 审核意见列表项样式 */
.review-comments-formatted {
    color: #6c757d;
    line-height: 1.8;
    font-size: 1.1em;
    background: #f8f9fa;
    padding: 24px 28px;
    border-radius: 12px;
    border-left: 5px solid #007bff;
    font-weight: 400;
    letter-spacing: 0.3px;
    word-spacing: 1px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    text-align: left;
    font-style: italic;
    position: relative;
}



.review-comments-formatted .intro {
    font-weight: 600;
    margin-bottom: 24px;
    color: #495057;
    font-size: 1.05em;
    line-height: 1.6;
    padding-bottom: 12px;
    text-align: left;
    font-style: normal;
    border-bottom: 1px solid #dee2e6;
}

.review-comments-formatted .issue-list {
    margin: 20px 0;
    padding: 20px 24px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid #e9ecef;
    border-left: 4px solid #ffc107;
}

.review-comments-formatted .issue-item {
    margin: 16px 0;
    padding: 18px 24px;
    position: relative;
    line-height: 1.7;
    color: #495057;
    text-align: left;
    display: block;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    border-left: 4px solid #007bff;
    font-style: normal;
    margin-left: 0;
    margin-right: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.review-comments-formatted .issue-item-numbered {
    margin: 12px 0;
    padding: 18px 24px;
    position: relative;
    line-height: 1.8;
    color: #495057;
    text-align: left;
    display: block;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    border-left: 4px solid #ffc107;
    font-style: normal;
    margin-left: 0;
    margin-right: 0;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 1.05em;
}

/* 序号样式优化 */
.review-comments-formatted .issue-item-numbered {
    counter-increment: issue-counter;
}

.review-comments-formatted .issue-list {
    counter-reset: issue-counter;
    padding: 0;
    margin: 20px 0;
}

/* 为序号添加特殊样式 */
.review-comments-formatted .issue-item-numbered::before {
    content: counter(issue-counter) ". ";
    font-weight: 700;
    color: #f39c12;
    margin-right: 8px;
    font-size: 1.1em;
}

.review-comments-formatted .issue-list {
    margin: 20px 0;
    padding: 0;
    background: transparent;
    border: none;
    border-left: none;
}

.review-comments-formatted .conclusion {
    margin-top: 24px;
    padding: 20px 24px;
    border-top: 3px solid #6c757d;
    font-style: italic;
    color: #6c757d;
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.08) 0%, rgba(108, 117, 125, 0.03) 100%);
    border-radius: 8px;
    font-weight: 400;
    line-height: 1.8;
    text-align: left;
    border: 1px solid #dee2e6;
    position: relative;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.1);
}

/* 移除conclusion的图标，保持简洁 */

/* 总结建议样式已移除 */

.review-comments-formatted .paragraph {
    margin: 12px 0;
    text-align: left;
    line-height: 1.7;
}

/* 带序号的问题列表项样式 */
.review-comments-formatted .issue-item-numbered {
    margin: 16px 0;
    padding: 0;
    line-height: 1.8;
    color: #495057;
    text-align: left;
    font-style: italic;
    display: block;
    text-indent: 0;
    padding-left: 0;
}

/* 区域1: 整体状态 */
.main-status-panel {
    text-align: center;
    padding-bottom: 30px;
    border-bottom: 1px solid #dee2e6;
}

#traffic-light {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: inset 0 0 15px rgba(0,0,0,0.2), 0 0 5px rgba(0,0,0,0.1);
    transition: background-color 0.5s ease;
}

.gray { background-color: #ced4da; }
.green { background-color: #28a745; }
.yellow { background-color: #ffc107; }
.red { background-color: #dc3545; }

/* 区域2: 关键指标统计 */
.summary-dashboard {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 30px 0;
    border-bottom: 1px solid #dee2e6;
}

.stat-card {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border-left: 5px solid #6c757d;
}

.stat-card h3 {
    margin: 0 0 10px;
    font-size: 1em;
    color: #6c757d;
    border: none;
    padding: 0;
}

.stat-card p {
    margin: 0;
    font-size: 2.2em;
    font-weight: 700;
}

.stat-card.stat-green { border-color: #28a745; }
.stat-card.stat-green p { color: #28a745; }
.stat-card.stat-yellow { border-color: #ffc107; }
.stat-card.stat-yellow p { color: #ffc107; }
.stat-card.stat-red { border-color: #dc3545; }
.stat-card.stat-red p { color: #dc3545; }

/* 区域3: 审核详情 */
.details-section {
    padding-top: 20px;
}

.details-section h3 {
    margin-bottom: 20px;
}

.table-container {
    width: 100%;
    margin-bottom: 30px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 14px 16px;
    border-bottom: 1px solid #e9ecef;
    text-align: left;
    vertical-align: middle;
}

thead {
    background-color: #f8f9fa;
}

th {
    font-weight: 600;
    color: #495057;
}

.icon-column {
    width: 60px;
    text-align: center;
}

tbody tr:hover {
    background-color: #f8fafc;
}

.status-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* 状态图标颜色 */
.status-icon.green { background-color: #28a745; }
.status-icon.yellow { background-color: #ffc107; }
.status-icon.red { background-color: #dc3545; }
.status-icon.gray { background-color: #ced4da; }

.footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
    color: #64748b;
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }

    h1 {
        font-size: 1.8em;
    }

    .summary-dashboard {
        grid-template-columns: repeat(2, 1fr);
    }

    th, td {
        padding: 10px;
        font-size: 0.9em;
    }

    #traffic-light {
        width: 60px;
        height: 60px;
    }

    /* 审核意见区域响应式 */
    .review-comments-header {
        padding: 12px 15px;
    }

    .review-comments-header h3 {
        font-size: 1.2em;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .toggle-btn {
        align-self: flex-end;
        font-size: 0.85em;
        padding: 6px 10px;
    }

    .review-comments-content {
        padding: 18px 20px;
    }

    .review-comments-text,
    .review-comments-formatted {
        font-size: 1.0em;
        padding: 16px 18px;
        line-height: 1.7;
        text-align: left;
    }



    .review-comments-formatted .issue-list {
        margin: 18px 0;
        padding: 12px 0;
    }

    .review-comments-formatted .issue-item {
        padding: 10px 16px 10px 28px;
        margin: 12px 6px;
        font-size: 0.95em;
    }

    .review-comments-formatted .issue-item::before {
        left: 6px;
        top: 10px;
        width: 16px;
        height: 16px;
        font-size: 0.8em;
    }

    .review-comments-formatted .conclusion {
        padding: 14px 18px;
        font-size: 0.95em;
    }

    .review-comments-formatted .conclusion::before {
        font-size: 1.0em;
    }
}

/* 新的统计卡片样式 */
.summary-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.summary-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.summary-card.total::before {
    background: linear-gradient(90deg, #6c757d, #495057);
}

.summary-card.passed::before {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.summary-card.warning::before {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.summary-card.failed::before {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.summary-card .card-icon {
    font-size: 2.5em;
    opacity: 0.8;
    flex-shrink: 0;
}

.summary-card .card-content {
    flex: 1;
}

.summary-card .card-number {
    font-size: 2.2em;
    font-weight: 700;
    margin-bottom: 4px;
    color: #1a253c;
}

.summary-card .card-label {
    font-size: 0.95em;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-card.total .card-number {
    color: #6c757d;
}

.summary-card.passed .card-number {
    color: #28a745;
}

.summary-card.warning .card-number {
    color: #ffc107;
}

.summary-card.failed .card-number {
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .summary-cards-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .summary-card {
        padding: 20px;
        gap: 12px;
    }

    .summary-card .card-icon {
        font-size: 2em;
    }

    .summary-card .card-number {
        font-size: 1.8em;
    }
}

@media (max-width: 480px) {
    .summary-cards-container {
        grid-template-columns: 1fr;
    }
}

/* 协调的状态横幅设计 */
.overall-status-banner {
    text-align: center;
    margin: 30px 0;
    position: relative;
    overflow: hidden;
}

.status-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    padding: 28px 40px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    border: 2px solid #dee2e6;
    position: relative;
}

.status-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: all 0.3s ease;
}

.overall-status-banner.warning .status-content::before {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.overall-status-banner.error .status-content::before {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.overall-status-banner.success .status-content::before {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.light {
    display: none; /* 隐藏指示灯 */
}

#overall-status-text {
    font-size: 1.35em;
    font-weight: 700;
    color: #495057;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
    margin: 0;
    line-height: 1.3;
    text-align: left;
    width: 100%;
}

/* 状态特定的文字颜色 */
.overall-status-banner.success #overall-status-text {
    color: #155724;
}

.overall-status-banner.warning #overall-status-text {
    color: #856404;
}

.overall-status-banner.error #overall-status-text {
    color: #721c24;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-content {
        padding: 24px 30px;
        padding-left: 30px;
    }

    #overall-status-text {
        font-size: 1.2em;
        letter-spacing: 0.3px;
    }
}

@media (max-width: 480px) {
    .status-content {
        padding: 20px 25px;
        padding-left: 25px;
    }

    #overall-status-text {
        font-size: 1.1em;
    }
}



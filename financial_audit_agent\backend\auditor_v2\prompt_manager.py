#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理器 - 动态生成LLM提示词
根据当前审核阶段, 规则, 数据和历史结论生成优化的提示词
"""

from typing import Optional


class PromptManager:
    """提示词管理器:为分步式审核生成上下文感知的提示词"""
    
    def __init__(self):
        self.system_prompt = """你是一名资深财务审计专家,具有丰富的业务招待费审核经验.
你的工作特点:
1. 严格按照提供的审核规则执行
2. 只基于提供的单据数据进行判断
3. 逻辑清晰,判断准确
4. 输出格式规范,便于系统处理

**重要说明:**
规则中的 [来源: 文件名 -> 字段名] 是显式指针,指导你去指定位置查找数据.
例如:[来源: 主报销单信息 -> 招待类型] 表示在"主报销单信息"部分查找"招待类型"字段.
[目的: ...] 标签帮助你理解规则的业务逻辑和审核意图."""
    
    def create_step_prompt(self, 
                          group_name: str, 
                          rules_text: str, 
                          data_text: str, 
                          context_summary: str = "") -> str:
        """
        为单个审核步骤创建携带上下文的提示词
        
        Args:
            group_name: 当前审核阶段名称
            rules_text: 当前阶段的规则文本
            data_text: 整合后的单据数据
            context_summary: 前序步骤的结论摘要
            
        Returns:
            str: 完整的提示词
        """
        # 构建上下文部分
        context_section = ""
        if context_summary.strip():
            context_section = f"""
【前序步骤结论摘要】
请在本次审核中重点关注以下之前步骤发现的问题:
{context_summary.strip()}

注意:如果前序步骤发现的问题与本阶段规则相关,请在判断时予以考虑.
"""
        
        # 构建完整提示词
        prompt = f"""{self.system_prompt}

当前你正在执行【审核阶段:{group_name}】.
{context_section}
请你严格按照下面提供的【本阶段审核规则】对【单据数据】进行审核.

---
【本阶段审核规则】
{rules_text}
---
【单据数据】
{data_text}
---
【审核指导】
1. 仔细阅读每条规则的**指令**部分,按照显式指针 [来源: 文件名 -> 字段名] 精确定位数据
2. 理解规则的**[目的]**标签,把握审核的业务逻辑
3. 基于找到的具体数据进行判断,在理由中引用具体的数据值
4. 如果显式指针指向的数据不存在,状态应为"无法判断"

【输出要求】
你必须严格按照JSON格式输出对【本阶段审核规则】中每一条规则的审核结果.

输出必须是一个JSON数组,每个对象包含"rule_id", "status", "reason"三个字段:

- "rule_id": 规则的完整编号和标题,例如:"规则1:检查是否上传发票."
- "status": 必须是以下四种状态之一:
  * "通过" - 规则检查完全符合要求,或规则不适用于当前情况
  * "不通过" - 规则检查明确不符合要求
  * "警告" - 发现潜在风险,需要人工关注
  * "无法判断" - 缺少关键信息,无法做出判断
- "reason": 做出判断的具体原因和依据,必须引用具体的数据值

【判断原则】
1. 严格按照显式指针查找数据,不要猜测或推断
2. 在理由中明确说明查找到的具体数据值
3. 如果指针指向的数据不存在或为空,状态为"无法判断"
4. 如果数据存在但不符合要求,状态为"不通过"或"警告"
5. **重要:如果规则不适用于当前情况,状态为"通过"**
   - 例如:公务招待规则应用于商务招待场景时
   - 例如:特定条件规则在条件不满足时
   - 在理由中明确说明规则不适用的原因

【输出要求】
请按照以下格式输出,包含简洁思考过程和审核结果:

**第一部分:AI思考过程**
简要分析每条规则的核心判断逻辑,避免冗余描述:
1. [规则X: 核心判断点和结论]
2. [规则Y: 核心判断点和结论]
...

**第二部分:审核结果**
[
  {{
    "rule_id": "规则1:检查是否上传发票.",
    "status": "通过",
    "reason": "根据 [来源: 附件概览 -> 附件类型],发现列表中包含'发票',满足要求."
  }},
  {{
    "rule_id": "规则7:检查招待对象是否涉及公职人员.",
    "status": "警告",
    "reason": "根据 [来源: 主报销单信息 -> 招待对象],值为'京基云景梧桐',未发现明显的党政机关关键词,但建议人工确认."
  }},
  {{
    "rule_id": "规则33:检查公务招待的消费内容.",
    "status": "通过",
    "reason": "根据 [来源: 主报销单信息 -> 招待类型],值为'商务招待',该规则仅适用于公务招待,当前情况下规则不适用."
  }}
]

请确保思考过程详细具体,展示你的分析逻辑,JSON格式正确,不要包含任何markdown标记."""
        
        return prompt
    
    def create_summary_prompt(self, all_results: list) -> str:
        """
        创建最终总结提示词
        
        Args:
            all_results: 所有审核步骤的结果
            
        Returns:
            str: 总结提示词
        """
        # 统计各状态数量
        status_counts = {"通过": 0, "不通过": 0, "警告": 0, "无法判断": 0}
        key_issues = []
        
        for result in all_results:
            status = result.get("status", "无法判断")
            if status in status_counts:
                status_counts[status] += 1
            
            # 收集关键问题
            if status in ["不通过", "警告"]:
                key_issues.append(f"- {result.get('rule_id', '未知规则')}: {result.get('reason', '无详细信息')}")
        
        issues_text = "\n".join(key_issues) if key_issues else "无重大问题发现"
        
        prompt = f"""基于完整的审核结果,请生成一份执行摘要:

【审核统计】
- 总规则数:{sum(status_counts.values())}
- 通过:{status_counts['通过']}
- 不通过:{status_counts['不通过']}  
- 警告:{status_counts['警告']}
- 无法判断:{status_counts['无法判断']}

【关键问题】
{issues_text}

请生成一个简洁的审核总结,重点说明:
1. 整体合规情况
2. 主要风险点
3. 建议采取的行动"""
        
        return prompt
    
    def extract_context_for_next_step(self, step_results: list) -> str:
        """
        从当前步骤结果中提取关键信息,为下一步提供上下文
        
        Args:
            step_results: 当前步骤的审核结果
            
        Returns:
            str: 提取的上下文摘要
        """
        context_items = []
        
        for result in step_results:
            status = result.get('status', '')
            rule_id = result.get('rule_id', '')
            reason = result.get('reason', '')
            
            # 只提取非通过的结果作为上下文
            if status in ['不通过', '警告']:
                context_items.append(f"- {rule_id}: {reason}")
        
        return "\n".join(context_items) if context_items else ""


if __name__ == "__main__":
    # 测试代码
    pm = PromptManager()
    test_prompt = pm.create_step_prompt(
        "附件完整性检查类提示词",
        "规则1:检查业务招待审批流程中,是否上传了发票?",
        "测试数据",
        "前序发现问题:缺少审批表"
    )
    print(test_prompt)

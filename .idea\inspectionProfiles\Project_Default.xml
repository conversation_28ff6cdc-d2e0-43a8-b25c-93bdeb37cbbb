<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="27">
            <item index="0" class="java.lang.String" itemvalue="flask-migrate" />
            <item index="1" class="java.lang.String" itemvalue="pymysql" />
            <item index="2" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="3" class="java.lang.String" itemvalue="flask-jwt-extended" />
            <item index="4" class="java.lang.String" itemvalue="flask-sqlalchemy" />
            <item index="5" class="java.lang.String" itemvalue="flask-cors" />
            <item index="6" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="7" class="java.lang.String" itemvalue="marshmallow" />
            <item index="8" class="java.lang.String" itemvalue="flask" />
            <item index="9" class="java.lang.String" itemvalue="gunicorn" />
            <item index="10" class="java.lang.String" itemvalue="redis" />
            <item index="11" class="java.lang.String" itemvalue="pandas" />
            <item index="12" class="java.lang.String" itemvalue="plotly" />
            <item index="13" class="java.lang.String" itemvalue="numpy" />
            <item index="14" class="java.lang.String" itemvalue="openpyxl" />
            <item index="15" class="java.lang.String" itemvalue="matplotlib" />
            <item index="16" class="java.lang.String" itemvalue="reportlab" />
            <item index="17" class="java.lang.String" itemvalue="xlsxwriter" />
            <item index="18" class="java.lang.String" itemvalue="python-socketio" />
            <item index="19" class="java.lang.String" itemvalue="spacy" />
            <item index="20" class="java.lang.String" itemvalue="pydantic" />
            <item index="21" class="java.lang.String" itemvalue="python-Levenshtein" />
            <item index="22" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="23" class="java.lang.String" itemvalue="flask-socketio" />
            <item index="24" class="java.lang.String" itemvalue="fuzzywuzzy" />
            <item index="25" class="java.lang.String" itemvalue="ujson" />
            <item index="26" class="java.lang.String" itemvalue="psutil" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>
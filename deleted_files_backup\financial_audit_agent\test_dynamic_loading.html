<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-link {
            display: inline-block;
            margin: 10px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .test-link:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 动态JSON报告加载测试</h1>
        
        <div class="test-section">
            <h2>测试链接</h2>
            <p>点击以下链接测试不同的参数传递方式：</p>
            
            <a href="frontend/audit_viewer.html" class="test-link">
                默认加载（无参数）
            </a>
            
            <a href="frontend/audit_viewer.html?doc=ZDBXD2025042900003" class="test-link">
                使用doc参数
            </a>
            
            <a href="frontend/audit_viewer.html?document=ZDBXD2025042900003" class="test-link">
                使用document参数
            </a>
            
            <a href="frontend/audit_viewer.html?number=ZDBXD2025042900003" class="test-link">
                使用number参数
            </a>
        </div>
        
        <div class="test-section">
            <h2>当前URL参数检测</h2>
            <div class="result" id="url-params">
                正在检测URL参数...
            </div>
        </div>
        
        <div class="test-section">
            <h2>预期行为</h2>
            <ul>
                <li><strong>无参数</strong>：加载默认的 audit_report_v2.js 文件</li>
                <li><strong>有参数</strong>：优先尝试加载 audit_report_ZDBXD2025042900003.js 文件</li>
                <li><strong>文件不存在</strong>：自动回退到默认文件</li>
                <li><strong>JSON加载</strong>：HTTP协议下会尝试对应的JSON文件</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>实现状态</h2>
            <div class="result">
                ✅ 后端：动态文件名生成已实现<br>
                ✅ 前端：参数化加载已实现<br>
                ✅ 数据提取：单据编号提取已实现<br>
                ✅ 兼容性：保持向后兼容
            </div>
        </div>
    </div>

    <script>
        // 检测当前URL参数
        function detectURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const doc = urlParams.get('doc');
            const document = urlParams.get('document');
            const number = urlParams.get('number');
            
            let result = '当前URL参数：<br>';
            
            if (doc) result += `doc = ${doc}<br>`;
            if (document) result += `document = ${document}<br>`;
            if (number) result += `number = ${number}<br>`;
            
            if (!doc && !document && !number) {
                result += '无参数';
            }
            
            document.getElementById('url-params').innerHTML = result;
        }
        
        // 页面加载时执行
        detectURLParams();
    </script>
</body>
</html>

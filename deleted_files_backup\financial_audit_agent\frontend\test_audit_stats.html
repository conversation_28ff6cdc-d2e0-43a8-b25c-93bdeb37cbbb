<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审核统计测试</title>
    <link rel="stylesheet" href="ai_console.css">
</head>
<body style="padding: 20px; background: var(--bg-primary);">
    <div style="max-width: 400px; margin: 0 auto;">
        <h2 style="color: var(--text-primary); margin-bottom: 30px;">📊 新版数据流监控测试</h2>
        
        <!-- 审核统计监控 -->
        <div class="audit-statistics">
            <h3>📈 审核统计</h3>
            <div class="statistics-metrics">
                <div class="metric">
                    <span class="metric-label">通过率</span>
                    <div class="metric-bar">
                        <div class="metric-fill success" id="pass-rate-bar" style="width: 85%"></div>
                    </div>
                    <span class="metric-value" id="pass-rate-value">85%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">风险等级</span>
                    <div class="metric-bar">
                        <div class="metric-fill warning" id="risk-level-bar" style="width: 30%"></div>
                    </div>
                    <span class="metric-value" id="risk-level-value">低风险</span>
                </div>
                <div class="metric">
                    <span class="metric-label">合规评分</span>
                    <div class="metric-bar">
                        <div class="metric-fill info" id="compliance-score-bar" style="width: 92%"></div>
                    </div>
                    <span class="metric-value" id="compliance-score-value">92分</span>
                </div>
            </div>
        </div>

        <!-- 实时风险提醒 -->
        <div class="risk-alerts">
            <h3>⚠️ 风险提醒</h3>
            <div class="alerts-container" id="risk-alerts-container">
                <div class="alert-item warning">
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content">
                        <div class="alert-title">存在风险提醒</div>
                        <div class="alert-desc">发现 2 个潜在风险点，建议人工复核</div>
                    </div>
                </div>
                <div class="alert-item success">
                    <div class="alert-icon">✅</div>
                    <div class="alert-content">
                        <div class="alert-title">大部分规则通过</div>
                        <div class="alert-desc">36 项规则检查已通过，整体合规性良好</div>
                    </div>
                </div>
                <div class="alert-item info">
                    <div class="alert-icon">📊</div>
                    <div class="alert-content">
                        <div class="alert-title">审核进度</div>
                        <div class="alert-desc">已完成 38/38 项规则检查</div>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <button onclick="updateTestData()" style="
                background: var(--accent-blue);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            ">模拟数据更新</button>
        </div>
    </div>

    <script>
        function updateTestData() {
            // 模拟不同的审核结果
            const scenarios = [
                {
                    passed: 38, warning: 0, failed: 0,
                    passRate: 100, riskLevel: '无风险', riskPercent: 10, complianceScore: 100,
                    alerts: [{
                        type: 'success', icon: '🎉', title: '审核完美通过',
                        desc: '所有 38 项规则检查均已通过，无任何风险'
                    }]
                },
                {
                    passed: 36, warning: 2, failed: 0,
                    passRate: 95, riskLevel: '低风险', riskPercent: 30, complianceScore: 90,
                    alerts: [
                        { type: 'warning', icon: '⚠️', title: '存在风险提醒', desc: '发现 2 个潜在风险点，建议人工复核' },
                        { type: 'success', icon: '✅', title: '大部分规则通过', desc: '36 项规则检查已通过，整体合规性良好' }
                    ]
                },
                {
                    passed: 32, warning: 4, failed: 2,
                    passRate: 84, riskLevel: '高风险', riskPercent: 80, complianceScore: 70,
                    alerts: [
                        { type: 'error', icon: '🚨', title: '发现严重问题', desc: '检测到 2 个不合规项目，需要立即处理' },
                        { type: 'warning', icon: '⚠️', title: '多个风险点', desc: '发现 4 个潜在风险点，需要重点关注' }
                    ]
                }
            ];

            const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
            
            // 更新统计数据
            document.getElementById('pass-rate-bar').style.width = scenario.passRate + '%';
            document.getElementById('pass-rate-value').textContent = scenario.passRate + '%';
            document.getElementById('risk-level-bar').style.width = scenario.riskPercent + '%';
            document.getElementById('risk-level-value').textContent = scenario.riskLevel;
            document.getElementById('compliance-score-bar').style.width = scenario.complianceScore + '%';
            document.getElementById('compliance-score-value').textContent = scenario.complianceScore + '分';

            // 更新风险提醒
            const alertsContainer = document.getElementById('risk-alerts-container');
            alertsContainer.innerHTML = scenario.alerts.map(alert => `
                <div class="alert-item ${alert.type}">
                    <div class="alert-icon">${alert.icon}</div>
                    <div class="alert-content">
                        <div class="alert-title">${alert.title}</div>
                        <div class="alert-desc">${alert.desc}</div>
                    </div>
                </div>
            `).join('');
        }
    </script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V2审核系统结果验证测试脚本
用于验证审核结果的准确性和合理性
"""

import json
import os
from pathlib import Path


def analyze_audit_report(report_path):
    """分析审核报告的结果"""
    print(f"📊 分析审核报告: {os.path.basename(report_path)}")
    print("=" * 60)
    
    try:
        with open(report_path, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        # 1. 基本统计
        summary = report.get('summary', {})
        total = summary.get('total_rules_checked', 0)
        passed = summary.get('passed_count', 0)
        failed = summary.get('failed_count', 0)
        warning = summary.get('warning_count', 0)
        
        print(f"📈 审核统计:")
        print(f"   总规则数: {total}")
        print(f"   ✅ 通过: {passed} ({passed/total*100:.1f}%)")
        print(f"   ⚠️ 警告: {warning} ({warning/total*100:.1f}%)")
        print(f"   ❌ 失败: {failed} ({failed/total*100:.1f}%)")
        
        # 2. 分析通过的规则
        details = report.get('details', [])
        passed_rules = [d for d in details if d.get('status') == 'PASS']
        failed_rules = [d for d in details if d.get('status') == 'FAIL']
        warning_rules = [d for d in details if d.get('status') == 'WARNING']
        
        print(f"\n✅ 通过的规则 ({len(passed_rules)}条):")
        for rule in passed_rules[:5]:  # 显示前5条
            rule_id = rule.get('rule_id', '').split('：')[0]
            print(f"   - {rule_id}")
        if len(passed_rules) > 5:
            print(f"   ... 还有 {len(passed_rules) - 5} 条")
        
        print(f"\n❌ 失败的规则 ({len(failed_rules)}条):")
        for rule in failed_rules:
            rule_id = rule.get('rule_id', '').split('：')[0]
            message = rule.get('message', '')[:50] + '...' if len(rule.get('message', '')) > 50 else rule.get('message', '')
            print(f"   - {rule_id}: {message}")
        
        print(f"\n⚠️ 警告的规则 ({len(warning_rules)}条):")
        for rule in warning_rules[:3]:  # 显示前3条
            rule_id = rule.get('rule_id', '').split('：')[0]
            message = rule.get('message', '')[:50] + '...' if len(rule.get('message', '')) > 50 else rule.get('message', '')
            print(f"   - {rule_id}: {message}")
        if len(warning_rules) > 3:
            print(f"   ... 还有 {len(warning_rules) - 3} 条")
        
        # 3. 健康度评估
        print(f"\n🏥 审核健康度评估:")
        if passed/total >= 0.7:
            print("   🟢 健康 - 大部分规则通过")
        elif passed/total >= 0.5:
            print("   🟡 一般 - 约一半规则通过")
        elif passed/total >= 0.3:
            print("   🟠 需要关注 - 通过率较低")
        else:
            print("   🔴 严重问题 - 通过率很低")
        
        # 4. 元数据信息
        metadata = report.get('audit_metadata', {})
        print(f"\n📋 审核元数据:")
        print(f"   版本: {metadata.get('version', 'N/A')}")
        print(f"   类型: {metadata.get('audit_type', 'N/A')}")
        print(f"   时间: {metadata.get('timestamp', 'N/A')}")
        print(f"   文档编号: {metadata.get('document_number', 'N/A')}")
        
        return {
            'total': total,
            'passed': passed,
            'failed': failed,
            'warning': warning,
            'pass_rate': passed/total if total > 0 else 0,
            'health_status': 'healthy' if passed/total >= 0.7 else 'needs_attention'
        }
        
    except Exception as e:
        print(f"❌ 分析报告失败: {e}")
        return None


def compare_reports(old_report_path, new_report_path):
    """比较两个审核报告的差异"""
    print(f"\n🔄 比较审核报告")
    print("=" * 60)
    
    try:
        # 读取旧报告
        with open(old_report_path, 'r', encoding='utf-8') as f:
            old_report = json.load(f)
        
        # 读取新报告
        with open(new_report_path, 'r', encoding='utf-8') as f:
            new_report = json.load(f)
        
        old_summary = old_report.get('summary', {})
        new_summary = new_report.get('summary', {})
        
        print(f"📊 统计对比:")
        print(f"   {'指标':<12} {'修复前':<8} {'修复后':<8} {'变化':<10}")
        print(f"   {'-'*12} {'-'*8} {'-'*8} {'-'*10}")
        
        old_passed = old_summary.get('passed_count', 0)
        new_passed = new_summary.get('passed_count', 0)
        old_failed = old_summary.get('failed_count', 0)
        new_failed = new_summary.get('failed_count', 0)
        old_warning = old_summary.get('warning_count', 0)
        new_warning = new_summary.get('warning_count', 0)
        
        print(f"   {'通过':<12} {old_passed:<8} {new_passed:<8} {'+' if new_passed > old_passed else ''}{new_passed - old_passed}")
        print(f"   {'失败':<12} {old_failed:<8} {new_failed:<8} {'+' if new_failed > old_failed else ''}{new_failed - old_failed}")
        print(f"   {'警告':<12} {old_warning:<8} {new_warning:<8} {'+' if new_warning > old_warning else ''}{new_warning - old_warning}")
        
        # 计算改善程度
        old_total = old_summary.get('total_rules_checked', 0)
        new_total = new_summary.get('total_rules_checked', 0)
        
        if old_total > 0 and new_total > 0:
            old_pass_rate = old_passed / old_total
            new_pass_rate = new_passed / new_total
            improvement = new_pass_rate - old_pass_rate
            
            print(f"\n📈 通过率对比:")
            print(f"   修复前: {old_pass_rate:.1%}")
            print(f"   修复后: {new_pass_rate:.1%}")
            print(f"   改善: {'+' if improvement > 0 else ''}{improvement:.1%}")
            
            if improvement > 0.3:
                print("   🎉 显著改善！")
            elif improvement > 0.1:
                print("   ✅ 明显改善")
            elif improvement > 0:
                print("   📈 有所改善")
            else:
                print("   ⚠️ 需要进一步优化")
        
    except Exception as e:
        print(f"❌ 比较报告失败: {e}")


def main():
    """主函数"""
    print("🧪 V2审核系统结果验证测试")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    reports_dir = project_root / "audit_reports"
    
    # 查找报告文件
    doc_report = reports_dir / "audit_report_ZDBXD2025042900003.json"
    v2_report = reports_dir / "audit_report_v2.json"
    default_report = reports_dir / "audit_report_default.json"
    
    # 分析最新的文档特定报告
    if doc_report.exists():
        result = analyze_audit_report(doc_report)
        
        # 如果存在其他报告，进行比较
        if v2_report.exists():
            compare_reports(v2_report, doc_report)
        elif default_report.exists():
            compare_reports(default_report, doc_report)
    else:
        print("❌ 未找到文档特定的审核报告")
        
        # 分析其他可用报告
        if v2_report.exists():
            analyze_audit_report(v2_report)
        elif default_report.exists():
            analyze_audit_report(default_report)
        else:
            print("❌ 未找到任何审核报告")
    
    print(f"\n💡 建议:")
    print(f"   1. 通过率达到50%以上表示修复有效")
    print(f"   2. 失败的规则主要是缺少特定附件（如餐饮小票、支付记录）")
    print(f"   3. 警告的规则通常是数据不完整或无法判断的情况")
    print(f"   4. 可以通过完善测试数据进一步提高通过率")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
前端修复测试脚本
用于验证AI分析引擎显示和规则引擎进度跟踪的修复效果
"""

import json
import time
import threading
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser

class TestAPIHandler(BaseHTTPRequestHandler):
    """测试API处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/state':
            # 返回测试状态数据
            state_data = self.get_test_state()
            self.wfile.write(json.dumps(state_data, ensure_ascii=False).encode('utf-8'))
            
        elif parsed_path.path == '/api/status':
            # 返回兼容的状态数据
            state_data = self.get_test_state()
            status_data = {
                "current_step": state_data["current_phase"],
                "status": "running" if state_data["audit_status"] == "running" else "completed",
                "message": state_data["message"],
                "timestamp": state_data["last_updated"],
                "detail": state_data["detail"],
                "ai_thinking": state_data["ai_thinking"],
                "final_stats": state_data["summary"],
                "progress_percent": state_data["progress_percent"]
            }
            self.wfile.write(json.dumps(status_data, ensure_ascii=False).encode('utf-8'))
            
        elif parsed_path.path == '/api/report':
            # 返回测试报告数据
            report_data = self.get_test_report()
            self.wfile.write(json.dumps(report_data, ensure_ascii=False).encode('utf-8'))
            
        else:
            self.wfile.write(b'{"error": "Not found"}')
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def get_test_state(self):
        """获取测试状态数据"""
        # 模拟不同阶段的状态
        current_time = time.time()
        phase_cycle = int(current_time / 10) % 4  # 每10秒切换一个阶段
        
        phases = [
            {
                "current_phase": "attachment-check",
                "progress_percent": 20,
                "ai_thinking": """## 🔍 附件完整性检查 (阶段 1/4)

### 📋 检查进展

正在进行第一阶段的附件完整性验证，确保所有必需文件都已提供。

### 🤖 AI分析过程

**步骤1：文件清单验证**
- ✅ 发票文件已找到
- ✅ 审批表已验证
- ✅ 支付记录已确认
- 🔄 正在检查小票文件...

**步骤2：文件格式检查**
- ✅ PDF格式验证通过
- ✅ 图片清晰度检查通过
- 🔄 正在进行OCR文本识别...

### 📊 当前状态
- **已完成规则**: 3/5
- **通过规则**: 3条
- **警告规则**: 0条
- **失败规则**: 0条

### 🔄 下一步骤
即将开始字段内容与一致性检查阶段...""",
                "message": "AI正在分析: 附件完整性检查",
                "detail": "已完成3/5条规则检查，当前阶段进度20%"
            },
            {
                "current_phase": "field-consistency", 
                "progress_percent": 60,
                "ai_thinking": """## 🔍 字段内容与一致性检查 (阶段 2/4)

### 📋 当前分析进展

正在进行第二阶段的深度分析，已完成附件完整性检查，现在开始字段内容与一致性验证。

### 🤖 AI分析过程

**步骤1：数据结构验证**
- ✅ 表单数据结构完整
- ✅ 附件数据关联正确
- ✅ 关键字段格式验证通过

**步骤2：一致性检查**
- ✅ 金额字段一致性验证通过
- ✅ 日期逻辑关系检查通过
- 🔄 正在分析业务流程合规性...

**步骤3：智能推理**
- 🧠 应用业务规则推理引擎
- 🧠 执行语义理解分析
- 🧠 生成风险评估建议

### 📊 当前状态
- **已完成规则**: 24/38
- **通过规则**: 22条
- **警告规则**: 2条
- **失败规则**: 0条

### 🔄 下一步骤
即将开始金额与标准检查阶段...""",
                "message": "AI正在分析: 字段内容与一致性检查",
                "detail": "已完成24/38条规则检查，当前阶段进度60%"
            },
            {
                "current_phase": "amount-standard",
                "progress_percent": 80,
                "ai_thinking": """## 🔍 金额与标准检查 (阶段 3/4)

### 📋 检查进展

正在进行第三阶段的金额与标准验证，重点关注预算控制和消费合理性。

### 🤖 AI分析过程

**步骤1：预算范围验证**
- ✅ 预算额度检查通过
- ✅ 消费类别匹配正确
- 🔄 正在验证标准限额...

**步骤2：金额合理性分析**
- ✅ 单项消费金额合理
- ✅ 总体消费水平适中
- 🔄 正在进行同类对比分析...

**步骤3：标准符合性检查**
- 🧠 应用行业标准规则
- 🧠 执行政策符合性验证
- 🧠 生成合规性评估

### 📊 当前状态
- **已完成规则**: 30/38
- **通过规则**: 28条
- **警告规则**: 2条
- **失败规则**: 0条

### 🔄 下一步骤
即将开始八项规定合规性检查...""",
                "message": "AI正在分析: 金额与标准检查",
                "detail": "已完成30/38条规则检查，当前阶段进度80%"
            },
            {
                "current_phase": "compliance-check",
                "progress_percent": 100,
                "ai_thinking": """## 🔍 八项规定合规性检查 (阶段 4/4)

### 📋 最终检查完成

已完成所有四个阶段的审核检查，正在生成最终审核报告。

### 🤖 AI分析过程

**步骤1：合规性全面检查**
- ✅ 八项规定符合性验证通过
- ✅ 政策遵循度检查通过
- ✅ 风险识别分析完成

**步骤2：综合评估**
- ✅ 所有规则检查完成
- ✅ 风险等级评估完成
- ✅ 合规评分计算完成

**步骤3：报告生成**
- ✅ 审核结果汇总完成
- ✅ 建议措施生成完成
- ✅ 最终报告已生成

### 📊 最终状态
- **已完成规则**: 38/38
- **通过规则**: 36条
- **警告规则**: 2条
- **失败规则**: 0条

### 🎉 审核完成
所有审核流程已完成，审核报告已生成！""",
                "message": "AI分析完成: 八项规定合规性检查",
                "detail": "已完成38/38条规则检查，审核流程100%完成"
            }
        ]
        
        phase_data = phases[phase_cycle]
        
        return {
            "audit_id": "ZDBXD2025042900003",
            "audit_status": "completed" if phase_cycle == 3 else "running",
            "current_phase": phase_data["current_phase"],
            "progress_percent": phase_data["progress_percent"],
            "start_time": "2025-07-28T08:44:32Z",
            "completion_time": time.strftime("%Y-%m-%dT%H:%M:%SZ") if phase_cycle == 3 else None,
            "summary": {
                "total_rules": 38,
                "completed_rules": min(38, 5 + phase_cycle * 8),
                "passed_rules": min(36, 5 + phase_cycle * 8 - 2),
                "failed_rules": 0,
                "warning_rules": 2 if phase_cycle > 0 else 0
            },
            "ai_thinking": phase_data["ai_thinking"],
            "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "message": phase_data["message"],
            "detail": phase_data["detail"]
        }
    
    def get_test_report(self):
        """获取测试报告数据"""
        return {
            "summary": {
                "total_rules_checked": 38,
                "passed_count": 36,
                "failed_count": 0,
                "warning_count": 2
            },
            "details": [
                {
                    "rule_id": "规则1",
                    "description": "发票完整性检查",
                    "status": "PASS",
                    "message": "发票文件完整，格式正确"
                },
                {
                    "rule_id": "规则2", 
                    "description": "金额一致性检查",
                    "status": "WARNING",
                    "message": "金额存在小数点精度差异，建议核实"
                }
            ]
        }

def start_test_server():
    """启动测试服务器"""
    server = HTTPServer(('localhost', 8001), TestAPIHandler)
    server_thread = threading.Thread(target=server.serve_forever, daemon=True)
    server_thread.start()
    print("✅ 测试API服务器已启动: http://localhost:8001")
    return server

def main():
    """主函数"""
    print("🧪 前端修复测试工具")
    print("=" * 60)
    
    # 启动测试服务器
    server = start_test_server()
    
    # 打开前端页面
    frontend_url = "http://localhost:8001/../frontend/ai_console.html?doc_num=ZDBXD2025042900003"
    print(f"🌐 正在打开前端页面: {frontend_url}")
    
    try:
        webbrowser.open(frontend_url)
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动访问: {frontend_url}")
    
    print("\n📋 测试说明:")
    print("1. 页面会每10秒自动切换不同的审核阶段")
    print("2. 观察AI分析引擎是否正确显示思考过程")
    print("3. 检查规则引擎进度条是否准确更新")
    print("4. 验证统计数据是否正确显示")
    
    print("\n⌨️ 按 Ctrl+C 停止测试服务器")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 测试服务器已停止")
        server.shutdown()

if __name__ == "__main__":
    main()

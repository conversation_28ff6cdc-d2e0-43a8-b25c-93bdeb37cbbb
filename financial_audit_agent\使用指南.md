# 🚀 AI财务审核系统使用指南

## 📋 系统简介

AI财务审核系统是一个基于千问大模型的智能财务审核工具，专门用于业务招待费等费用的自动化审核。

## 🎯 唯一启动入口

系统已简化为**单一启动入口**，避免混淆：

```bash
# 基本启动（推荐）
python start.py --doc-num ZDBXD2025042900003

# 不自动打开浏览器
python start.py --doc-num ZDBXD2025042900003 --no-browser
```

## 📁 核心文件说明

```
financial_audit_agent/
├── start.py                    # 🚀 主启动脚本（唯一入口）
├── reset_audit_status.py       # 🔄 状态重置脚本（可选）
├── README.md                   # 📖 项目说明
├── 使用指南.md                  # 📋 本文件
├── 业务招待费审核规则_V2.txt     # 📜 审核规则文件
├── backend/                    # 🔧 后端核心代码
├── frontend/                   # 🌐 前端界面文件
└── audit_reports/              # 📊 审核报告输出目录
```

## 🔧 配置要求

### 1. Python环境
- Python 3.8 或更高版本
- 安装依赖：`pip install -r backend/requirements.txt`

### 2. API配置
编辑 `backend/config.json`：
```json
{
  "LLM_API_KEY": "your-qwen-api-key",
  "LLM_MODEL_NAME": "qwen-max"
}
```

### 3. 数据文件
确保以下文件存在：
- `C:\Users\<USER>\Desktop\测试附件\测试记录\{doc_num}\表单提取.json`
- `C:\Users\<USER>\Desktop\测试附件\测试记录\{doc_num}\附件提取.json`

## 🚀 启动流程

### 第1步：启动系统
```bash
python start.py --doc-num ZDBXD2025042900003
```

### 第2步：观察进度
- 系统会自动打开浏览器
- 访问地址：`http://localhost:8002/frontend/ai_console.html`
- 实时查看审核进度和AI思考过程

### 第3步：获取结果
- 审核完成后，报告保存在 `audit_reports/` 目录
- 文件名格式：`audit_report_{doc_num}.json`

## 📊 系统功能

### ✨ 核心特性
- 🤖 **智能审核**：基于千问大模型的20+条规则检查
- 📈 **实时进度**：Web界面实时显示审核进度
- 🧠 **AI思考**：完整展示大模型的推理过程
- 📋 **详细报告**：生成完整的审核结果报告

### 🔍 审核阶段
1. **第一部分**：附件完整性检查（5条规则）
2. **第二部分**：字段内容与一致性检查
3. **第三部分**：金额与标准检查（5条规则）
4. **第四部分**：八项规定合规性检查（7条规则）

## 🛠️ 故障排除

### 常见问题

**Q: 启动时提示端口被占用？**
A: 系统会自动使用端口8002，如被占用请关闭占用该端口的程序。

**Q: 审核过程中出现编码乱码？**
A: 已修复！新版启动脚本已解决Windows控制台中文显示问题。

**Q: 找不到数据文件？**
A: 确保数据文件路径正确，文档编号与实际文件夹名称一致。

**Q: API调用失败？**
A: 检查 `backend/config.json` 中的API密钥配置是否正确。

### 重置系统
如遇异常，可重置系统状态：
```bash
python reset_audit_status.py
```

## 📞 技术支持

### 系统要求
- ✅ Windows 10/11
- ✅ Python 3.8+
- ✅ 稳定的网络连接
- ✅ 千问大模型API访问权限

### 注意事项
- 🚫 **不要同时运行多个启动脚本**
- 🚫 **不要手动修改状态文件**
- ✅ **只使用 `start.py` 作为启动入口**
- ✅ **确保数据文件格式正确**

## 🎉 使用建议

1. **首次使用**：建议先用测试文档编号验证系统功能
2. **正式使用**：确保数据文件完整且格式正确
3. **结果查看**：审核完成后及时查看生成的报告文件
4. **问题反馈**：遇到问题时保留完整的控制台输出日志

---

**享受AI驱动的智能审核体验！** 🎯

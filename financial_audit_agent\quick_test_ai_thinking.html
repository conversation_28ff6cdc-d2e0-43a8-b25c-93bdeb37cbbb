<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI思维链快速测试</title>
    <link rel="stylesheet" href="frontend/ai_results.css">
    <style>
        body {
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f3a 100%);
            color: #e2e8f0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .quick-test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(26, 31, 58, 0.4);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4ff;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #8b5cf6, #00d4ff);
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.5);
        }
        
        .debug-info {
            background: rgba(42, 47, 74, 0.4);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 215, 0, 0.2);
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
        }
        
        .debug-info h3 {
            color: #ffd700;
            margin-top: 0;
        }
        
        .status-indicator {
            padding: 10px 20px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
            border: 1px solid #00ff88;
        }
        
        .status-error {
            background: rgba(255, 107, 53, 0.2);
            color: #ff6b35;
            border: 1px solid #ff6b35;
        }
        
        .status-info {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            border: 1px solid #00d4ff;
        }
    </style>
</head>
<body>
    <div class="quick-test-container">
        <div class="test-header">
            <h1>🧠 AI思维链快速测试</h1>
            <p>直接测试AI思维链功能的核心组件</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="loadTestData()">加载测试数据</button>
            <button class="test-btn" onclick="testButtonDisplay()">测试按钮显示</button>
            <button class="test-btn" onclick="testThinkingChain()">测试思维链功能</button>
            <button class="test-btn" onclick="clearDebugInfo()">清除调试信息</button>
        </div>
        
        <div id="debug-info" class="debug-info">
            <h3>🔧 调试信息</h3>
            <div id="debug-content">点击上方按钮开始测试...</div>
        </div>
        
        <!-- AI分析洞察 -->
        <section class="ai-insights" id="ai-insights">
            <div class="insights-header">
                <h3>🧠 AI智能洞察</h3>
                <div class="neural-indicator">
                    <div class="neural-dot"></div>
                    <span>神经网络分析</span>
                </div>
                <button class="thinking-chain-btn" id="thinking-chain-btn" style="display: none;">
                    <span class="btn-icon">🔍</span>
                    <span class="btn-text">查看AI思考过程</span>
                    <div class="btn-glow"></div>
                </button>
            </div>
            <div class="insights-content" id="insights-content">
                <div class="insight-loading">
                    <div class="loading-brain">
                        <div class="brain-wave"></div>
                        <div class="brain-wave"></div>
                        <div class="brain-wave"></div>
                    </div>
                    <p>等待测试数据加载...</p>
                </div>
            </div>
        </section>

        <!-- AI思考过程展示区域 -->
        <section class="ai-thinking-chain" id="ai-thinking-chain" style="display: none;">
            <div class="thinking-header">
                <h3>🤖 AI思考过程</h3>
                <div class="thinking-controls">
                    <button class="control-btn" id="expand-all-btn">
                        <span class="btn-icon">📖</span>
                        <span class="btn-text">展开全部</span>
                    </button>
                    <button class="control-btn" id="collapse-all-btn">
                        <span class="btn-icon">📚</span>
                        <span class="btn-text">收起全部</span>
                    </button>
                    <button class="control-btn" id="copy-thinking-btn">
                        <span class="btn-icon">📋</span>
                        <span class="btn-text">复制内容</span>
                    </button>
                    <button class="control-btn" id="close-thinking-btn">
                        <span class="btn-icon">✖️</span>
                        <span class="btn-text">关闭</span>
                    </button>
                </div>
            </div>
            
            <div class="thinking-search">
                <div class="search-container">
                    <input type="text" id="thinking-search-input" placeholder="搜索AI思考内容..." class="search-input">
                    <button class="search-btn" id="thinking-search-btn">🔍</button>
                </div>
                <div class="search-results" id="search-results"></div>
            </div>
            
            <div class="thinking-content" id="thinking-content">
                <div class="thinking-loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载AI思考过程...</p>
                </div>
            </div>
            
            <div class="thinking-metadata" id="thinking-metadata" style="display: none;">
                <div class="metadata-title">📊 思维链元数据</div>
                <div class="metadata-content" id="metadata-content"></div>
            </div>
        </section>
    </div>

    <script>
        // 全局变量
        let testAuditData = null;
        let testViewer = null;

        // 调试信息显示
        function addDebugInfo(message, type = 'info') {
            const debugContent = document.getElementById('debug-content');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 'status-info';
            
            const newInfo = document.createElement('div');
            newInfo.className = `status-indicator ${statusClass}`;
            newInfo.innerHTML = `[${timestamp}] ${message}`;
            
            debugContent.appendChild(newInfo);
            debugContent.scrollTop = debugContent.scrollHeight;
        }

        // 加载测试数据
        async function loadTestData() {
            addDebugInfo('开始加载测试数据...', 'info');
            
            try {
                const response = await fetch('audit_reports/audit_report_123.json');
                if (response.ok) {
                    testAuditData = await response.json();
                    addDebugInfo('✅ 成功加载 audit_report_123.json', 'success');
                    addDebugInfo(`数据包含AI思维链: ${!!testAuditData.ai_thinking_chain}`, 'info');
                    
                    if (testAuditData.ai_thinking_chain) {
                        const aiThinking = testAuditData.ai_thinking_chain;
                        addDebugInfo(`组合思维链长度: ${aiThinking.combined_thinking?.length || 0}`, 'info');
                        addDebugInfo(`阶段历史数量: ${Object.keys(aiThinking.phases_history || {}).length}`, 'info');
                        addDebugInfo(`元数据存在: ${!!aiThinking.extraction_metadata}`, 'info');
                    }
                    
                    // 更新洞察内容
                    document.getElementById('insights-content').innerHTML = `
                        <div style="padding: 20px; text-align: center;">
                            <h4 style="color: #00d4ff; margin-bottom: 15px;">✅ 测试数据加载完成</h4>
                            <p style="color: #94a3b8;">AI思维链数据已准备就绪</p>
                        </div>
                    `;
                } else {
                    addDebugInfo('❌ 加载audit_report_123.json失败', 'error');
                }
            } catch (error) {
                addDebugInfo(`❌ 加载过程出错: ${error.message}`, 'error');
            }
        }

        // 测试按钮显示
        function testButtonDisplay() {
            addDebugInfo('开始测试按钮显示逻辑...', 'info');
            
            const button = document.getElementById('thinking-chain-btn');
            addDebugInfo(`按钮元素存在: ${!!button}`, 'info');
            
            if (testAuditData) {
                addDebugInfo('测试数据已加载', 'success');
                addDebugInfo(`AI思维链数据存在: ${!!testAuditData.ai_thinking_chain}`, 'info');
                
                if (testAuditData.ai_thinking_chain) {
                    button.style.display = 'flex';
                    button.style.animation = 'fadeIn 0.5s ease-out';
                    addDebugInfo('✅ 按钮已显示', 'success');
                    addDebugInfo(`按钮display样式: ${button.style.display}`, 'info');
                } else {
                    addDebugInfo('❌ 无AI思维链数据，按钮不显示', 'error');
                }
            } else {
                addDebugInfo('❌ 请先加载测试数据', 'error');
            }
        }

        // 测试思维链功能
        function testThinkingChain() {
            addDebugInfo('开始测试思维链功能...', 'info');
            
            if (!testAuditData || !testAuditData.ai_thinking_chain) {
                addDebugInfo('❌ 请先加载包含AI思维链的测试数据', 'error');
                return;
            }
            
            // 显示思维链区域
            const thinkingSection = document.getElementById('ai-thinking-chain');
            thinkingSection.style.display = 'block';
            thinkingSection.scrollIntoView({ behavior: 'smooth' });
            
            addDebugInfo('✅ 思维链区域已显示', 'success');
            
            // 模拟加载内容
            setTimeout(() => {
                loadThinkingContent();
            }, 1000);
        }

        // 加载思维链内容
        function loadThinkingContent() {
            const thinkingContent = document.getElementById('thinking-content');
            const aiThinking = testAuditData.ai_thinking_chain;
            
            let contentHTML = '';
            
            // 显示组合思维链
            if (aiThinking.combined_thinking) {
                contentHTML += `
                    <div class="combined-thinking">
                        <div class="combined-thinking-title">
                            🧠 完整思维链概览
                        </div>
                        <div class="combined-thinking-content">${escapeHtml(aiThinking.combined_thinking)}</div>
                    </div>
                `;
            }
            
            // 显示各阶段思维链
            if (aiThinking.phases_history) {
                const phases = Object.entries(aiThinking.phases_history);
                
                if (phases.length > 0) {
                    contentHTML += '<div class="phases-container">';
                    
                    phases.forEach(([phaseKey, phaseData], index) => {
                        const phaseNumber = index + 1;
                        const phaseName = phaseData.phase_name || phaseKey;
                        const status = phaseData.status || 'unknown';
                        const timestamp = phaseData.timestamp || '';
                        const thinking = phaseData.ai_thinking || '无思考内容';
                        
                        contentHTML += `
                            <div class="thinking-phase" data-phase="${phaseKey}">
                                <div class="phase-header" onclick="togglePhase(this)">
                                    <div class="phase-title">
                                        阶段 ${phaseNumber}: ${escapeHtml(phaseName)}
                                    </div>
                                    <div class="phase-meta">
                                        <span class="phase-status ${status}">${getStatusText(status)}</span>
                                        <span class="phase-time">${formatTimestamp(timestamp)}</span>
                                        <span class="phase-expand-icon">▼</span>
                                    </div>
                                </div>
                                <div class="phase-content">
                                    <div class="thinking-text">${escapeHtml(thinking)}</div>
                                </div>
                            </div>
                        `;
                    });
                    
                    contentHTML += '</div>';
                }
            }
            
            thinkingContent.innerHTML = contentHTML;
            addDebugInfo('✅ 思维链内容已加载', 'success');
        }

        // 切换阶段展开/收起
        function togglePhase(header) {
            const content = header.parentElement.querySelector('.phase-content');
            content.classList.toggle('expanded');
            header.classList.toggle('expanded');
        }

        // 辅助函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function getStatusText(status) {
            const statusMap = {
                'completed': '已完成',
                'running': '进行中',
                'failed': '失败',
                'unknown': '未知'
            };
            return statusMap[status] || status;
        }

        function formatTimestamp(timestamp) {
            if (!timestamp) return '';
            try {
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN');
            } catch (error) {
                return timestamp;
            }
        }

        function clearDebugInfo() {
            document.getElementById('debug-content').innerHTML = '调试信息已清除...';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            addDebugInfo('页面加载完成，准备测试...', 'success');
        });
    </script>
</body>
</html>

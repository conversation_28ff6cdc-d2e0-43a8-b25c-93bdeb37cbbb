<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI思维链测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .thinking-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .thinking-content {
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 AI思维链测试页面</h1>
        
        <div class="status info">
            <strong>测试目的：</strong>验证AI思维链是否能正确从API获取并显示
        </div>
        
        <button onclick="testAPIConnection()">测试API连接</button>
        <button onclick="fetchAIThinking()">获取AI思维链</button>
        <button onclick="clearDisplay()">清空显示</button>
        
        <div id="status-display"></div>
        
        <div class="thinking-section">
            <h3>🔍 AI思维链内容</h3>
            <div id="thinking-display" class="thinking-content">
                点击"获取AI思维链"按钮开始测试...
            </div>
        </div>
        
        <div class="thinking-section">
            <h3>📊 原始API响应</h3>
            <div id="raw-response" class="thinking-content">
                等待API响应...
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8001';
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function testAPIConnection() {
            showStatus('🔍 测试API连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`);
                if (response.ok) {
                    const data = await response.json();
                    showStatus(`✅ API连接成功！服务状态: ${data.status}`, 'success');
                } else {
                    showStatus(`❌ API连接失败！状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ API连接异常: ${error.message}`, 'error');
            }
        }
        
        async function fetchAIThinking() {
            showStatus('🧠 获取AI思维链...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/status`);
                if (response.ok) {
                    const data = await response.json();
                    
                    // 显示原始响应
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                    
                    // 提取并显示AI思维链
                    const aiThinking = data.ai_thinking || '未找到AI思维链内容';
                    document.getElementById('thinking-display').textContent = aiThinking;
                    
                    if (data.ai_thinking) {
                        showStatus(`✅ AI思维链获取成功！长度: ${data.ai_thinking.length} 字符`, 'success');
                        
                        // 分析思维链内容
                        const phases = (data.ai_thinking.match(/## 🔍/g) || []).length;
                        if (phases > 0) {
                            showStatus(`✅ 发现 ${phases} 个审核阶段的思维链！`, 'success');
                        }
                    } else {
                        showStatus('⚠️ API响应中没有ai_thinking字段', 'error');
                    }
                } else {
                    showStatus(`❌ 获取状态失败！状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 获取AI思维链异常: ${error.message}`, 'error');
            }
        }
        
        function clearDisplay() {
            document.getElementById('thinking-display').textContent = '已清空...';
            document.getElementById('raw-response').textContent = '已清空...';
            document.getElementById('status-display').innerHTML = '';
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            showStatus('📋 页面已加载，准备测试AI思维链功能', 'info');
        };
    </script>
</body>
</html>

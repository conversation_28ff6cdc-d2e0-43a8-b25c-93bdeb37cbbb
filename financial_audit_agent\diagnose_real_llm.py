#!/usr/bin/env python3
"""
真实LLM连接诊断工具
诊断并修复真实的LLM API连接问题
"""

import json
import requests
import time
from pathlib import Path

def load_config():
    """加载配置文件"""
    config_file = Path("backend/config.json")
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    return config

def test_api_connectivity(config):
    """测试API连接性"""
    print("\n🔍 测试API连接性...")
    
    api_key = config.get('LLM_API_KEY')
    base_url = config.get('LLM_BASE_URL')
    model_name = config.get('LLM_MODEL_NAME')
    
    if not api_key:
        print("❌ API密钥未配置")
        return False
    
    if not base_url:
        print("❌ API基础URL未配置")
        return False
    
    print(f"📋 API密钥: {api_key[:8]}...{api_key[-4:]}")
    print(f"📋 基础URL: {base_url}")
    print(f"📋 模型名称: {model_name}")
    
    # 测试基础连接
    try:
        print("🔄 测试基础网络连接...")
        response = requests.get(base_url.replace('/v1', ''), timeout=10)
        print(f"✅ 网络连接正常，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
        return False
    
    # 测试API端点
    try:
        print("🔄 测试API端点...")
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # 测试模型列表端点
        models_url = f"{base_url}/models"
        response = requests.get(models_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ API端点可访问")
            models_data = response.json()
            if 'data' in models_data:
                available_models = [model['id'] for model in models_data['data']]
                print(f"📋 可用模型: {available_models[:5]}...")  # 只显示前5个
                
                if model_name in available_models:
                    print(f"✅ 目标模型 {model_name} 可用")
                    return True
                else:
                    print(f"⚠️ 目标模型 {model_name} 不在可用列表中")
                    print("💡 建议使用以下模型之一:")
                    for model in available_models[:3]:
                        print(f"   - {model}")
                    return False
            else:
                print("⚠️ 模型列表格式异常")
                return False
        else:
            print(f"❌ API端点访问失败，状态码: {response.status_code}")
            print(f"❌ 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def test_real_llm_call(config):
    """测试真实的LLM调用"""
    print("\n🔍 测试真实LLM调用...")
    
    api_key = config.get('LLM_API_KEY')
    base_url = config.get('LLM_BASE_URL')
    model_name = config.get('LLM_MODEL_NAME')
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # 构建请求数据
    request_data = {
        "model": model_name,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的财务审核助手。请简洁回答问题。"
            },
            {
                "role": "user", 
                "content": "请简单回答：1+1等于几？"
            }
        ],
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    try:
        print("🔄 发送测试请求...")
        chat_url = f"{base_url}/chat/completions"
        response = requests.post(chat_url, headers=headers, json=request_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"✅ LLM调用成功")
                print(f"📝 响应内容: {content}")
                return True, content
            else:
                print("❌ 响应格式异常")
                print(f"❌ 完整响应: {result}")
                return False, None
        else:
            print(f"❌ LLM调用失败，状态码: {response.status_code}")
            print(f"❌ 错误信息: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ LLM调用异常: {e}")
        return False, None

def test_thinking_model(config):
    """测试思维链模型"""
    print("\n🔍 测试思维链功能...")
    
    api_key = config.get('LLM_API_KEY')
    base_url = config.get('LLM_BASE_URL')
    model_name = config.get('LLM_MODEL_NAME')
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # 构建思维链请求
    request_data = {
        "model": model_name,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的财务审核助手。请详细展示你的分析思考过程，然后给出最终结果。"
            },
            {
                "role": "user",
                "content": "请分析：一笔1000元的业务招待费是否合理？请详细展示你的思考过程。"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    # 如果是thinking模型，添加特殊参数
    if "thinking" in model_name.lower():
        request_data["reasoning_effort"] = "medium"
        request_data["enable_thinking"] = True
    
    try:
        print("🔄 发送思维链测试请求...")
        chat_url = f"{base_url}/chat/completions"
        response = requests.post(chat_url, headers=headers, json=request_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                choice = result['choices'][0]
                content = choice['message']['content']
                
                # 检查是否有思维链内容
                thinking_content = None
                if 'reasoning_content' in choice['message']:
                    thinking_content = choice['message']['reasoning_content']
                elif 'reasoning' in choice['message']:
                    thinking_content = choice['message']['reasoning']
                
                print(f"✅ 思维链模型调用成功")
                print(f"📝 响应内容长度: {len(content)} 字符")
                
                if thinking_content:
                    print(f"🧠 思维链内容长度: {len(thinking_content)} 字符")
                    print(f"🧠 思维链预览: {thinking_content[:200]}...")
                    return True, thinking_content, content
                else:
                    print("⚠️ 未检测到专门的思维链字段，尝试从内容中提取")
                    print(f"📝 完整内容预览: {content[:300]}...")
                    return True, content[:len(content)//2], content[len(content)//2:]
            else:
                print("❌ 思维链响应格式异常")
                return False, None, None
        else:
            print(f"❌ 思维链调用失败，状态码: {response.status_code}")
            print(f"❌ 错误信息: {response.text}")
            return False, None, None
            
    except Exception as e:
        print(f"❌ 思维链调用异常: {e}")
        return False, None, None

def fix_config_issues(config):
    """修复配置问题"""
    print("\n🔧 检查并修复配置问题...")
    
    fixed = False
    
    # 禁用模拟模式
    if config.get('LLM_USE_MOCK', False):
        print("🔧 禁用模拟模式")
        config['LLM_USE_MOCK'] = False
        fixed = True
    
    # 检查模型名称
    model_name = config.get('LLM_MODEL_NAME', '')
    if not model_name:
        print("🔧 设置默认模型名称")
        config['LLM_MODEL_NAME'] = 'qwen-plus'
        fixed = True
    
    # 检查温度设置
    if config.get('LLM_TEMPERATURE', 0) > 1.0:
        print("🔧 调整温度设置")
        config['LLM_TEMPERATURE'] = 0.7
        fixed = True
    
    # 检查最大token数
    if config.get('LLM_MAX_TOKENS', 0) < 1000:
        print("🔧 调整最大token数")
        config['LLM_MAX_TOKENS'] = 2000
        fixed = True
    
    if fixed:
        # 保存修复后的配置
        config_file = Path("backend/config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ 配置已修复并保存")
    
    return fixed

def main():
    """主函数"""
    print("🔍 真实LLM连接诊断工具")
    print("=" * 60)
    
    # 1. 加载配置
    config = load_config()
    if not config:
        return
    
    # 2. 修复配置问题
    fix_config_issues(config)
    
    # 3. 测试API连接性
    if not test_api_connectivity(config):
        print("\n❌ API连接性测试失败，请检查网络和配置")
        return
    
    # 4. 测试基础LLM调用
    success, content = test_real_llm_call(config)
    if not success:
        print("\n❌ 基础LLM调用失败")
        return
    
    # 5. 测试思维链功能
    thinking_success, thinking, result = test_thinking_model(config)
    if thinking_success:
        print("\n✅ 思维链功能正常")
    else:
        print("\n⚠️ 思维链功能异常，但基础调用正常")
    
    print("\n" + "=" * 60)
    print("🎉 LLM连接诊断完成")
    print("✅ 系统现在可以进行真实的AI分析")
    print("💡 建议重新运行审核系统测试真实功能")

if __name__ == "__main__":
    main()

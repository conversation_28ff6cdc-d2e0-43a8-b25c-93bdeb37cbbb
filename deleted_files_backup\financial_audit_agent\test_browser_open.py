#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器自动打开功能
"""

import os
import sys
import webbrowser
from pathlib import Path

def test_browser_open():
    """测试浏览器打开功能"""
    print("🧪 测试浏览器自动打开功能")
    print("=" * 40)
    
    # 测试URL
    test_url = "http://localhost:8002/frontend/ai_console.html"
    
    try:
        # 方法1：尝试使用Chrome
        print("\n📋 方法1: 尝试使用Chrome浏览器")
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
        ]
        
        chrome_path = None
        for path in chrome_paths:
            print(f"   检查路径: {path}")
            if os.path.exists(path):
                chrome_path = path
                print(f"   ✅ 找到Chrome: {path}")
                break
            else:
                print(f"   ❌ 不存在")
        
        if chrome_path:
            print(f"\n🚀 使用Chrome打开: {test_url}")
            try:
                import subprocess
                subprocess.run([chrome_path, "--new-window", test_url], check=False)
                print("✅ Chrome启动成功")
            except Exception as e:
                print(f"❌ Chrome启动失败: {e}")
                print("🔄 尝试使用默认浏览器...")
                webbrowser.open(test_url)
                print("✅ 默认浏览器启动成功")
        else:
            print("❌ 未找到Chrome浏览器")
            
            # 方法2：使用默认浏览器
            print("\n📋 方法2: 使用默认浏览器")
            webbrowser.open(test_url)
            print(f"✅ 默认浏览器启动: {test_url}")
            
    except Exception as e:
        print(f"❌ 浏览器打开失败: {e}")
        return False
    
    print(f"\n💡 如果浏览器没有打开，请手动访问: {test_url}")
    return True

def check_frontend_files():
    """检查前端文件是否存在"""
    print("\n🔍 检查前端文件")
    print("-" * 30)
    
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    
    if not frontend_dir.exists():
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return False
    
    print(f"✅ 前端目录存在: {frontend_dir}")
    
    # 检查关键文件
    key_files = [
        "ai_console.html",
        "ai_results.html", 
        "audit_viewer.html"
    ]
    
    for file_name in key_files:
        file_path = frontend_dir / file_name
        if file_path.exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} (不存在)")
    
    return True

def main():
    """主函数"""
    print("🚀 浏览器打开功能测试工具")
    print("=" * 50)
    
    # 1. 检查前端文件
    check_frontend_files()
    
    # 2. 测试浏览器打开
    test_browser_open()
    
    print("\n📊 测试完成")
    print("💡 如果浏览器成功打开但显示错误，可能需要先启动Web服务器")
    print("🌐 启动Web服务器命令: python simple_launcher.py")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时数据联动诊断</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 8px 0;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        .data-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .auto-refresh { background-color: #28a745; }
        .auto-refresh:hover { background-color: #1e7e34; }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 2px;
        }
        .log-success { background-color: #d4edda; }
        .log-error { background-color: #f8d7da; }
        .log-warning { background-color: #fff3cd; }
        .log-info { background-color: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 实时数据联动诊断工具</h1>
        
        <div class="section">
            <h3>📋 基本信息</h3>
            <div id="basic-info"></div>
        </div>
        
        <div class="section">
            <h3>🔗 API连接测试</h3>
            <div id="api-status" class="status info">检测中...</div>
            <button onclick="testAPIConnection()">重新测试API</button>
            <div id="api-details" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>📊 数据加载测试</h3>
            <div id="data-status" class="status info">等待测试...</div>
            <button onclick="testDataLoading()">测试数据加载</button>
            <button onclick="toggleAutoRefresh()" id="auto-btn">开启自动刷新</button>
            <div id="data-details" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>🧪 前端JavaScript测试</h3>
            <div id="js-status" class="status info">检测中...</div>
            <button onclick="testJavaScript()">测试JavaScript</button>
            <div id="js-details" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>📝 实时日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log-container" style="max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        let apiBaseUrl = null;
        let autoRefreshInterval = null;
        let docNum = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }

        // 显示基本信息
        function showBasicInfo() {
            const urlParams = new URLSearchParams(window.location.search);
            docNum = urlParams.get('doc_num');
            
            const info = `
当前URL: ${window.location.href}
文档编号: ${docNum || '未指定'}
时间戳: ${new Date().toLocaleString()}
用户代理: ${navigator.userAgent}
            `;
            document.getElementById('basic-info').textContent = info;
            log(`页面加载完成，文档编号: ${docNum || '未指定'}`);
        }

        // 测试API连接
        async function testAPIConnection() {
            log('开始测试API连接...');
            const statusElement = document.getElementById('api-status');
            const detailsElement = document.getElementById('api-details');
            
            statusElement.className = 'status info';
            statusElement.textContent = '🔍 检测API服务器...';
            
            const possiblePorts = [8001, 8002, 8003, 8004, 8005];
            let results = [];
            
            for (const port of possiblePorts) {
                try {
                    log(`尝试连接端口 ${port}...`);
                    const response = await fetch(`http://localhost:${port}/api/status`, {
                        method: 'GET',
                        signal: AbortSignal.timeout(3000)
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        apiBaseUrl = `http://localhost:${port}`;
                        results.push(`✅ 端口 ${port}: 连接成功`);
                        results.push(`   状态: ${data.status || 'unknown'}`);
                        results.push(`   消息: ${data.message || 'no message'}`);
                        log(`API服务器连接成功: ${apiBaseUrl}`, 'success');
                        break;
                    } else {
                        results.push(`❌ 端口 ${port}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ 端口 ${port}: ${error.message}`);
                    log(`端口 ${port} 连接失败: ${error.message}`, 'warning');
                }
            }
            
            if (apiBaseUrl) {
                statusElement.className = 'status success';
                statusElement.textContent = `✅ API连接成功: ${apiBaseUrl}`;
            } else {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ API连接失败';
                log('所有API端口连接失败', 'error');
            }
            
            detailsElement.textContent = results.join('\n');
        }

        // 测试数据加载
        async function testDataLoading() {
            if (!apiBaseUrl) {
                log('API未连接，无法测试数据加载', 'error');
                return;
            }
            
            log('开始测试数据加载...');
            const statusElement = document.getElementById('data-status');
            const detailsElement = document.getElementById('data-details');
            
            statusElement.className = 'status info';
            statusElement.textContent = '📊 加载数据中...';
            
            let results = [];
            
            // 测试各个API端点
            const endpoints = [
                { path: '/api/status', name: '状态信息' },
                { path: `/api/report${docNum ? `?doc_num=${docNum}` : ''}`, name: '审核报告' },
                { path: '/api/progress', name: '审核进度' },
                { path: '/api/rules', name: '规则信息' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    log(`测试端点: ${endpoint.path}`);
                    const response = await fetch(`${apiBaseUrl}${endpoint.path}`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        results.push(`✅ ${endpoint.name}: 加载成功`);
                        
                        // 显示关键数据
                        if (endpoint.path.includes('/api/report')) {
                            if (data.summary) {
                                results.push(`   总规则: ${data.summary.total_rules_checked || 0}`);
                                results.push(`   通过: ${data.summary.passed_count || 0}`);
                                results.push(`   失败: ${data.summary.failed_count || 0}`);
                                results.push(`   警告: ${data.summary.warning_count || 0}`);
                            }
                            if (data.metadata) {
                                results.push(`   数据源: ${data.metadata.source || 'unknown'}`);
                                results.push(`   时间戳: ${data.metadata.timestamp || 'unknown'}`);
                            }
                        } else if (endpoint.path === '/api/progress') {
                            results.push(`   总体进度: ${data.overall_progress || 0}%`);
                            results.push(`   当前阶段: ${data.current_phase || 'unknown'}`);
                        } else if (endpoint.path === '/api/rules') {
                            results.push(`   总规则数: ${data.total_rules || 0}`);
                        }
                        
                        log(`${endpoint.name} 加载成功`, 'success');
                    } else {
                        results.push(`❌ ${endpoint.name}: HTTP ${response.status}`);
                        log(`${endpoint.name} 加载失败: HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint.name}: ${error.message}`);
                    log(`${endpoint.name} 加载异常: ${error.message}`, 'error');
                }
            }
            
            statusElement.className = 'status success';
            statusElement.textContent = '✅ 数据加载测试完成';
            detailsElement.textContent = results.join('\n');
        }

        // 测试JavaScript功能
        function testJavaScript() {
            log('开始测试JavaScript功能...');
            const statusElement = document.getElementById('js-status');
            const detailsElement = document.getElementById('js-details');
            
            let results = [];
            
            // 检查全局对象
            results.push('=== 全局对象检查 ===');
            results.push(`window.aiConsole: ${typeof window.aiConsole}`);
            results.push(`window.AIConsole: ${typeof window.AIConsole}`);
            results.push(`window.AIConsoleEnhanced: ${typeof window.AIConsoleEnhanced}`);
            
            // 检查脚本加载
            results.push('\n=== 脚本文件检查 ===');
            const scripts = document.querySelectorAll('script[src]');
            scripts.forEach(script => {
                results.push(`脚本: ${script.src}`);
            });
            
            // 检查控制台错误
            results.push('\n=== 控制台检查 ===');
            results.push('请查看浏览器控制台(F12)获取详细错误信息');
            
            statusElement.className = 'status warning';
            statusElement.textContent = '⚠️ JavaScript检查完成，请查看详情';
            detailsElement.textContent = results.join('\n');
            
            log('JavaScript功能测试完成', 'info');
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const button = document.getElementById('auto-btn');
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                button.textContent = '开启自动刷新';
                button.className = '';
                log('自动刷新已停止');
            } else {
                autoRefreshInterval = setInterval(() => {
                    testDataLoading();
                }, 5000);
                button.textContent = '停止自动刷新';
                button.className = 'auto-refresh';
                log('自动刷新已开启 (每5秒)');
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('log-container').innerHTML = '';
        }

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            log('诊断工具初始化开始...');
            showBasicInfo();
            await testAPIConnection();
            if (apiBaseUrl) {
                await testDataLoading();
            }
            testJavaScript();
            log('诊断工具初始化完成');
        });
    </script>
</body>
</html>

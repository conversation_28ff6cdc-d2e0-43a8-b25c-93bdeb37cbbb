#!/usr/bin/env python3
"""
测试思维链提取修复效果
"""

import sys
import os
sys.path.append('backend')

from llm_caller import LLMCaller

def test_thinking_extraction():
    """测试思维链提取功能"""
    print("🧪 测试思维链提取修复效果")
    print("=" * 60)
    
    # 模拟LLM返回的完整内容（基于实际输出）
    test_content = """**第一部分:AI思考过程**
1. **规则1：检查是否上传发票。**
   根据指令，需检查[来源: 附件概览 -> 附件类型] 列表中是否包含"发票"。在【单据数据】的"附件概览"部分，"附件类型"字段值为：业务招待事前审批表, 发票, 餐饮小票, 支付记录。该列表明确包含"发票"，因此判断为"通过"。

2. **规则2：检查是否上传事前审批表。**
   根据指令，需检查 [来源: 附件概览 -> 附件类型] 是否包含"业务招待事前审批表"。在"附件类型"字段中，该项存在，且其后还提供了该审批表的具体信息（如招待对象、人数、事由等），说明已上传。因此判断为"通过"。

3. **规则3：检查是否上传用餐小票。**
   指令要求检查 [来源: 附件概览 -> 附件类型] 是否包含"餐饮小票"。在"附件类型"中明确列出"餐饮小票"，且后续提供了详细的菜品列表和用餐总额1876元，与报销金额一致，信息完整。因此判断为"通过"。

**第二部分:审核结果**
[
  {
    "rule_id": "规则1:检查是否上传发票.",
    "status": "通过",
    "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'发票', 满足要求."
  },
  {
    "rule_id": "规则2:检查是否上传事前审批表.",
    "status": "通过",
    "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'业务招待事前审批表', 满足要求."
  }
]"""

    print("📝 测试内容长度:", len(test_content))
    print("📝 测试内容预览:")
    print(test_content[:200] + "...")
    print()
    
    # 创建LLM调用器实例
    llm_caller = LLMCaller("test-key", "test-model", {})
    
    # 测试思维链提取
    print("🔄 开始测试思维链提取...")
    thinking, result = llm_caller._extract_reasoning_from_content(test_content)
    
    print("=" * 60)
    print("📊 提取结果:")
    print(f"🧠 思维链长度: {len(thinking)} 字符")
    print(f"📝 结果长度: {len(result)} 字符")
    print()
    
    print("🧠 提取的思维链内容:")
    print("-" * 40)
    print(thinking)
    print("-" * 40)
    print()
    
    print("📝 提取的结果内容:")
    print("-" * 40)
    print(result[:500] + "..." if len(result) > 500 else result)
    print("-" * 40)
    
    # 验证提取效果
    print("\n✅ 验证结果:")
    if len(thinking) > 200:
        print("✅ 思维链长度正常")
    else:
        print("❌ 思维链长度过短")
    
    if "规则1：检查是否上传发票" in thinking:
        print("✅ 思维链内容正确")
    else:
        print("❌ 思维链内容不完整")
    
    if '"rule_id"' in result and '"status"' in result:
        print("✅ 结果格式正确")
    else:
        print("❌ 结果格式异常")
    
    # 测试旧格式（带冒号）
    print("\n🔄 测试带冒号格式...")
    test_content_with_colon = test_content.replace("**第二部分:审核结果**", "**第二部分：审核结果**")
    thinking2, result2 = llm_caller._extract_reasoning_from_content(test_content_with_colon)
    
    print(f"🧠 带冒号格式思维链长度: {len(thinking2)} 字符")
    if len(thinking2) > 200:
        print("✅ 带冒号格式也能正确提取")
    else:
        print("❌ 带冒号格式提取失败")

if __name__ == "__main__":
    test_thinking_extraction()

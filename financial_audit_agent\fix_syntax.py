#!/usr/bin/env python3
"""
修复Python文件中的中文标点符号语法错误
"""

import os
import re

def fix_chinese_punctuation(file_path):
    """修复文件中的中文标点符号"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 记录原始内容
        original_content = content
        
        # 修复中文逗号
        content = content.replace('，', ',')

        # 修复中文顿号
        content = content.replace('、', ', ')

        # 修复中文句号
        content = content.replace('。', '.')

        # 修复中文分号
        content = content.replace('；', ';')

        # 修复中文冒号
        content = content.replace('：', ':')

        # 修复中文问号
        content = content.replace('？', '?')

        # 修复中文感叹号
        content = content.replace('！', '!')
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复了文件: {file_path}")
            return True
        else:
            print(f"✓ 文件无需修复: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 修复文件失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复Python文件中的中文标点符号...")
    
    # 需要修复的文件列表
    files_to_fix = [
        'financial_audit_agent/backend/llm_caller.py',
        'financial_audit_agent/backend/mock_llm_caller.py',
        'financial_audit_agent/backend/auditor_v2/run_audit_v2.py',
        'financial_audit_agent/backend/auditor_v2/orchestrator_v2.py',
        'financial_audit_agent/backend/auditor_v2/data_consolidator.py',
        'financial_audit_agent/backend/auditor_v2/rule_parser.py',
        'financial_audit_agent/backend/auditor_v2/prompt_manager.py'
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_chinese_punctuation(file_path):
                fixed_count += 1
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print(f"\n🎉 修复完成! 共修复了 {fixed_count} 个文件")

if __name__ == "__main__":
    main()

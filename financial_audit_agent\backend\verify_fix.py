#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果的脚本
"""

import json
import re

def verify_fix():
    """验证修复效果"""
    print("🔍 验证修复效果...")
    
    # 读取状态文件
    with open('audit_state.json', 'r', encoding='utf-8') as f:
        state = json.load(f)
    
    ai_thinking = state.get('ai_thinking', '')
    summary = state.get('summary', {})
    
    print(f"\n📊 状态文件统计:")
    print(f"- 总规则数: {summary.get('total_rules', 0)}")
    print(f"- 已完成规则: {summary.get('completed_rules', 0)}")
    print(f"- 通过规则: {summary.get('passed_rules', 0)}")
    print(f"- 失败规则: {summary.get('failed_rules', 0)}")
    
    print(f"\n📝 AI思考内容分析:")
    print(f"- 内容长度: {len(ai_thinking)} 字符")
    
    # 检查是否包含JSON结果
    json_pattern = r'\[\s*\{[\s\S]*?"rule_id"[\s\S]*?\}\s*\]'
    json_matches = re.findall(json_pattern, ai_thinking)
    
    if json_matches:
        print(f"✅ 包含JSON格式结果: {len(json_matches)} 个")
        
        # 解析JSON结果
        try:
            json_str = json_matches[0]
            results = json.loads(json_str)
            print(f"✅ JSON解析成功，包含 {len(results)} 条规则结果")
            
            for i, result in enumerate(results, 1):
                rule_id = result.get('rule_id', 'N/A')
                status = result.get('status', 'N/A')
                print(f"  - 规则{i}: {status}")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
    else:
        print("❌ 未找到JSON格式结果")
    
    # 检查规则状态
    print(f"\n🔍 规则状态检查:")
    for i in range(1, 6):
        rule_pattern = f"规则{i}.*?状态.*?通过"
        if re.search(rule_pattern, ai_thinking, re.DOTALL):
            print(f"✅ 规则{i}: 已分析并通过")
        else:
            print(f"❌ 规则{i}: 未找到完整分析")
    
    # 模拟前端解析逻辑
    print(f"\n🔧 模拟前端解析:")
    
    # 使用前端的解析模式
    patterns = [
        r'规则(\d+)[\s\S]{0,500}?状态应为\s*["""]?(通过|不通过|警告|无法判断)["""]?',
        r'"rule_id":\s*"规则(\d+)[^"]*",\s*"status":\s*"(通过|不通过|警告|无法判断)"'
    ]
    
    completed_rules = set()
    
    for pattern in patterns:
        matches = re.findall(pattern, ai_thinking, re.IGNORECASE)
        for match in matches:
            rule_num = int(match[0])
            status = match[1]
            completed_rules.add(rule_num)
            print(f"  📋 解析到规则{rule_num}: {status}")
    
    print(f"\n📊 前端解析结果:")
    print(f"- 解析到的规则数: {len(completed_rules)}")
    print(f"- 规则列表: {sorted(completed_rules)}")
    
    # 按阶段统计
    phase_rules = {
        'attachment': list(range(1, 6)),      # 规则1-5
        'consistency': list(range(6, 25)),    # 规则6-24
        'amount': list(range(25, 31)),        # 规则25-30
        'compliance': list(range(31, 39))     # 规则31-38
    }
    
    print(f"\n📈 各阶段完成情况:")
    for phase, rules in phase_rules.items():
        completed_in_phase = [r for r in rules if r in completed_rules]
        total_in_phase = len(rules)
        completed_count = len(completed_in_phase)
        percentage = (completed_count / total_in_phase) * 100 if total_in_phase > 0 else 0
        
        phase_names = {
            'attachment': '附件完整性检查',
            'consistency': '字段内容与一致性检查', 
            'amount': '金额与标准检查',
            'compliance': '八项规定合规性检查'
        }
        
        print(f"  {phase_names[phase]}: {completed_count}/{total_in_phase} ({percentage:.1f}%)")
    
    print(f"\n🎯 修复效果评估:")
    if len(completed_rules) >= 5:
        print("✅ 修复成功！前端应该能正确显示第一阶段的5条规则已完成")
    else:
        print("❌ 修复不完整，前端可能仍无法正确解析")
    
    return len(completed_rules) >= 5

if __name__ == "__main__":
    success = verify_fix()
    if success:
        print("\n🎉 修复验证通过！请刷新前端页面查看效果。")
    else:
        print("\n⚠️ 修复验证失败，需要进一步调试。")

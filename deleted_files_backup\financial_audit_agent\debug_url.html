<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .test-button {
            display: inline-block;
            margin: 10px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 URL参数调试工具</h1>
        
        <div class="info-box">
            <strong>当前完整URL:</strong><br>
            <span id="full-url"></span>
        </div>
        
        <div class="info-box">
            <strong>协议:</strong> <span id="protocol"></span><br>
            <strong>主机:</strong> <span id="host"></span><br>
            <strong>路径:</strong> <span id="pathname"></span><br>
            <strong>查询参数:</strong> <span id="search"></span><br>
            <strong>Hash:</strong> <span id="hash"></span>
        </div>
        
        <div id="doc-number-result" class="info-box">
            <strong>检测到的单据编号:</strong> <span id="detected-doc"></span>
        </div>
        
        <div class="info-box">
            <strong>所有URL参数:</strong><br>
            <div id="all-params"></div>
        </div>
        
        <h2>测试链接</h2>
        <a href="?doc=ZDBXD2025042900003" class="test-button">添加doc参数</a>
        <a href="?document=ZDBXD2025042900003" class="test-button">添加document参数</a>
        <a href="?" class="test-button">清除所有参数</a>
        
        <h2>生成的进度页面链接</h2>
        <div class="info-box">
            <strong>应该生成的链接:</strong><br>
            <span id="expected-link"></span>
        </div>
        
        <h2>测试报告链接生成</h2>
        <button class="test-button" onclick="testReportLinkGeneration()">测试报告链接生成</button>
        <div id="report-link-result" class="info-box" style="display: none;">
            <strong>生成的报告链接:</strong><br>
            <span id="generated-report-link"></span>
        </div>
    </div>

    <script>
        function updateURLInfo() {
            // 显示URL信息
            document.getElementById('full-url').textContent = window.location.href;
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('host').textContent = window.location.host || '(本地文件)';
            document.getElementById('pathname').textContent = window.location.pathname;
            document.getElementById('search').textContent = window.location.search || '(无)';
            document.getElementById('hash').textContent = window.location.hash || '(无)';
            
            // 检测单据编号
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
            
            const docElement = document.getElementById('detected-doc');
            const resultBox = document.getElementById('doc-number-result');
            
            if (documentNumber) {
                docElement.textContent = documentNumber;
                resultBox.className = 'info-box success';
            } else {
                docElement.textContent = '未检测到';
                resultBox.className = 'info-box warning';
            }
            
            // 显示所有参数
            const allParamsDiv = document.getElementById('all-params');
            const params = [...urlParams.entries()];
            if (params.length > 0) {
                allParamsDiv.innerHTML = params.map(([key, value]) => `${key} = ${value}`).join('<br>');
            } else {
                allParamsDiv.textContent = '(无参数)';
            }
            
            // 生成期望的链接
            const currentPath = window.location.pathname;
            const basePath = currentPath.replace('debug_url.html', 'progress_viewer.html');
            const expectedLink = `${window.location.protocol}//${window.location.host || ''}${basePath}?doc=ZDBXD2025042900003`;
            document.getElementById('expected-link').textContent = expectedLink;
        }
        
        function testReportLinkGeneration() {
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
            
            let reportUrl;
            const currentUrl = window.location.href;
            
            if (currentUrl.includes('localhost:8001')) {
                reportUrl = 'http://localhost:8001/frontend/audit_viewer.html';
            } else if (currentUrl.startsWith('file://')) {
                reportUrl = 'http://localhost:8001/frontend/audit_viewer.html';
            } else {
                reportUrl = 'audit_viewer.html';
            }
            
            if (documentNumber) {
                reportUrl += `?doc=${documentNumber}`;
            }
            
            document.getElementById('generated-report-link').textContent = reportUrl;
            document.getElementById('report-link-result').style.display = 'block';
        }
        
        // 页面加载时更新信息
        window.onload = updateURLInfo;
        
        // 监听URL变化
        window.addEventListener('popstate', updateURLInfo);
    </script>
</body>
</html>

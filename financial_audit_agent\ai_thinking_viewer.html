<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 AI思维链查看器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .thinking-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .thinking-content {
            white-space: pre-wrap;
            line-height: 1.8;
            font-size: 15px;
            color: #333;
        }
        .status-bar {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .refresh-btn:hover {
            transform: translateY(-2px);
        }
        .phase-title {
            color: #007bff;
            font-weight: bold;
            font-size: 1.2em;
            margin: 15px 0 10px 0;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 AI思维链查看器</h1>
            <p>实时显示AI财务审核系统的思维过程</p>
        </div>
        
        <div class="content">
            <div class="status-bar">
                <div>
                    <strong>状态:</strong> <span id="audit-status">加载中...</span> |
                    <strong>进度:</strong> <span id="progress">0%</span> |
                    <strong>阶段:</strong> <span id="current-phase">-</span>
                </div>
                <button class="refresh-btn" onclick="loadAIThinking()">🔄 刷新</button>
            </div>
            
            <div id="message-area"></div>
            
            <div class="thinking-section">
                <h3>🔍 AI分析过程</h3>
                <div id="thinking-display" class="thinking-content">
                    <div class="loading">正在加载AI思维链...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 直接从状态文件读取数据（通过API）
        async function loadAIThinking() {
            const messageArea = document.getElementById('message-area');
            const thinkingDisplay = document.getElementById('thinking-display');
            
            try {
                messageArea.innerHTML = '<div class="success">🔄 正在刷新AI思维链...</div>';
                
                // 尝试从API获取状态
                const response = await fetch('http://localhost:8001/api/status');
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 更新状态信息
                    document.getElementById('audit-status').textContent = data.audit_status || '未知';
                    document.getElementById('progress').textContent = (data.progress_percent || 0) + '%';
                    document.getElementById('current-phase').textContent = data.current_phase || '未知';
                    
                    // 显示AI思维链
                    if (data.ai_thinking) {
                        thinkingDisplay.innerHTML = formatThinkingContent(data.ai_thinking);
                        messageArea.innerHTML = `<div class="success">✅ AI思维链加载成功！内容长度: ${data.ai_thinking.length} 字符</div>`;
                        
                        // 分析阶段数量
                        const phases = (data.ai_thinking.match(/## 🔍/g) || []).length;
                        if (phases > 0) {
                            messageArea.innerHTML += `<div class="success">🎯 发现 ${phases} 个审核阶段的详细思维链</div>`;
                        }
                    } else {
                        thinkingDisplay.innerHTML = '<div class="error">❌ 未找到AI思维链内容</div>';
                        messageArea.innerHTML = '<div class="error">⚠️ API响应中没有ai_thinking字段</div>';
                    }
                } else {
                    throw new Error(`API请求失败: ${response.status}`);
                }
                
            } catch (error) {
                console.error('加载AI思维链失败:', error);
                messageArea.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                thinkingDisplay.innerHTML = `<div class="error">无法连接到API服务器，请确保系统正在运行</div>`;
            }
        }
        
        function formatThinkingContent(content) {
            // 简单的格式化处理
            return content
                .replace(/## 🔍/g, '<div class="phase-title">## 🔍')
                .replace(/\n/g, '<br>')
                .replace(/• \*\*(.*?)\*\*/g, '<strong>• $1</strong>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        }
        
        // 页面加载时自动加载
        window.onload = function() {
            loadAIThinking();
            
            // 每30秒自动刷新
            setInterval(loadAIThinking, 30000);
        };
    </script>
</body>
</html>

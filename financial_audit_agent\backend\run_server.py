#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的服务器启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from start_backend_v2 import app
    print("🚀 启动后端服务器...")
    print("📍 服务地址: http://localhost:8001")
    print("✅ 修复已完成，前端应该能正确显示第一阶段的5条规则已完成")
    print("🔄 请刷新前端页面查看效果")
    print("⏹️  按 Ctrl+C 停止服务器")
    
    app.run(host='0.0.0.0', port=8001, debug=False)
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在正确的目录中运行此脚本")
except Exception as e:
    print(f"❌ 启动失败: {e}")

# 财务智能审核系统

<div align="center">

![系统状态](https://img.shields.io/badge/状态-稳定运行-brightgreen)
![版本](https://img.shields.io/badge/版本-v2.0-blue)
![Python](https://img.shields.io/badge/Python-3.8+-blue)
![AI模型](https://img.shields.io/badge/AI-阿里云百炼-orange)

**基于AI大模型的智能化财务单据审核系统**

</div>

## 📋 项目概述

财务智能审核系统是一套基于人工智能大模型的自动化财务单据审核解决方案，专门针对业务招待费等财务报销单据进行智能化审核。系统采用分步式处理架构，能够自动识别单据内容、执行合规性检查、生成详细审核报告，并提供现代化的Web可视化界面。

### 🎯 核心价值

- **智能化审核**：基于阿里云百炼大模型，实现语义级别的智能审核
- **分步式处理**：将复杂审核任务分解为多个逻辑清晰的步骤，突破上下文长度限制
- **全流程自动化**：从单据上传到审核报告生成的完整自动化流程
- **可视化展示**：现代化Web界面，红绿灯状态指示，审核结果一目了然
- **高度可扩展**：模块化设计，支持自定义审核规则和业务场景

## ✨ 功能特性

### 🔍 智能审核引擎
- **三层规则引擎**：确定性规则 + 关键词规则 + 语义规则
- **分步式处理**：4个审核阶段，38条审核规则
- **上下文传递**：步骤间智能传递关键信息，保持审核连贯性
- **错误恢复**：完善的异常处理和错误恢复机制

### 📊 审核报告系统
- **结构化报告**：JSON格式，包含摘要统计和详细结果
- **多维度分析**：通过/警告/失败状态统计
- **智能建议**：基于审核结果生成改进建议
- **历史记录**：审核报告自动保存，支持历史查询

### 🎨 可视化界面
- **红绿灯状态**：直观的整体审核状态指示
- **统计仪表盘**：关键指标实时展示
- **详细审核表格**：规则级别的审核结果展示
- **响应式设计**：支持多种设备和屏幕尺寸

### 🔧 系统集成
- **一体化启动**：单命令启动审核+Web服务
- **自动化流程**：审核完成后自动打开可视化页面
- **多模式支持**：支持命令行和Web界面两种使用方式
- **配置灵活**：支持多种AI模型和API配置

## 🏗️ 技术架构

### 系统架构图

```mermaid
graph TB
    A[用户界面] --> B[一体化启动器]
    B --> C[审核引擎]
    B --> D[Web服务器]
    
    C --> E[规则解析器]
    C --> F[数据整合器]
    C --> G[提示词管理器]
    C --> H[核心编排器]
    
    H --> I[确定性规则引擎]
    H --> J[关键词规则引擎]
    H --> K[语义规则引擎]
    
    K --> L[阿里云百炼API]
    
    H --> M[审核报告生成器]
    M --> N[JSON报告]
    
    D --> O[前端界面]
    O --> P[审核结果可视化]
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style L fill:#fff3e0
    style O fill:#e8f5e8
```

### 技术栈

#### 后端技术栈
- **Python 3.8+**：主要开发语言
- **Pydantic**：数据模型和验证
- **OpenAI SDK**：AI模型API调用（兼容模式）
- **DashScope**：阿里云百炼传统模式支持
- **HTTP Server**：内置Web服务器

#### 前端技术栈
- **HTML5**：页面结构
- **CSS3**：现代化样式设计，支持渐变和动画
- **JavaScript ES6+**：交互逻辑和数据处理
- **响应式设计**：自适应多种设备

#### AI模型
- **阿里云百炼**：主要AI推理引擎
- **Qwen-Max**：默认使用的大语言模型
- **OpenAI兼容模式**：推荐的API调用方式

## 🔄 单据审核流程

### 完整审核流程图

```mermaid
graph TD
    A[单据上传] --> B[文件验证]
    B --> C[规则解析]
    C --> D[数据整合]
    D --> E[第一阶段：附件完整性检查]
    E --> F[第二阶段：字段内容与一致性检查]
    F --> G[第三阶段：金额与标准检查]
    G --> H[第四阶段：八项规定检查]
    H --> I[结果汇总]
    I --> J[报告生成]
    J --> K[可视化展示]
    
    style A fill:#ffebee
    style E fill:#e3f2fd
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#fce4ec
    style J fill:#f3e5f5
```

### 详细审核步骤

#### 1. 数据准备阶段
- **文件读取**：解析表单JSON和附件JSON文件
- **数据验证**：检查文件格式和必要字段
- **数据整合**：将多个数据源整合为统一格式
- **规则加载**：解析业务招待费审核规则文件

#### 2. 分步式审核执行

**第一阶段：附件完整性检查（5条规则）**
- 检查发票上传情况
- 验证事前审批表存在性
- 确认用餐小票完整性
- 检查支付记录附件
- 验证特殊物品签收表（如适用）

**第二阶段：字段内容与一致性检查（25条规则）**
- 商务招待发起主体验证
- 招待对象公职人员识别
- 各字段间一致性校验
- 日期、人数、金额匹配检查
- 事由描述合理性分析

**第三阶段：金额与标准检查（7条规则）**
- 消费标准合规性检查
- 金额计算准确性验证
- 人均消费标准审核
- 发票金额与实际支出对比

**第四阶段：八项规定检查（6条规则）**
- 政策合规性验证
- 招待标准符合性检查
- 特殊情况处理规则

#### 3. 智能判断机制

每个审核规则都采用以下判断逻辑：

1. **数据定位**：使用显式指针技术精确定位数据源
   ```
   [来源: 主报销单信息 -> 招待类型]
   ```

2. **业务理解**：通过目的标签理解规则背后的业务逻辑
   ```
   [目的: 确保报销单据所需的基础材料齐全]
   ```

3. **结果判定**：
   - **PASS**：完全符合规则要求
   - **WARNING**：存在需要关注的问题，建议人工复核
   - **FAIL**：不符合规则要求，审核不通过

## 📊 审核报告生成

### 报告结构

审核报告采用JSON格式，包含以下主要部分：

```json
{
  "summary": {
    "total_rules_checked": 38,
    "passed_count": 37,
    "failed_count": 0,
    "warning_count": 1
  },
  "details": [
    {
      "rule_id": "规则1：检查是否上传发票",
      "status": "PASS",
      "message": "根据附件概览，发现列表中包含'发票'，满足要求。"
    }
  ],
  "review_comments": "经审核，本次业务招待费报销存在以下需要关注的问题...",
  "metadata": {
    "audit_time": "2025-01-XX XX:XX:XX",
    "system_version": "v2.0",
    "model_used": "qwen-max"
  }
}
```

### 报告内容解读

#### 摘要统计 (summary)
- **total_rules_checked**：执行的审核规则总数
- **passed_count**：通过的规则数量
- **failed_count**：失败的规则数量  
- **warning_count**：警告的规则数量

#### 详细结果 (details)
每条规则的具体审核结果，包括：
- **rule_id**：规则标识和描述
- **status**：审核状态（PASS/WARNING/FAIL）
- **message**：详细的审核说明和依据

#### 审核意见 (review_comments)
基于所有审核结果生成的综合性审核意见，包括：
- 问题汇总
- 改进建议
- 最终审核结论

### 可视化展示

#### 红绿灯状态指示器
- 🟢 **绿灯**：所有规则通过，审核完全合规
- 🟡 **黄灯**：存在警告项，建议人工复核
- 🔴 **红灯**：存在失败项，审核不通过

#### 统计仪表盘
实时显示关键审核指标：
- 总规则数统计
- 通过数量及占比
- 警告数量及占比
- 失败数量及占比

#### 详细审核表格
规则级别的审核结果展示：
- 规则ID和描述
- 状态可视化标识
- 详细审核意见
- 数据来源追溯

## 🚀 安装部署

### 环境要求

- **Python**：3.8 或更高版本
- **操作系统**：Windows/Linux/macOS
- **内存**：建议 4GB 以上
- **网络**：需要访问阿里云百炼API

### 快速安装

1. **克隆项目**
```bash
git clone <repository-url>
cd 规则判断
```

2. **创建虚拟环境**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS  
source venv/bin/activate
```

3. **安装依赖**
```bash
cd financial_audit_agent/backend
pip install -r requirements.txt
```

4. **配置API密钥**
```bash
# 复制配置模板
cp config_example.json config.json
# 编辑配置文件，填入您的API密钥
```

### 配置说明

编辑 `financial_audit_agent/backend/config.json`：

```json
{
  "INPUT_DIRECTORY": "./audit_files/",
  "OUTPUT_DIRECTORY": "./audit_reports/",
  "LLM_API_KEY": "sk-your-api-key-here",
  "LLM_MODEL_NAME": "qwen-max",
  "LLM_API_MODE": "openai_compatible",
  "LLM_BASE_URL": "https://dashscope.aliyuncs.com/compatible-mode/v1",
  "LLM_TEMPERATURE": 0.7,
  "LLM_MAX_TOKENS": 2000
}
```

**配置参数说明：**
- `LLM_API_KEY`：阿里云百炼API密钥
- `LLM_MODEL_NAME`：使用的模型名称（推荐qwen-max）
- `LLM_API_MODE`：API模式（推荐openai_compatible）
- `LLM_TEMPERATURE`：生成文本的随机性（0-2）
- `LLM_MAX_TOKENS`：单次请求最大token数

## 📖 使用说明

### 一体化启动（推荐）

最简单的使用方式，一键启动审核+Web服务：

```bash
cd financial_audit_agent
python main.py
```

系统将自动：
1. 检查环境和配置
2. 查找待审核文件
3. 执行智能审核
4. 生成审核报告
5. 启动Web服务器
6. 打开浏览器显示结果

### 分步操作模式

如需更精细的控制，可以分步执行：

#### 1. 准备审核文件
将以下文件放入 `financial_audit_agent/backend/audit_files/` 目录：
- `*_表单提取.json`：表单数据文件
- `*_附件提取.json`：附件数据文件

#### 2. 执行审核
```bash
cd financial_audit_agent/backend
python main.py
```

#### 3. 查看结果
```bash
cd ../
python start_web_server.py
```

### 新版分步式审核系统

使用最新的v2.0分步式审核引擎：

```bash
cd financial_audit_agent/backend/auditor_v2
python run_audit_v2.py --form ../../ZDBXD2025051300001_表单提取.json --attachment ../../ZDBXD2025051300001_附件提取.json --rules ../../业务招待费审核规则_V2.txt
```

### 命令行参数

```bash
# 查看帮助
python run_audit_v2.py --help

# 指定文件路径
python run_audit_v2.py --form <表单文件> --attachment <附件文件> --rules <规则文件>

# 使用演示数据
python run_audit_v2.py
```

## 🔌 API接口说明

### 核心API模块

#### 1. 审核编排器 (Orchestrator)

**主要接口：**
```python
class Orchestrator:
    def run_audit(self, data_context: DataContext) -> List[AuditResult]
    def generate_report_and_display(self, report_path: str) -> None
```

**功能说明：**
- 统筹整个审核流程
- 调度三层规则引擎
- 生成最终审核报告

#### 2. 数据模型 (DataContext)

**数据结构：**
```python
class DataContext(BaseModel):
    source_files: Dict[str, str]          # 源文件路径
    attachment_list: List[str]            # 附件清单
    all_extracted_data: Dict[str, Any]    # 提取的原始数据
```

#### 3. 审核结果 (AuditResult)

**结果格式：**
```python
class AuditResult(BaseModel):
    rule_id: str      # 规则标识
    status: str       # PASS/WARNING/FAIL
    message: str      # 详细说明
```

### LLM调用接口

**主要方法：**
```python
class LLMCaller:
    def query_semantic_rule(self, prompt: str, max_retries: int = 3) -> str
```

**支持的API模式：**
- OpenAI兼容模式（推荐）
- DashScope传统模式

### Web服务接口

**静态文件服务：**
- `GET /audit_viewer.html` - 主页面
- `GET /audit_reports/audit_report_v2.json` - 审核报告数据
- `GET /style.css` - 样式文件
- `GET /script.js` - 前端脚本

## 📋 示例数据和预期结果

### 输入数据示例

#### 表单数据结构 (*_表单提取.json)
```json
{
  "主报销单信息": {
    "招待类型": "商务招待",
    "招待发起主体": "业务部门",
    "招待对象": "北京中指实证数据信息技术有限公司深圳分公司",
    "招待日期": "2025-04-18",
    "招待人数": "11",
    "事由": "招待客户进行业务交流"
  }
}
```

#### 附件数据结构 (*_附件提取.json)
```json
{
  "附件概览": {
    "附件类型": ["发票", "业务招待事前审批表", "餐饮小票", "支付记录"]
  },
  "附件：业务招待事前审批表": {
    "业务招待类别": "商务宴请",
    "招待日期": "2025-04-18",
    "宴请标准": "550元/人"
  }
}
```

### 预期审核结果

#### 正常通过场景
```json
{
  "summary": {
    "total_rules_checked": 38,
    "passed_count": 38,
    "failed_count": 0,
    "warning_count": 0
  },
  "review_comments": "经审核，本次业务招待费报销完全符合相关规定和标准，可予以通过。"
}
```

#### 存在警告场景
```json
{
  "summary": {
    "total_rules_checked": 38,
    "passed_count": 37,
    "failed_count": 0,
    "warning_count": 1
  },
  "review_comments": "经审核，本次业务招待费报销存在以下需要关注的问题：1. 招待事由与项目用途的描述存在不一致..."
}
```

#### 审核失败场景
```json
{
  "summary": {
    "total_rules_checked": 38,
    "passed_count": 35,
    "failed_count": 2,
    "warning_count": 1
  },
  "review_comments": "经审核，本次业务招待费报销存在以下不符合规定的问题，建议退回修改..."
}
```

## ❓ 常见问题解答

### 安装和配置问题

**Q: 提示"未找到可用的API库"错误？**
A: 请确保安装了必要的依赖包：
```bash
pip install openai>=1.0.0 dashscope==1.22.1
```

**Q: API调用失败，提示认证错误？**
A: 请检查以下配置：
1. 确认API密钥格式正确（以sk-开头）
2. 检查config.json中的LLM_API_KEY设置
3. 确认阿里云百炼账户余额充足

**Q: 找不到待审核文件？**
A: 请确保文件放置在正确位置：
- 文件应放在 `financial_audit_agent/backend/audit_files/` 目录
- 文件名格式：`*_表单提取.json` 和 `*_附件提取.json`
- 确保文件格式为有效的JSON

### 使用问题

**Q: Web页面显示"正在审核..."不更新？**
A: 可能的原因和解决方案：
1. 审核进程仍在运行，请等待完成
2. 检查浏览器控制台是否有错误信息
3. 手动刷新页面（F5或Ctrl+R）
4. 确认audit_report_v2.json文件已生成

**Q: 审核结果不准确？**
A: 建议检查：
1. 输入数据格式是否正确
2. 规则文件是否完整
3. 尝试调整LLM_TEMPERATURE参数（降低随机性）
4. 检查模型版本是否为最新

**Q: 系统运行缓慢？**
A: 优化建议：
1. 确保网络连接稳定
2. 检查系统资源使用情况
3. 考虑使用更快的模型（如qwen-turbo）
4. 减少LLM_MAX_TOKENS参数值

### 开发问题

**Q: 如何添加自定义审核规则？**
A: 修改 `业务招待费审核规则_V2.txt` 文件：
1. 按照现有格式添加新规则
2. 使用 `[来源: 文件名 -> 字段名]` 语法指定数据源
3. 添加 `[目的: ...]` 标签说明业务逻辑
4. 重启系统使规则生效

**Q: 如何自定义前端界面？**
A: 修改前端文件：
- `frontend/style.css`：修改样式和颜色
- `frontend/script.js`：修改交互逻辑
- `frontend/audit_viewer.html`：修改页面结构

**Q: 如何集成其他AI模型？**
A: 修改 `backend/llm_caller.py`：
1. 添加新的API客户端初始化
2. 实现对应的查询方法
3. 在config.json中配置相关参数

## 🛠️ 开发指南

### 项目结构

```
financial_audit_agent/
├── backend/                    # 后端核心代码
│   ├── auditor_v2/            # 新版分步式审核引擎
│   │   ├── rule_parser.py     # 规则解析器
│   │   ├── data_consolidator.py # 数据整合器
│   │   ├── prompt_manager.py  # 提示词管理器
│   │   ├── orchestrator_v2.py # 核心编排器v2
│   │   └── run_audit_v2.py    # 执行入口v2
│   ├── rules/                 # 规则引擎
│   │   ├── deterministic_rules.py # 确定性规则
│   │   └── keyword_rules.py   # 关键词规则
│   ├── data_models.py         # 数据模型定义
│   ├── llm_caller.py          # AI模型调用
│   ├── orchestrator.py        # 核心编排器v1
│   ├── main.py               # 主程序入口
│   └── config.json           # 配置文件
├── frontend/                  # 前端界面
│   ├── audit_viewer.html     # 主页面
│   ├── style.css            # 样式文件
│   └── script.js            # 交互脚本
├── audit_reports/            # 审核报告目录
├── main.py                  # 一体化启动器
├── run_audit_with_web.py    # 审核+Web一体化
└── 业务招待费审核规则_V2.txt # 审核规则文件
```

### 核心模块说明

#### 1. 规则引擎架构

**三层规则引擎：**
- **确定性规则**：基于精确匹配和数值计算
- **关键词规则**：基于预定义关键词库匹配
- **语义规则**：基于AI大模型的语义理解

#### 2. 分步式处理机制

**v2.0新特性：**
- 显式指针技术：`[来源: 文件名 -> 字段名]`
- 业务意图透明化：`[目的: ...]`
- 结构化指令体系
- 完善的错误处理

#### 3. 数据流处理

```mermaid
graph LR
    A[JSON文件] --> B[数据解析]
    B --> C[数据验证]
    C --> D[数据整合]
    D --> E[规则执行]
    E --> F[结果汇总]
    F --> G[报告生成]
```

### 扩展开发

#### 添加新的审核规则

1. **修改规则文件**：在 `业务招待费审核规则_V2.txt` 中添加新规则
2. **更新规则引擎**：如需要，在对应的规则引擎中添加实现
3. **测试验证**：使用测试数据验证新规则的正确性

#### 支持新的单据类型

1. **定义数据模型**：在 `data_models.py` 中添加新的数据结构
2. **创建规则文件**：参考现有格式创建新的规则文件
3. **更新解析器**：修改数据解析和整合逻辑
4. **扩展前端**：更新前端界面以支持新的单据类型

#### 集成新的AI模型

1. **扩展LLM调用器**：在 `llm_caller.py` 中添加新的API支持
2. **配置参数**：在 `config.json` 中添加相关配置项
3. **测试兼容性**：确保新模型与现有提示词兼容

## 📈 性能优化

### 系统性能指标

- **审核速度**：平均每个单据 30-60 秒
- **准确率**：基于测试数据集 >95%
- **并发支持**：单实例支持 1-3 个并发审核任务
- **内存使用**：运行时内存占用 <500MB

### 优化建议

#### 1. API调用优化
- 使用OpenAI兼容模式（性能更好）
- 合理设置max_tokens参数
- 启用请求重试机制

#### 2. 数据处理优化
- 预处理和缓存常用数据
- 优化JSON解析性能
- 减少不必要的数据复制

#### 3. 前端性能优化
- 启用浏览器缓存
- 压缩CSS和JavaScript文件
- 使用CDN加速静态资源

## 🔒 安全考虑

### 数据安全
- 本地处理，数据不上传到第三方服务器
- API密钥加密存储
- 审核报告本地保存

### 访问控制
- Web服务仅监听本地地址
- 支持自定义端口配置
- 可配置访问权限

### 隐私保护
- 敏感信息脱敏处理
- 日志记录最小化
- 支持数据清理功能

## 📝 更新日志

### v2.0.0 (2025-01-XX)
- ✨ 新增分步式智能审核引擎
- 🚀 显式指针技术，提升数据定位精度
- 💡 业务意图透明化，增强AI理解能力
- 🛡️ 完善的错误处理和恢复机制
- 📊 优化审核报告格式和内容

### v1.0.0 (2024-XX-XX)
- 🎉 初始版本发布
- 🔍 三层规则引擎架构
- 🎨 现代化Web可视化界面
- 🤖 阿里云百炼AI模型集成
- 📋 完整的业务招待费审核流程

## 🤝 贡献指南

### 贡献方式
1. Fork 项目仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范
- 遵循PEP 8 Python代码规范
- 添加必要的注释和文档
- 编写单元测试
- 更新相关文档

## 📞 技术支持

### 联系方式
- 项目仓库：[GitHub Repository]
- 问题反馈：[Issues]
- 技术讨论：[Discussions]

### 故障排除
1. 查看系统日志和错误信息
2. 检查配置文件和环境设置
3. 参考常见问题解答
4. 提交Issue获取帮助

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

---

<div align="center">

**财务智能审核系统 - 让财务审核更智能、更高效**

Made with ❤️ by AI & Human Collaboration

</div>

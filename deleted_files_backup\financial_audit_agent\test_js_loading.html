<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript加载测试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            border: 1px solid #00ff00;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
        }
        .test-output {
            background: #000;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #00ffff; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 JavaScript加载测试</h1>
        
        <div class="test-section">
            <h3>加载状态检查</h3>
            <div id="loading-output" class="test-output">正在检查...</div>
        </div>
        
        <div class="test-section">
            <h3>类定义检查</h3>
            <div id="class-output" class="test-output">等待检查...</div>
        </div>
        
        <div class="test-section">
            <h3>实例化测试</h3>
            <div id="instance-output" class="test-output">等待测试...</div>
        </div>
    </div>

    <!-- 按照原始页面的顺序加载JavaScript -->
    <script src="error_handler.js"></script>
    <script src="performance_monitor.js"></script>
    <script src="ai_console_enhanced.js"></script>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        // 检查加载状态
        function checkLoadingStatus() {
            log('loading-output', '开始检查JavaScript文件加载状态...', 'info');
            
            // 检查全局对象
            const expectedObjects = [
                'ErrorHandler',
                'PerformanceMonitor', 
                'AIConsoleEnhanced'
            ];
            
            for (const objName of expectedObjects) {
                try {
                    if (typeof window[objName] !== 'undefined') {
                        log('loading-output', `✅ ${objName} 已加载`, 'success');
                    } else {
                        log('loading-output', `❌ ${objName} 未加载`, 'error');
                    }
                } catch (error) {
                    log('loading-output', `❌ ${objName} 检查失败: ${error.message}`, 'error');
                }
            }
            
            // 检查函数
            const expectedFunctions = [
                'initializeAIConsole'
            ];
            
            for (const funcName of expectedFunctions) {
                try {
                    if (typeof window[funcName] === 'function') {
                        log('loading-output', `✅ 函数 ${funcName} 已定义`, 'success');
                    } else {
                        log('loading-output', `❌ 函数 ${funcName} 未定义`, 'error');
                    }
                } catch (error) {
                    log('loading-output', `❌ 函数 ${funcName} 检查失败: ${error.message}`, 'error');
                }
            }
        }

        // 检查类定义
        function checkClassDefinition() {
            log('class-output', '开始检查类定义...', 'info');
            
            try {
                if (typeof AIConsoleEnhanced === 'function') {
                    log('class-output', '✅ AIConsoleEnhanced 类已定义', 'success');
                    
                    // 检查类的原型方法
                    const expectedMethods = [
                        'init',
                        'detectAPIServer',
                        'loadRealData',
                        'startStatusPolling',
                        'updateRealStatus',
                        'updateRealEngineStatus'
                    ];
                    
                    for (const method of expectedMethods) {
                        if (typeof AIConsoleEnhanced.prototype[method] === 'function') {
                            log('class-output', `✅ 方法 ${method} 存在`, 'success');
                        } else {
                            log('class-output', `❌ 方法 ${method} 不存在`, 'error');
                        }
                    }
                    
                } else {
                    log('class-output', '❌ AIConsoleEnhanced 类未定义', 'error');
                }
            } catch (error) {
                log('class-output', `❌ 类定义检查失败: ${error.message}`, 'error');
            }
        }

        // 测试实例化
        async function testInstantiation() {
            log('instance-output', '开始测试实例化...', 'info');
            
            try {
                if (typeof AIConsoleEnhanced === 'function') {
                    log('instance-output', '创建 AIConsoleEnhanced 实例...', 'info');
                    const testInstance = new AIConsoleEnhanced();
                    log('instance-output', '✅ 实例创建成功', 'success');
                    
                    // 检查实例属性
                    const expectedProperties = [
                        'statusPollingInterval',
                        'realAuditData',
                        'realRulesData',
                        'apiBaseUrl',
                        'isInitialized'
                    ];
                    
                    for (const prop of expectedProperties) {
                        if (prop in testInstance) {
                            log('instance-output', `✅ 属性 ${prop} 存在: ${testInstance[prop]}`, 'success');
                        } else {
                            log('instance-output', `❌ 属性 ${prop} 不存在`, 'error');
                        }
                    }
                    
                    // 测试初始化
                    log('instance-output', '测试 init() 方法...', 'info');
                    await testInstance.init();
                    log('instance-output', '✅ init() 方法执行成功', 'success');
                    log('instance-output', `   初始化状态: ${testInstance.isInitialized}`, 'info');
                    log('instance-output', `   API地址: ${testInstance.apiBaseUrl}`, 'info');
                    
                    // 将实例保存到全局
                    window.testConsole = testInstance;
                    
                } else {
                    log('instance-output', '❌ AIConsoleEnhanced 类不可用', 'error');
                }
            } catch (error) {
                log('instance-output', `❌ 实例化测试失败: ${error.message}`, 'error');
                console.error('实例化错误详情:', error);
            }
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('🧪 开始JavaScript加载测试...');
                checkLoadingStatus();
                setTimeout(checkClassDefinition, 1000);
                setTimeout(testInstantiation, 2000);
            }, 500);
        });

        // 错误监听
        window.addEventListener('error', function(event) {
            log('loading-output', `JavaScript错误: ${event.error?.message || event.message}`, 'error');
            console.error('JavaScript错误详情:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            log('loading-output', `Promise错误: ${event.reason}`, 'error');
            console.error('Promise错误详情:', event.reason);
        });
    </script>
</body>
</html>

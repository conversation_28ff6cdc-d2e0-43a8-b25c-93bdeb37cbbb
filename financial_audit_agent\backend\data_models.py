from pydantic import BaseModel, Field
from typing import Optional, List, Any, Dict

class AuditResult(BaseModel):
    """定义单条审核规则的结果。"""
    rule_id: str
    status: str  # 状态应为: "PASS" (通过), "FAIL" (失败), "WARNING" (警告)
    message: str

class DataContext(BaseModel):
    """一个统一的数据结构，持有一次审计所需的所有提取信息。"""
    source_files: Dict[str, str] = Field(description="源JSON文件的路径")
    attachment_list: List[str] = Field(description="识别出的附件清单")
    all_extracted_data: Dict[str, Any] = Field(description="从JSON解析出的原始数据")

    # 在完整实现中，你可以在这里定义所有关键字段以方便访问和验证。
    # 目前，我们可以通过 all_extracted_data 字典来访问它们。

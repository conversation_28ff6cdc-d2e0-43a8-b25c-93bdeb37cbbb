/**
 * 前端状态管理器
 * 职责：统一管理前端状态获取和更新逻辑
 * 解决多数据源混乱问题
 */
class FrontendStateManager {
    constructor() {
        this.apiBaseUrl = null;
        this.currentState = null;
        this.pollingInterval = null;
        this.listeners = [];
    }

    /**
     * 初始化状态管理器
     */
    async init(apiBaseUrl) {
        this.apiBaseUrl = apiBaseUrl;
        console.log('🔧 状态管理器初始化:', apiBaseUrl);

        // 获取初始状态
        await this.fetchState();

        // 启动轮询
        this.startPolling();

        console.log('✅ 状态管理器初始化完成');
        return this.currentState;
    }

    /**
     * 获取当前状态（单一数据源）
     */
    async fetchState() {
        try {
            // 只从统一API获取状态，不再读取多个文件
            const response = await fetch(`${this.apiBaseUrl}/api/status`);
            if (response.ok) {
                const newState = await response.json();

                // 检查状态是否真正发生变化
                if (this.hasStateChanged(newState)) {
                    this.currentState = newState;
                    console.log('📊 状态更新:', this.currentState);

                    // 只在状态真正变化时通知监听器
                    this.notifyListeners();
                } else {
                    // 减少日志输出，避免控制台混乱
                    // console.log('📊 状态无变化，跳过更新');
                }

                return this.currentState;
            } else {
                throw new Error(`API响应错误: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ 状态获取失败:', error);

            // 降级到默认状态
            this.currentState = this.getDefaultState();
            return this.currentState;
        }
    }

    /**
     * 检查状态是否发生变化
     */
    hasStateChanged(newState) {
        if (!this.currentState) return true;

        // 比较关键字段
        const keyFields = ['audit_status', 'current_phase', 'progress_percent', 'last_updated'];

        for (const field of keyFields) {
            if (this.currentState[field] !== newState[field]) {
                console.log(`📊 状态字段变化: ${field}`, {
                    old: this.currentState[field],
                    new: newState[field]
                });
                return true;
            }
        }

        // 特殊处理AI思考内容：支持增量更新（包括已完成的审核）
        const oldThinking = this.currentState.ai_thinking || '';
        const newThinking = newState.ai_thinking || '';

        // 改进的变化检测逻辑：支持增量更新
        if (oldThinking !== newThinking) {
            const lengthDiff = newThinking.length - oldThinking.length;

            console.log('📊 AI思维链变化检测', {
                oldLength: oldThinking.length,
                newLength: newThinking.length,
                lengthDiff: lengthDiff,
                audit_status: newState.audit_status
            });

            // 情况1：内容增加（增量更新）
            if (lengthDiff > 0 && newThinking.startsWith(oldThinking)) {
                console.log('📊 AI思维链增量更新', {
                    oldLength: oldThinking.length,
                    newLength: newThinking.length,
                    addedLength: lengthDiff,
                    type: 'incremental'
                });
                return true;
            }

            // 情况2：内容完全替换（显著变化）
            const absoluteLengthDiff = Math.abs(lengthDiff);
            if (absoluteLengthDiff > 50) { // 降低阈值，提高敏感度
                console.log('📊 AI思维链完全更新', {
                    oldLength: oldThinking.length,
                    newLength: newThinking.length,
                    lengthDiff: lengthDiff,
                    type: 'complete'
                });
                return true;
            }

            // 情况3：内容结构变化（即使长度相似）
            if (oldThinking.length > 0 && newThinking.length > 0) {
                console.log('📊 AI思维链结构更新', {
                    oldLength: oldThinking.length,
                    newLength: newThinking.length,
                    type: 'structural'
                });
                return true;
            }

            // 情况4：从默认内容到实际内容的变化
            if (oldThinking.includes('系统就绪') && newThinking.length > 100) {
                console.log('📊 AI思维链从默认内容更新为实际内容', {
                    oldLength: oldThinking.length,
                    newLength: newThinking.length,
                    type: 'initialization'
                });
                return true;
            }
        }

        return false;
    }

    /**
     * 判断审核是否完成（统一逻辑）
     */
    isAuditComplete() {
        if (!this.currentState) return false;
        
        // 基于明确的状态字段判断，不依赖报告文件存在性
        return this.currentState.audit_status === 'completed' && 
               this.currentState.current_phase === 'finished' &&
               this.currentState.completion_time !== null;
    }

    /**
     * 获取审核进度百分比
     */
    getProgressPercent() {
        return this.currentState?.progress_percent || 0;
    }

    /**
     * 获取当前AI思考过程
     */
    getAIThinking() {
        return this.currentState?.ai_thinking || '';
    }

    /**
     * 获取审核摘要
     */
    getSummary() {
        return this.currentState?.summary || {
            total_rules: 0,
            completed_rules: 0,
            passed_rules: 0,
            failed_rules: 0,
            warning_rules: 0
        };
    }

    /**
     * 启动状态轮询
     */
    startPolling(interval = 5000) {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }
        
        this.pollingInterval = setInterval(async () => {
            await this.fetchState();
        }, interval);
        
        console.log('🔄 状态轮询已启动，间隔:', interval + 'ms');
    }

    /**
     * 停止状态轮询
     */
    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
            console.log('⏹️ 状态轮询已停止');
        }
    }

    /**
     * 添加状态变化监听器
     */
    addListener(callback) {
        this.listeners.push(callback);
    }

    /**
     * 移除状态变化监听器
     */
    removeListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 通知所有监听器
     */
    notifyListeners() {
        this.listeners.forEach(callback => {
            try {
                callback(this.currentState);
            } catch (error) {
                console.error('❌ 监听器回调错误:', error);
            }
        });
    }

    /**
     * 获取默认状态
     */
    getDefaultState() {
        return {
            audit_id: null,
            audit_status: 'ready',
            current_phase: 'ready',
            progress_percent: 0,
            start_time: null,
            completion_time: null,
            summary: {
                total_rules: 0,
                completed_rules: 0,
                passed_rules: 0,
                failed_rules: 0,
                warning_rules: 0
            },
            ai_thinking: '系统就绪，等待开始审核',
            last_updated: new Date().toISOString(),
            message: '系统就绪，等待开始审核',
            detail: '请启动审核流程'
        };
    }

    /**
     * 销毁状态管理器
     */
    destroy() {
        this.stopPolling();
        this.listeners = [];
        this.currentState = null;
        console.log('🗑️ 状态管理器已销毁');
    }
}

// 全局状态管理器实例
window.StateManager = FrontendStateManager;

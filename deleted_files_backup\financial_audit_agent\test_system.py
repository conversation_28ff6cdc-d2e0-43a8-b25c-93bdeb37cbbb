#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI财务审核系统测试脚本
验证前端和后台服务是否正常工作
"""

import os
import sys
import time
import json
import requests
import subprocess
from pathlib import Path


def test_backend_service():
    """测试后台服务"""
    print("🧪 测试后台审核服务...")
    
    try:
        # 运行后台服务
        result = subprocess.run([
            sys.executable, "start_backend.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 后台服务测试通过")
            
            # 检查生成的报告文件
            reports_dir = Path("audit_reports")
            if reports_dir.exists():
                report_files = list(reports_dir.glob("*.json"))
                print(f"✅ 发现 {len(report_files)} 个审核报告文件")
                
                # 验证报告内容
                for report_file in report_files:
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            report_data = json.load(f)
                            if 'summary' in report_data and 'details' in report_data:
                                print(f"✅ 报告文件 {report_file.name} 格式正确")
                            else:
                                print(f"⚠️ 报告文件 {report_file.name} 格式异常")
                    except Exception as e:
                        print(f"❌ 读取报告文件失败: {e}")
            else:
                print("⚠️ 未找到审核报告目录")
                
            return True
        else:
            print(f"❌ 后台服务测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 后台服务测试超时")
        return False
    except Exception as e:
        print(f"❌ 后台服务测试异常: {e}")
        return False


def test_frontend_service():
    """测试前端服务"""
    print("\n🧪 测试前端服务...")
    
    try:
        # 启动前端服务（非阻塞）
        process = subprocess.Popen([
            sys.executable, "simple_launcher.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待服务启动
        time.sleep(8)
        
        # 测试Web服务器
        try:
            response = requests.get("http://localhost:8002/frontend/ai_console.html", timeout=5)
            if response.status_code == 200:
                print("✅ Web服务器响应正常")
                web_ok = True
            else:
                print(f"⚠️ Web服务器响应异常: {response.status_code}")
                web_ok = False
        except requests.RequestException as e:
            print(f"❌ Web服务器连接失败: {e}")
            web_ok = False
        
        # 测试API服务器
        try:
            response = requests.get("http://localhost:8001/api/status", timeout=5)
            if response.status_code == 200:
                print("✅ API服务器响应正常")
                api_ok = True
            else:
                print(f"⚠️ API服务器响应异常: {response.status_code}")
                api_ok = False
        except requests.RequestException as e:
            print(f"❌ API服务器连接失败: {e}")
            api_ok = False
        
        # 终止前端进程
        process.terminate()
        process.wait(timeout=5)
        
        return web_ok or api_ok
        
    except Exception as e:
        print(f"❌ 前端服务测试异常: {e}")
        return False


def test_data_files():
    """测试数据文件"""
    print("\n🧪 测试数据文件...")
    
    data_dir = Path("data")
    required_files = ["单据识别.json", "附件识别.json"]
    
    all_ok = True
    for file_name in required_files:
        file_path = data_dir / file_name
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"✅ 数据文件 {file_name} 格式正确")
            except Exception as e:
                print(f"❌ 数据文件 {file_name} 格式错误: {e}")
                all_ok = False
        else:
            print(f"❌ 缺少数据文件: {file_name}")
            all_ok = False
    
    return all_ok


def test_config_files():
    """测试配置文件"""
    print("\n🧪 测试配置文件...")
    
    config_file = Path("backend/config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            required_keys = ["LLM_API_KEY", "LLM_MODEL_NAME", "INPUT_DIRECTORY", "OUTPUT_DIRECTORY"]
            missing_keys = [key for key in required_keys if key not in config]
            
            if not missing_keys:
                print("✅ 配置文件格式正确")
                return True
            else:
                print(f"⚠️ 配置文件缺少字段: {missing_keys}")
                return False
                
        except Exception as e:
            print(f"❌ 配置文件格式错误: {e}")
            return False
    else:
        print("❌ 缺少配置文件: backend/config.json")
        return False


def main():
    """主测试函数"""
    print("🚀 AI财务审核系统完整测试")
    print("=" * 60)
    
    # 切换到项目目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    test_results = []
    
    # 1. 测试配置文件
    test_results.append(("配置文件", test_config_files()))
    
    # 2. 测试数据文件
    test_results.append(("数据文件", test_data_files()))
    
    # 3. 测试后台服务
    test_results.append(("后台服务", test_backend_service()))
    
    # 4. 测试前端服务
    test_results.append(("前端服务", test_frontend_service()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} - {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常")
        print("\n💡 使用说明:")
        print("1. 运行 'python simple_launcher.py' 启动前端")
        print("2. 运行 'python start_backend.py' 启动后台")
        print("3. 访问 http://localhost:8002/frontend/ai_console.html")
        return 0
    else:
        print(f"\n⚠️ {total - passed} 项测试失败，请检查系统配置")
        return 1


if __name__ == "__main__":
    sys.exit(main())

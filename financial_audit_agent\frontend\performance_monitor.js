// 系统性能监控和数据持久化
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            pageLoad: 0,
            apiCalls: [],
            errors: [],
            userActions: [],
            systemResources: []
        };
        
        this.init();
    }
    
    init() {
        this.measurePageLoad();
        this.setupAPIMonitoring();
        this.setupUserActionTracking();
        this.setupSystemResourceMonitoring();
        this.setupDataPersistence();
        
        // 定期收集性能数据
        setInterval(() => {
            this.collectSystemMetrics();
        }, 5000);
        
        // 定期保存数据
        setInterval(() => {
            this.saveMetricsToStorage();
        }, 30000);
    }
    
    measurePageLoad() {
        // 测量页面加载性能
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            this.metrics.pageLoad = {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalTime: navigation.loadEventEnd - navigation.fetchStart,
                timestamp: new Date().toISOString()
            };
            
            console.log('📊 页面加载性能:', this.metrics.pageLoad);
        });
    }
    
    setupAPIMonitoring() {
        // 监控API调用性能
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0];
            
            try {
                const response = await originalFetch(...args);
                const endTime = performance.now();
                
                this.recordAPICall({
                    url,
                    method: args[1]?.method || 'GET',
                    status: response.status,
                    duration: endTime - startTime,
                    success: response.ok,
                    timestamp: new Date().toISOString()
                });
                
                return response;
            } catch (error) {
                const endTime = performance.now();
                
                this.recordAPICall({
                    url,
                    method: args[1]?.method || 'GET',
                    status: 0,
                    duration: endTime - startTime,
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                
                throw error;
            }
        };
    }
    
    recordAPICall(callData) {
        this.metrics.apiCalls.push(callData);
        
        // 保持最近100次调用记录
        if (this.metrics.apiCalls.length > 100) {
            this.metrics.apiCalls.shift();
        }
        
        // 更新实时性能显示
        this.updatePerformanceDisplay();
    }
    
    setupUserActionTracking() {
        // 跟踪用户操作
        const trackableEvents = ['click', 'scroll', 'keydown'];
        
        trackableEvents.forEach(eventType => {
            document.addEventListener(eventType, (event) => {
                this.recordUserAction({
                    type: eventType,
                    target: event.target.tagName,
                    targetId: event.target.id,
                    targetClass: event.target.className,
                    timestamp: new Date().toISOString()
                });
            });
        });
    }
    
    recordUserAction(actionData) {
        this.metrics.userActions.push(actionData);
        
        // 保持最近50次操作记录
        if (this.metrics.userActions.length > 50) {
            this.metrics.userActions.shift();
        }
    }
    
    setupSystemResourceMonitoring() {
        // 监控系统资源使用
        if ('memory' in performance) {
            this.collectMemoryMetrics();
        }
        
        this.collectNetworkMetrics();
    }
    
    collectMemoryMetrics() {
        const memory = performance.memory;
        
        const memoryData = {
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
            timestamp: new Date().toISOString()
        };
        
        this.metrics.systemResources.push({
            type: 'memory',
            data: memoryData
        });
    }
    
    collectNetworkMetrics() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            const networkData = {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt,
                saveData: connection.saveData,
                timestamp: new Date().toISOString()
            };
            
            this.metrics.systemResources.push({
                type: 'network',
                data: networkData
            });
        }
    }
    
    collectSystemMetrics() {
        // 收集综合系统指标
        const now = performance.now();
        
        // 计算API调用成功率
        const recentAPICalls = this.metrics.apiCalls.slice(-10);
        const successRate = recentAPICalls.length > 0 
            ? (recentAPICalls.filter(call => call.success).length / recentAPICalls.length) * 100
            : 100;
        
        // 计算平均响应时间
        const avgResponseTime = recentAPICalls.length > 0
            ? recentAPICalls.reduce((sum, call) => sum + call.duration, 0) / recentAPICalls.length
            : 0;
        
        // 更新性能显示
        this.updateRealPerformanceMetrics({
            apiSuccessRate: successRate,
            avgResponseTime: avgResponseTime,
            timestamp: now
        });
    }
    
    updateRealPerformanceMetrics(metrics) {
        // 更新真实的性能数据到界面
        const cpuMetric = document.querySelector('.metric-fill');
        const memoryMetric = document.querySelectorAll('.metric-fill')[1];
        const aiSpeedMetric = document.querySelectorAll('.metric-fill')[2];
        
        if (cpuMetric) {
            // 基于API成功率模拟CPU使用率
            const cpuUsage = Math.max(20, 100 - metrics.apiSuccessRate);
            cpuMetric.style.width = `${cpuUsage}%`;
            cpuMetric.parentElement.nextElementSibling.textContent = `${Math.round(cpuUsage)}%`;
        }
        
        if (memoryMetric && 'memory' in performance) {
            const memory = performance.memory;
            const memoryUsage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
            memoryMetric.style.width = `${memoryUsage}%`;
            memoryMetric.parentElement.nextElementSibling.textContent = `${Math.round(memoryUsage)}%`;
        }
        
        if (aiSpeedMetric) {
            // 基于响应时间计算AI推理速度
            const speedScore = Math.max(60, 100 - (metrics.avgResponseTime / 10));
            aiSpeedMetric.style.width = `${speedScore}%`;
            aiSpeedMetric.parentElement.nextElementSibling.textContent = `${Math.round(speedScore)}%`;
        }
    }
    
    updatePerformanceDisplay() {
        // 在控制台显示性能摘要
        const summary = this.getPerformanceSummary();
        
        if (window.console && console.groupCollapsed) {
            console.groupCollapsed('📊 性能监控摘要');
            console.log('API调用统计:', summary.apiStats);
            console.log('页面性能:', summary.pagePerformance);
            console.log('用户活动:', summary.userActivity);
            console.groupEnd();
        }
    }
    
    getPerformanceSummary() {
        const recentAPICalls = this.metrics.apiCalls.slice(-20);
        
        return {
            apiStats: {
                totalCalls: this.metrics.apiCalls.length,
                successRate: recentAPICalls.length > 0 
                    ? (recentAPICalls.filter(call => call.success).length / recentAPICalls.length) * 100
                    : 100,
                avgResponseTime: recentAPICalls.length > 0
                    ? recentAPICalls.reduce((sum, call) => sum + call.duration, 0) / recentAPICalls.length
                    : 0
            },
            pagePerformance: this.metrics.pageLoad,
            userActivity: {
                totalActions: this.metrics.userActions.length,
                recentActions: this.metrics.userActions.slice(-10).length
            }
        };
    }
    
    setupDataPersistence() {
        // 设置数据持久化
        this.loadMetricsFromStorage();
        
        // 页面卸载时保存数据
        window.addEventListener('beforeunload', () => {
            this.saveMetricsToStorage();
        });
    }
    
    saveMetricsToStorage() {
        try {
            const dataToSave = {
                ...this.metrics,
                lastSaved: new Date().toISOString()
            };
            
            localStorage.setItem('ai_console_metrics', JSON.stringify(dataToSave));
            console.log('💾 性能数据已保存到本地存储');
        } catch (error) {
            console.warn('⚠️ 保存性能数据失败:', error);
        }
    }
    
    loadMetricsFromStorage() {
        try {
            const savedData = localStorage.getItem('ai_console_metrics');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                
                // 合并历史数据（保留最近的数据）
                this.metrics.apiCalls = [...(parsedData.apiCalls || [])].slice(-50);
                this.metrics.errors = [...(parsedData.errors || [])].slice(-20);
                this.metrics.userActions = [...(parsedData.userActions || [])].slice(-30);
                
                console.log('📂 已加载历史性能数据');
            }
        } catch (error) {
            console.warn('⚠️ 加载历史数据失败:', error);
        }
    }
    
    exportMetrics() {
        // 导出性能数据
        const exportData = {
            ...this.metrics,
            summary: this.getPerformanceSummary(),
            exportTime: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `ai_console_metrics_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        console.log('📊 性能数据已导出');
    }
    
    showPerformanceReport() {
        // 显示性能报告
        const summary = this.getPerformanceSummary();
        
        const modal = document.createElement('div');
        modal.className = 'modal-overlay performance-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>📊 系统性能报告</h2>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="performance-section">
                        <h3>🌐 API性能</h3>
                        <div class="metric-row">
                            <span>总调用次数:</span>
                            <span>${summary.apiStats.totalCalls}</span>
                        </div>
                        <div class="metric-row">
                            <span>成功率:</span>
                            <span>${summary.apiStats.successRate.toFixed(1)}%</span>
                        </div>
                        <div class="metric-row">
                            <span>平均响应时间:</span>
                            <span>${summary.apiStats.avgResponseTime.toFixed(0)}ms</span>
                        </div>
                    </div>
                    
                    <div class="performance-section">
                        <h3>⚡ 页面性能</h3>
                        <div class="metric-row">
                            <span>DOM加载时间:</span>
                            <span>${summary.pagePerformance?.domContentLoaded || 0}ms</span>
                        </div>
                        <div class="metric-row">
                            <span>完全加载时间:</span>
                            <span>${summary.pagePerformance?.totalTime || 0}ms</span>
                        </div>
                    </div>
                    
                    <div class="performance-section">
                        <h3>👤 用户活动</h3>
                        <div class="metric-row">
                            <span>总操作次数:</span>
                            <span>${summary.userActivity.totalActions}</span>
                        </div>
                        <div class="metric-row">
                            <span>最近操作:</span>
                            <span>${summary.userActivity.recentActions}</span>
                        </div>
                    </div>
                    
                    <div class="performance-actions">
                        <button class="perf-btn" onclick="performanceMonitor.exportMetrics()">
                            导出数据
                        </button>
                        <button class="perf-btn" onclick="performanceMonitor.clearMetrics()">
                            清除数据
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.addPerformanceStyles();
    }
    
    addPerformanceStyles() {
        if (document.getElementById('performance-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'performance-styles';
        style.textContent = `
            .performance-modal .modal-content {
                max-width: 500px;
            }
            .performance-section {
                margin: 20px 0;
                padding: 15px;
                background: rgba(0, 0, 0, 0.3);
                border-radius: 8px;
                border-left: 3px solid var(--accent-blue);
            }
            .performance-section h3 {
                margin: 0 0 15px 0;
                color: var(--accent-blue);
                font-size: 1.1rem;
            }
            .metric-row {
                display: flex;
                justify-content: space-between;
                margin: 8px 0;
                color: var(--text-secondary);
            }
            .metric-row span:last-child {
                color: var(--text-primary);
                font-weight: 600;
            }
            .performance-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
                margin-top: 25px;
            }
            .perf-btn {
                padding: 10px 20px;
                background: var(--accent-blue);
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .perf-btn:hover {
                background: var(--accent-purple);
                transform: translateY(-1px);
            }
        `;
        document.head.appendChild(style);
    }
    
    clearMetrics() {
        this.metrics = {
            pageLoad: 0,
            apiCalls: [],
            errors: [],
            userActions: [],
            systemResources: []
        };
        
        localStorage.removeItem('ai_console_metrics');
        console.log('🗑️ 性能数据已清除');
        
        // 关闭模态框
        const modal = document.querySelector('.performance-modal');
        if (modal) modal.remove();
    }
}

// 全局性能监控实例
let performanceMonitor;

// 初始化性能监控
document.addEventListener('DOMContentLoaded', () => {
    performanceMonitor = new PerformanceMonitor();
    
    // 添加性能报告快捷键
    document.addEventListener('keydown', (event) => {
        if (event.ctrlKey && event.shiftKey && event.key === 'P') {
            event.preventDefault();
            performanceMonitor.showPerformanceReport();
        }
    });
});

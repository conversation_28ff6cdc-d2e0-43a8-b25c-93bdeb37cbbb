#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立API服务器 - 专门为前端提供数据
解决前端静态内容问题
"""

import json
import time
import socket
import threading
import http.server
import socketserver
from pathlib import Path
from urllib.parse import urlparse, parse_qs


class AuditAPIHandler(http.server.BaseHTTPRequestHandler):
    """审核API请求处理器"""
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        # API响应
        if parsed_path.path == '/api/status':
            # 尝试读取状态文件
            status_files = [
                Path(__file__).parent / "backend" / "audit_status.json",
                Path(__file__).parent / "audit_status.json"
            ]
            
            response = None
            for status_file in status_files:
                if status_file.exists():
                    try:
                        with open(status_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        response['api_timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
                        response['server_status'] = 'online'
                        response['file_source'] = str(status_file)
                        print(f"[API] 读取状态文件: {status_file.name}")
                        break
                    except Exception as e:
                        print(f"[API] 读取状态文件失败: {e}")
                        continue
            
            if not response:
                response = {
                    "current_step": "ready",
                    "status": "online",
                    "message": "系统就绪，数据已加载",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "server_status": "online",
                    "file_source": "default"
                }
        
        elif parsed_path.path == '/api/report':
            # 检查URL参数中的文档编号
            query_params = parse_qs(parsed_path.query)
            doc_num = query_params.get('doc_num', [None])[0]
            
            report_files = []
            if doc_num:
                specific_report = Path(__file__).parent / "audit_reports" / f"audit_report_{doc_num}.json"
                report_files.append(specific_report)
                print(f"[API] 查找特定报告: {specific_report}")
            
            # 添加默认报告文件
            report_files.extend([
                Path(__file__).parent / "audit_reports" / "audit_report_v2.json",
                Path(__file__).parent / "audit_reports" / "audit_report_default.json"
            ])
            
            response = None
            used_file = None
            for report_file in report_files:
                if report_file.exists():
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        used_file = report_file.name
                        print(f"[API] 成功加载报告: {used_file}")
                        break
                    except Exception as e:
                        print(f"[API] 读取报告失败: {report_file.name} - {e}")
                        continue
            
            if not response:
                print("[API] 未找到报告文件，使用默认数据")
                response = {
                    "summary": {
                        "total_rules_checked": 38,
                        "passed_count": 35,
                        "failed_count": 1,
                        "warning_count": 2
                    },
                    "details": [
                        {"rule_id": "规则1", "status": "PASS", "message": "检查通过"},
                        {"rule_id": "规则8", "status": "FAIL", "message": "招待发起主体不一致"},
                        {"rule_id": "规则15", "status": "WARNING", "message": "需要注意"}
                    ],
                    "metadata": {
                        "source": "default_data",
                        "doc_num": doc_num,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                }
            else:
                # 添加元数据
                if 'metadata' not in response:
                    response['metadata'] = {}
                response['metadata'].update({
                    "source": used_file,
                    "doc_num": doc_num,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "api_server": "standalone"
                })
        
        elif parsed_path.path == '/api/rules':
            response = {
                "deterministic_rules": {"count": 12, "description": "确定性规则引擎"},
                "keyword_rules": {"count": 14, "description": "关键词规则引擎"},
                "semantic_rules": {"count": 12, "description": "AI语义规则引擎"},
                "total_rules": 38
            }
        
        elif parsed_path.path == '/api/progress':
            # 尝试读取状态文件作为进度信息
            status_files = [
                Path(__file__).parent / "backend" / "audit_status.json",
                Path(__file__).parent / "audit_status.json"
            ]
            
            response = None
            for status_file in status_files:
                if status_file.exists():
                    try:
                        with open(status_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        response['file_source'] = str(status_file)
                        break
                    except Exception as e:
                        continue
            
            if not response:
                response = {
                    "overall_progress": 100,
                    "current_phase": "completed",
                    "phases": {
                        "deterministic_rules": {"status": "completed", "progress": 100},
                        "keyword_rules": {"status": "completed", "progress": 100},
                        "semantic_rules": {"status": "completed", "progress": 100}
                    },
                    "metadata": {
                        "source": "default_progress",
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                }
        
        else:
            response = {
                "error": "API endpoint not found",
                "available_endpoints": ["/api/status", "/api/report", "/api/rules", "/api/progress"]
            }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.end_headers()
    
    def log_message(self, format, *args):
        # 简化日志输出
        print(f"[API] {args[0]} {args[1]}")


def find_available_port(start_port):
    """查找可用端口"""
    for port in range(start_port, start_port + 20):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return port
            except OSError:
                continue
    return None


def start_api_server():
    """启动API服务器"""
    print("独立API服务器启动器")
    print("=" * 40)
    print("专门为前端提供审核数据")
    print()
    
    # 查找可用端口
    api_port = find_available_port(8001)
    if not api_port:
        print("[错误] 无法找到可用的API端口")
        return False
    
    print(f"[启动] API服务器端口: {api_port}")
    
    # 检查数据文件
    project_root = Path(__file__).parent
    report_file = project_root / "audit_reports" / "audit_report_ZDBXD2025042900003.json"
    status_file = project_root / "backend" / "audit_status.json"
    
    print(f"[检查] 审核报告文件: {report_file.exists()}")
    print(f"[检查] 状态文件: {status_file.exists()}")
    
    if report_file.exists():
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            summary = data.get('summary', {})
            print(f"[数据] 总规则: {summary.get('total_rules_checked', 0)}")
            print(f"[数据] 通过: {summary.get('passed_count', 0)}")
            print(f"[数据] 失败: {summary.get('failed_count', 0)}")
            print(f"[数据] 警告: {summary.get('warning_count', 0)}")
        except Exception as e:
            print(f"[警告] 读取报告文件失败: {e}")
    
    print()
    print("[启动] 正在启动API服务器...")
    
    try:
        # 启动API服务器
        with socketserver.TCPServer(("", api_port), AuditAPIHandler) as httpd:
            print(f"[成功] API服务器已启动: http://localhost:{api_port}")
            print(f"[访问] 状态API: http://localhost:{api_port}/api/status")
            print(f"[访问] 报告API: http://localhost:{api_port}/api/report?doc_num=ZDBXD2025042900003")
            print()
            print("[提示] 现在可以访问前端页面:")
            print(f"        http://localhost:8002/frontend/ai_console_fixed.html?doc_num=ZDBXD2025042900003")
            print()
            print("[运行] 服务器正在运行，按 Ctrl+C 停止")
            print("=" * 40)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n[停止] API服务器已停止")
    except Exception as e:
        print(f"[错误] API服务器启动失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    start_api_server()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI思维链集成测试脚本
测试从audit_state.json提取AI思维链数据并集成到审核报告中的功能
"""

import json
import os
import sys
from pathlib import Path

# 添加路径以便导入模块
sys.path.append(str(Path(__file__).parent / "backend" / "auditor_v2"))

def test_ai_thinking_extraction():
    """测试AI思维链数据提取功能"""
    print("🔍 测试AI思维链数据提取功能")
    
    # 1. 检查audit_state.json文件
    state_file = Path("backend/audit_state.json")
    if not state_file.exists():
        print("❌ audit_state.json文件不存在")
        return False
    
    print("✅ 找到audit_state.json文件")
    
    # 2. 读取并分析状态文件
    with open(state_file, 'r', encoding='utf-8') as f:
        state_data = json.load(f)
    
    print(f"📊 状态文件分析:")
    print(f"  - 审核ID: {state_data.get('audit_id', 'N/A')}")
    print(f"  - 审核状态: {state_data.get('audit_status', 'N/A')}")
    print(f"  - 当前阶段: {state_data.get('current_phase', 'N/A')}")
    print(f"  - AI思维链长度: {len(state_data.get('ai_thinking', ''))}")
    
    # 3. 分析phases_history
    phases_history = state_data.get('phases_history', {})
    print(f"  - 阶段历史数量: {len(phases_history)}")
    
    for phase_key, phase_data in phases_history.items():
        if isinstance(phase_data, dict):
            ai_thinking_len = len(phase_data.get('ai_thinking', ''))
            print(f"    * {phase_key}: {ai_thinking_len} 字符")
    
    return True

def test_report_integration():
    """测试审核报告集成功能"""
    print("\n🔧 测试审核报告集成功能")

    try:
        from orchestrator_v2 import OrchestratorV2

        # 创建模拟配置和LLM调用器
        mock_config = {"test": True}
        mock_llm_caller = None  # 对于测试，我们不需要真实的LLM调用器

        # 创建编排器实例
        orchestrator = OrchestratorV2(mock_config, mock_llm_caller, "ZDBXD2025042900003")

        # 测试AI思维链提取方法
        ai_thinking_data = orchestrator._extract_ai_thinking_for_report()

        print("✅ AI思维链数据提取成功")
        print(f"📊 提取结果分析:")
        print(f"  - 组合思维链长度: {len(ai_thinking_data.get('combined_thinking', ''))}")
        print(f"  - 阶段历史数量: {len(ai_thinking_data.get('phases_history', {}))}")

        # 显示提取的元数据
        metadata = ai_thinking_data.get('extraction_metadata', {})
        print(f"  - 提取时间: {metadata.get('extracted_at', 'N/A')}")
        print(f"  - 审核ID: {metadata.get('audit_id', 'N/A')}")
        print(f"  - 审核状态: {metadata.get('audit_status', 'N/A')}")

        return True

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_enhanced_report_format():
    """测试增强的报告格式"""
    print("\n📋 测试增强的报告格式")
    
    # 模拟一个包含AI思维链的审核报告
    sample_report = {
        "summary": {
            "total_rules_checked": 4,
            "passed_count": 0,
            "failed_count": 4,
            "warning_count": 0
        },
        "details": [
            {
                "rule_id": "规则1：检查是否上传发票",
                "status": "FAIL",
                "message": "测试消息"
            }
        ],
        "review_comments": "测试审批意见",
        "ai_thinking_chain": {
            "combined_thinking": "这是组合的AI思维链内容...",
            "phases_history": {
                "phase1": {
                    "phase_name": "附件完整性检查",
                    "ai_thinking": "第一阶段的AI分析...",
                    "status": "completed",
                    "timestamp": "2025-07-28T14:13:50Z"
                }
            },
            "extraction_metadata": {
                "extracted_at": "2025-07-28 14:13:50",
                "audit_id": "ZDBXD2025042900003",
                "audit_status": "completed"
            }
        },
        "audit_metadata": {
            "version": "2.0",
            "audit_type": "stepwise_intelligent_audit",
            "timestamp": "2025-07-28 14:13:50",
            "document_number": "ZDBXD2025042900003",
            "ai_thinking_included": True
        }
    }
    
    # 保存示例报告
    sample_file = Path("audit_reports/sample_enhanced_report.json")
    sample_file.parent.mkdir(exist_ok=True)
    
    with open(sample_file, 'w', encoding='utf-8') as f:
        json.dump(sample_report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 增强报告格式示例已保存到: {sample_file}")
    print("📊 新增字段:")
    print("  - ai_thinking_chain: AI思维链完整数据")
    print("  - ai_thinking_chain.combined_thinking: 组合思维链")
    print("  - ai_thinking_chain.phases_history: 各阶段历史")
    print("  - ai_thinking_chain.extraction_metadata: 提取元数据")
    print("  - audit_metadata.ai_thinking_included: 包含标记")
    
    return True

def analyze_data_flow():
    """分析数据流"""
    print("\n🔄 分析数据流")
    
    print("当前数据流:")
    print("1. 前端AI控制台 → 显示ai_thinking内容")
    print("2. 后端audit_state.json → 存储思维链数据")
    print("3. 审核完成 → 生成最终报告JSON")
    
    print("\n增强后的数据流:")
    print("1. 前端AI控制台 → 显示ai_thinking内容")
    print("2. 后端audit_state.json → 存储思维链数据")
    print("3. 审核完成 → 从audit_state.json提取AI思维链")
    print("4. 最终报告JSON → 包含完整AI思维链数据")
    print("5. 审核文档 → 永久保存AI推理过程")
    
    return True

def main():
    """主测试函数"""
    print("🚀 AI思维链集成测试开始")
    print("=" * 50)
    
    # 切换到正确的工作目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    tests = [
        ("AI思维链数据提取", test_ai_thinking_extraction),
        ("审核报告集成", test_report_integration),
        ("增强报告格式", test_enhanced_report_format),
        ("数据流分析", analyze_data_flow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI思维链集成功能可行。")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")

if __name__ == "__main__":
    main()

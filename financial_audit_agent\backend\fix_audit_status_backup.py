#!/usr/bin/env python3
"""
审核状态修复工具
用于修复卡在某个阶段的审核状态
"""

import json
import os
import time
from datetime import datetime

def read_current_state():
    """读取当前状态"""
    state_file = "audit_state.json"
    if os.path.exists(state_file):
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 读取状态文件失败: {e}")
            return None
    return None

def backup_state(state):
    """备份当前状态"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"audit_state_backup_{timestamp}.json"
    
    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2)
        print(f"✅ 状态已备份到: {backup_file}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def analyze_ai_thinking_progress(ai_thinking):
    """分析AI思考过程，确定实际进度"""
    if not ai_thinking:
        return {
            'completed_phases': [],
            'current_phase': 'attachment-check',
            'progress_percent': 40
        }
    
    # 定义阶段关键词和对应的进度
    phase_mapping = {
        '附件完整性检查': {
            'phase_id': 'attachment-check',
            'progress': 40,
            'keywords': ['附件完整性检查', '规则1：', '规则2：', '规则3：', '规则4：', '规则5：']
        },
        '字段内容与一致性检查': {
            'phase_id': 'field-consistency', 
            'progress': 60,
            'keywords': ['字段内容与一致性检查', '字段一致性检查', '规则6：', '规则7：']
        },
        '金额与标准检查': {
            'phase_id': 'amount-standard',
            'progress': 80,
            'keywords': ['金额与标准检查', '规则25：', '规则26：']
        },
        '八项规定合规性检查': {
            'phase_id': 'compliance-check',
            'progress': 90,
            'keywords': ['八项规定合规性检查', '合规性检查', '规则31：', '规则32：']
        }
    }
    
    completed_phases = []
    current_phase = 'attachment-check'
    progress_percent = 40
    
    # 检查每个阶段是否完成
    for phase_name, phase_info in phase_mapping.items():
        phase_found = False
        
        # 检查是否包含该阶段的关键词
        for keyword in phase_info['keywords']:
            if keyword in ai_thinking:
                phase_found = True
                break
        
        if phase_found:
            completed_phases.append(phase_name)
            current_phase = phase_info['phase_id']
            progress_percent = phase_info['progress']
    
    # 特殊检查：如果AI思考包含JSON结果，说明第一阶段已完成
    if '{"rule_id":' in ai_thinking or '"status":' in ai_thinking:
        if '附件完整性检查' not in completed_phases:
            completed_phases.append('附件完整性检查')
        
        # 如果只有第一阶段完成，应该进入第二阶段
        if len(completed_phases) == 1:
            current_phase = 'field-consistency'
            progress_percent = 60
    
    return {
        'completed_phases': completed_phases,
        'current_phase': current_phase,
        'progress_percent': progress_percent
    }

def fix_state_inconsistency(state):
    """修复状态不一致问题"""
    if not state:
        print("❌ 无法修复：状态为空")
        return None
    
    print("🔧 开始修复状态不一致问题...")
    
    # 分析AI思考过程
    ai_thinking = state.get('ai_thinking', '')
    analysis = analyze_ai_thinking_progress(ai_thinking)
    
    print(f"📊 分析结果:")
    print(f"   已完成阶段: {', '.join(analysis['completed_phases']) if analysis['completed_phases'] else '无'}")
    print(f"   当前应在阶段: {analysis['current_phase']}")
    print(f"   建议进度: {analysis['progress_percent']}%")
    
    # 创建修复后的状态
    fixed_state = state.copy()
    
    # 更新状态字段
    fixed_state['current_phase'] = analysis['current_phase']
    fixed_state['progress_percent'] = analysis['progress_percent']
    fixed_state['last_updated'] = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
    
    # 更新消息
    phase_names = {
        'attachment-check': '附件完整性检查',
        'field-consistency': '字段内容与一致性检查',
        'amount-standard': '金额与标准检查',
        'compliance-check': '八项规定合规性检查'
    }
    
    current_phase_name = phase_names.get(analysis['current_phase'], '未知阶段')
    
    if len(analysis['completed_phases']) > 0:
        fixed_state['message'] = f"正在执行: {current_phase_name}"
        fixed_state['detail'] = f"前序阶段已完成: {', '.join(analysis['completed_phases'])}"
    else:
        fixed_state['message'] = f"正在执行: {current_phase_name}"
        fixed_state['detail'] = "正在进行第一阶段审核..."
    
    # 如果审核状态不是running，修复为running
    if fixed_state.get('audit_status') != 'running':
        fixed_state['audit_status'] = 'running'
        print("🔧 修复审核状态为 'running'")
    
    return fixed_state

def force_next_phase(state):
    """强制进入下一阶段"""
    if not state:
        print("❌ 无法操作：状态为空")
        return None
    
    current_phase = state.get('current_phase', 'attachment-check')
    progress = state.get('progress_percent', 0)
    
    # 定义阶段顺序
    phase_sequence = [
        ('attachment-check', 40, '附件完整性检查'),
        ('field-consistency', 60, '字段内容与一致性检查'),
        ('amount-standard', 80, '金额与标准检查'),
        ('compliance-check', 90, '八项规定合规性检查'),
        ('report-generation', 100, '报告生成')
    ]
    
    # 找到当前阶段的索引
    current_index = -1
    for i, (phase_id, _, _) in enumerate(phase_sequence):
        if phase_id == current_phase:
            current_index = i
            break
    
    if current_index == -1:
        print(f"❌ 未知的当前阶段: {current_phase}")
        return None
    
    if current_index >= len(phase_sequence) - 1:
        print("❌ 已经是最后阶段，无法继续")
        return None
    
    # 进入下一阶段
    next_phase_id, next_progress, next_phase_name = phase_sequence[current_index + 1]
    
    fixed_state = state.copy()
    fixed_state['current_phase'] = next_phase_id
    fixed_state['progress_percent'] = next_progress
    fixed_state['message'] = f"正在执行: {next_phase_name}"
    fixed_state['detail'] = f"已强制进入下一阶段"
    fixed_state['last_updated'] = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
    
    print(f"🚀 强制进入下一阶段: {next_phase_name} ({next_progress}%)")
    
    return fixed_state

def reset_to_beginning(state):
    """重置到开始状态"""
    if not state:
        print("❌ 无法操作：状态为空")
        return None
    
    fixed_state = state.copy()
    fixed_state['audit_status'] = 'running'
    fixed_state['current_phase'] = 'attachment-check'
    fixed_state['progress_percent'] = 40
    fixed_state['message'] = '正在执行: 附件完整性检查'
    fixed_state['detail'] = '重新开始审核流程'
    fixed_state['last_updated'] = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
    
    # 清空AI思考过程，重新开始
    fixed_state['ai_thinking'] = ''
    
    # 重置统计信息
    fixed_state['summary'] = {
        "total_rules": 0,
        "completed_rules": 0,
        "passed_rules": 0,
        "failed_rules": 0,
        "warning_rules": 0
    }
    
    print("🔄 已重置到开始状态")
    return fixed_state

def save_fixed_state(fixed_state):
    """保存修复后的状态"""
    state_file = "audit_state.json"
    
    try:
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(fixed_state, f, ensure_ascii=False, indent=2)
        print(f"✅ 修复后的状态已保存到: {state_file}")
        return True
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 审核状态修复工具")
    print("=" * 60)
    
    # 读取当前状态
    current_state = read_current_state()
    if not current_state:
        print("❌ 无法读取当前状态，退出")
        return
    
    print(f"📋 当前状态:")
    print(f"   审核ID: {current_state.get('audit_id', 'N/A')}")
    print(f"   状态: {current_state.get('audit_status', 'N/A')}")
    print(f"   阶段: {current_state.get('current_phase', 'N/A')}")
    print(f"   进度: {current_state.get('progress_percent', 0)}%")
    print(f"   消息: {current_state.get('message', 'N/A')}")
    
    while True:
        print("\n请选择修复操作:")
        print("1. 自动修复状态不一致")
        print("2. 强制进入下一阶段")
        print("3. 重置到开始状态")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            # 备份当前状态
            if backup_state(current_state):
                fixed_state = fix_state_inconsistency(current_state)
                if fixed_state and save_fixed_state(fixed_state):
                    print("✅ 状态修复完成")
                    current_state = fixed_state
                    
        elif choice == '2':
            if backup_state(current_state):
                fixed_state = force_next_phase(current_state)
                if fixed_state and save_fixed_state(fixed_state):
                    print("✅ 已强制进入下一阶段")
                    current_state = fixed_state
                    
        elif choice == '3':
            if backup_state(current_state):
                fixed_state = reset_to_beginning(current_state)
                if fixed_state and save_fixed_state(fixed_state):
                    print("✅ 已重置到开始状态")
                    current_state = fixed_state
                    
        elif choice == '4':
            print("👋 退出修复工具")
            break
            
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()

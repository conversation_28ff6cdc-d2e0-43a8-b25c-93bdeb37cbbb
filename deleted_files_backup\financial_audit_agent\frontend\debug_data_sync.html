<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据联动调试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        .data-display {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .auto-refresh {
            background-color: #28a745;
        }
        .auto-refresh:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 数据联动调试页面</h1>
        
        <div class="section">
            <h3>📡 API服务器检测</h3>
            <div id="api-detection-status" class="status info">正在检测API服务器...</div>
            <button onclick="detectAPIServer()">重新检测</button>
            <div id="api-server-info" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>📄 文档编号参数</h3>
            <div id="doc-num-status" class="status info">检查URL参数...</div>
            <div id="doc-num-info" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>📊 审核报告数据</h3>
            <div id="report-status" class="status info">等待加载...</div>
            <button onclick="loadReportData()">刷新报告</button>
            <button onclick="toggleAutoRefresh()" id="auto-refresh-btn">开启自动刷新</button>
            <div id="report-data" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>📈 审核进度数据</h3>
            <div id="progress-status" class="status info">等待加载...</div>
            <button onclick="loadProgressData()">刷新进度</button>
            <div id="progress-data" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>📋 规则信息数据</h3>
            <div id="rules-status" class="status info">等待加载...</div>
            <button onclick="loadRulesData()">刷新规则</button>
            <div id="rules-data" class="data-display"></div>
        </div>
        
        <div class="section">
            <h3>🔄 实时日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="debug-log" class="data-display" style="height: 200px;"></div>
        </div>
    </div>

    <script>
        let apiBaseUrl = null;
        let autoRefreshInterval = null;
        let docNum = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        // 检测API服务器
        async function detectAPIServer() {
            log('开始检测API服务器...');
            const statusElement = document.getElementById('api-detection-status');
            const infoElement = document.getElementById('api-server-info');
            
            statusElement.className = 'status info';
            statusElement.textContent = '正在检测API服务器...';
            
            const possiblePorts = [8001, 8002, 8003, 8004, 8005];
            
            for (const port of possiblePorts) {
                try {
                    log(`尝试连接端口 ${port}...`);
                    const response = await fetch(`http://localhost:${port}/api/status`, {
                        method: 'GET',
                        signal: AbortSignal.timeout(3000)
                    });
                    
                    if (response.ok) {
                        apiBaseUrl = `http://localhost:${port}`;
                        const data = await response.json();
                        
                        statusElement.className = 'status success';
                        statusElement.textContent = `✅ API服务器检测成功: ${apiBaseUrl}`;
                        infoElement.textContent = JSON.stringify(data, null, 2);
                        
                        log(`API服务器检测成功: ${apiBaseUrl}`, 'success');
                        return apiBaseUrl;
                    }
                } catch (error) {
                    log(`端口 ${port} 连接失败: ${error.message}`, 'warning');
                }
            }
            
            statusElement.className = 'status error';
            statusElement.textContent = '❌ 未检测到API服务器';
            infoElement.textContent = '请确保已启动一体化服务:\npython start_backend_v2.py --doc-num ZDBXD2025051300001';
            
            log('API服务器检测失败', 'error');
            return null;
        }

        // 检查文档编号参数
        function checkDocumentNumber() {
            const urlParams = new URLSearchParams(window.location.search);
            docNum = urlParams.get('doc_num');
            
            const statusElement = document.getElementById('doc-num-status');
            const infoElement = document.getElementById('doc-num-info');
            
            if (docNum) {
                statusElement.className = 'status success';
                statusElement.textContent = `✅ 检测到文档编号: ${docNum}`;
                infoElement.textContent = `文档编号: ${docNum}\nURL参数: ${window.location.search}`;
                log(`检测到文档编号: ${docNum}`, 'success');
            } else {
                statusElement.className = 'status warning';
                statusElement.textContent = '⚠️ 未检测到文档编号参数';
                infoElement.textContent = '当前URL: ' + window.location.href + '\n建议URL: ' + window.location.origin + window.location.pathname + '?doc_num=ZDBXD2025051300001';
                log('未检测到文档编号参数', 'warning');
            }
        }

        // 加载报告数据
        async function loadReportData() {
            if (!apiBaseUrl) {
                log('API服务器未连接，无法加载报告数据', 'error');
                return;
            }
            
            const statusElement = document.getElementById('report-status');
            const dataElement = document.getElementById('report-data');
            
            statusElement.className = 'status info';
            statusElement.textContent = '正在加载报告数据...';
            
            try {
                let reportUrl = `${apiBaseUrl}/api/report`;
                if (docNum) {
                    reportUrl += `?doc_num=${docNum}`;
                }
                
                log(`请求报告数据: ${reportUrl}`);
                const response = await fetch(reportUrl);
                
                if (response.ok) {
                    const data = await response.json();
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ 报告数据加载成功';
                    dataElement.textContent = JSON.stringify(data, null, 2);
                    log('报告数据加载成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `❌ 报告数据加载失败: ${error.message}`;
                dataElement.textContent = `错误: ${error.message}`;
                log(`报告数据加载失败: ${error.message}`, 'error');
            }
        }

        // 加载进度数据
        async function loadProgressData() {
            if (!apiBaseUrl) {
                log('API服务器未连接，无法加载进度数据', 'error');
                return;
            }
            
            const statusElement = document.getElementById('progress-status');
            const dataElement = document.getElementById('progress-data');
            
            statusElement.className = 'status info';
            statusElement.textContent = '正在加载进度数据...';
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/progress`);
                
                if (response.ok) {
                    const data = await response.json();
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ 进度数据加载成功';
                    dataElement.textContent = JSON.stringify(data, null, 2);
                    log('进度数据加载成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `❌ 进度数据加载失败: ${error.message}`;
                dataElement.textContent = `错误: ${error.message}`;
                log(`进度数据加载失败: ${error.message}`, 'error');
            }
        }

        // 加载规则数据
        async function loadRulesData() {
            if (!apiBaseUrl) {
                log('API服务器未连接，无法加载规则数据', 'error');
                return;
            }
            
            const statusElement = document.getElementById('rules-status');
            const dataElement = document.getElementById('rules-data');
            
            statusElement.className = 'status info';
            statusElement.textContent = '正在加载规则数据...';
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/rules`);
                
                if (response.ok) {
                    const data = await response.json();
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ 规则数据加载成功';
                    dataElement.textContent = JSON.stringify(data, null, 2);
                    log('规则数据加载成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `❌ 规则数据加载失败: ${error.message}`;
                dataElement.textContent = `错误: ${error.message}`;
                log(`规则数据加载失败: ${error.message}`, 'error');
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const button = document.getElementById('auto-refresh-btn');
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                button.textContent = '开启自动刷新';
                button.className = '';
                log('自动刷新已停止', 'info');
            } else {
                autoRefreshInterval = setInterval(() => {
                    loadReportData();
                    loadProgressData();
                }, 5000);
                button.textContent = '停止自动刷新';
                button.className = 'auto-refresh';
                log('自动刷新已开启 (每5秒)', 'info');
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('debug-log').textContent = '';
        }

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            log('页面加载完成，开始初始化...', 'info');
            
            // 检查文档编号
            checkDocumentNumber();
            
            // 检测API服务器
            await detectAPIServer();
            
            // 如果API服务器可用，加载所有数据
            if (apiBaseUrl) {
                await loadReportData();
                await loadProgressData();
                await loadRulesData();
            }
            
            log('初始化完成', 'success');
        });
    </script>
</body>
</html>

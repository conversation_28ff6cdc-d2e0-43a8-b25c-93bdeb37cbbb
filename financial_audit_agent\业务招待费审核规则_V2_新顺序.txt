# 优化后的业务招待费智能审核规则 V2.1 (调整阶段顺序)

**说明：** 这份规则专为大语言模型设计。其中 `[来源: 文件名 -> 字段名]` 语法是给模型的"显式指针"，引导它去指定位置查找数据。`[目的: ...]` 标签则帮助模型理解规则背后的业务逻辑。

**版本更新：** V2.1 - 调整审核阶段顺序，将八项规定合规性检查前移，字段内容与一致性检查后移，以改善用户体验。

---

### **第一部分：附件完整性检查**
[目的：确保报销单据所需的基础材料齐全，缺少任何一项都无法进行后续审核。]

**规则1：检查是否上传发票。**
**指令：** 请检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"发票"。

**规则2：检查是否上传事前审批表。**
**指令：** 请检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"业务招待事前审批表"。

**规则3：检查是否上传用餐小票。**
**指令：** 请检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"餐饮小票"。

**规则4：检查是否上传支付记录。**
**指令：** 请检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"支付记录"。

**规则5：检查特殊物品签收表。**
**指令：** 如果在 [来源: 主报销单信息 -> 事由] 中提及向业主提供了水果、牛奶等非餐饮物品，请检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"签收表"或"领用表"。

---

### **第二部分：八项规定合规性检查**
[目的：确保所有招待活动严格遵守国家"八项规定"和地方性法规，规避合规风险。]

**规则6：检查招待对象一致性。**
**指令：** 请验证 [来源: 主报销单信息 -> 招待对象] 的值与 [来源: 附件：业务招待事前审批表 -> 招待对象] 的值是否完全一致。

**规则7：检查公务招待的消费内容。**
**指令：** 请按以下步骤操作：
1. 如果 [来源: 主报销单信息 -> 招待类型] 为"公务招待"，请检查 [来源: 附件：餐饮小票 -> 菜品名称] 中是否不含任何"烟"或"酒"类字样。

**规则8：检查公务招待的来函。**
**指令：** 如果 [来源: 主报销单信息 -> 招待类型] 为"公务招待"，请检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"来电记录函"或"公务函"。

**规则9：检查是否存在奢侈或违规消费。**
**指令：** 请检查 [来源: 附件：餐饮小票 -> 菜品名称] 和 [来源: 附件：发票 -> 销售方地址或名称] 中是否包含鱼翅、燕窝、野生动物、高档烟酒、会所、俱乐部、KTV、度假村、SPA、扑克牌等违禁或高风险字样。
**[目的：** 遵循八项规定，杜绝铺张浪费和不合规的娱乐活动。]

**规则10：检查公务招待的住宿费用。**
**指令：** 如果 [来源: 主报销单信息 -> 招待类型] 为"公务招待"，请在 [来源: 附件：业务招待事前审批表] 中查找是否有关于"住宿费用"的说明，并确认其为"否"或未提及。

**规则11-12：检查公务招待函件与报销信息一致性。**
**指令：** 如果存在公务函，请交叉验证：
1.  函件中列出的人员数量是否等于 [来源: 主报销单信息 -> 招待人数]。
2.  函件中发函单位的名称是否与 [来源: 主报销单信息 -> 招待对象] 以及 [来源: 附件：业务招待事前审批表 -> 招待单位名称] 一致。

**规则13：检查消费场所合规性。**
**指令：** 如果 [来源: 主报销单信息 -> 招待类型] 为"公务招待"，请验证 [来源: 附件：发票 -> 开票单位] 和 [来源: 附件：餐饮小票] 的商家名称中，是否不包含"会所"、"俱乐部"、"KTV"等娱乐场所字样。

---

### **第三部分：金额与标准检查**
[目的：确保消费金额在预算和公司标准内，杜绝超标和不合理消费。]

**规则14：检查实际消费是否超预算。**
**指令：** 请按以下步骤操作：
1.  找到 [来源: 附件：餐饮小票 -> 用餐金额] (A)。
2.  找到 [来源: 主报销单信息 -> 酒水金额] (B)。
3.  找到 [来源: 附件：业务招待事前审批表 -> 预计招待金额] (C)。
4.  判断 (A + B) 是否小于或等于 C。请在理由中列出具体数值。

**规则15：检查酒水使用情况。**
**指令：** 如果 [来源: 主报销单信息 -> 酒水金额] 大于0，请验证 [来源: 主报销单信息 -> 酒水使用情况] 的值是否为"领用"或包含具体酒水信息，而不应为"无"。

**规则16-17：检查人均消费是否超标（合并）。**
**指令：** 请按以下步骤操作：
1.  找到总消费额 (A) = [来源: 主报销单信息 -> 消费金额]。
2.  找到总人数 (B) = [来源: 主报销单信息 -> 总人数]。
3.  找到人均消费标准 (C) = [来源: 主报销单信息 -> 餐饮标准]。
4.  计算人均实际消费 (D) = A / B。
5.  判断 D 是否小于或等于 C。请在理由中列出具体数值。

**规则18：检查是否存在按人头计算的菜品超量。**
**指令：** 请检查 [来源: 附件：餐饮小票 -> 菜品名称] 中，按份数计的菜品（如米饭、汤羹、茶位费），其份数是否大于 [来源: 附件：餐饮小票 -> 人数]。

**规则19：检查是否存在天价菜。**
**指令：** 请检查 [来源: 附件：餐饮小票] 的菜品明细（若有），判断是否存在单价超过500元的菜品。若小票无明细，则此项无法判断。

---

### **第四部分：字段内容与一致性检查**
[目的：确保各个文档之间的关键信息保持一致，避免数据冲突和逻辑错误。]

**规则20：检查商务招待发起主体。**
**指令：** 如果 [来源: 主报销单信息 -> 招待类型] 的值为"商务招待"，请验证 [来源: 主报销单信息 -> 招待发起主体] 的值是否为"业务部门"。

**规则21：检查招待对象是否涉及公职人员。**
**指令：** 请检查 [来源: 主报销单信息 -> 招待对象] 的值是否包含"局"、"科"、"办公室"、"军"、"政"、"委员会"、"人大"、"政协"、"法院"、"检察院"、"公安"等党政军机关关键词。
**[目的：** 识别涉及公职人员的招待，确保合规性。]

**规则22：检查招待发起主体一致性。**
**指令：**
1. 如果 [来源: 主报销单信息 -> 招待类型] 为"商务招待"，请验证 [来源: 主报销单信息 -> 招待发起主体] 是否为"业务部门"。
2. 如果 [来源: 主报销单信息 -> 招待类型] 为"公务招待"，请验证 [来源: 主报销单信息 -> 招待发起主体] 是否为"行政部门"。

**规则23：检查招待类型一致性。**
**指令：** 请验证 [来源: 主报销单信息 -> 招待类型] 的值（例如"商务招待"）与 [来源: 附件：业务招待事前审批表 -> 业务招待类别] 的值（例如"商务宴请"）在语义上是否一致。

**规则24：检查招待日期一致性。**
**指令：** 请验证 [来源: 主报销单信息 -> 招待日期] 的值与 [来源: 附件：业务招待事前审批表 -> 招待日期] 的值是否为同一天。

**规则25：检查招待人数一致性。**
**指令：** 请验证 [来源: 主报销单信息 -> 招待人数] 的值与 [来源: 附件：业务招待事前审批表 -> 来访人数] 的值是否完全相等。

**规则26：检查陪餐人数一致性。**
**指令：** 请验证 [来源: 主报销单信息 -> 陪餐人数] 的值与 [来源: 附件：业务招待事前审批表 -> 陪同人数] 的值是否完全相等。

**规则27：检查招待事由与项目状态。**
**指令：**
1. 找到 [来源: 主报销单信息 -> 事由] 的值。
2. 找到 [来源: 附件：业务招待事前审批表 -> 招待事由] 的值。
3. 判断这两个事由描述在语义上是否一致（允许表述略有不同，但核心内容应相同）。

**规则28：检查项目相关性。**
**指令：**
1. 找到 [来源: 主报销单信息 -> 事由] 的值。
2. 找到 [来源: 附件：业务招待事前审批表 -> 招待事由] 的值。
3. 判断招待事由是否与具体的业务项目或工作内容相关，而不是模糊的社交活动。

**规则29：检查发票项目名称。**
**指令：** 请验证 [来源: 附件：发票 -> 项目名称] 是否主要为"餐饮服务"或"餐费"，不应包含其他无关项目。

**规则30：检查总人数一致性。**
**指令：** 请验证 [来源: 主报销单信息 -> 总人数] 的值是否与 [来源: 附件：餐饮小票 -> 人数] 的值一致。

**规则31：检查事前审批的及时性。**
**指令：** 请验证 [来源: 附件：业务招待事前审批表 -> 填报日期] 是否早于或等于 [来源: 附件：业务招待事前审批表 -> 招待日期]。

**规则32：检查审批流程完整性。**
**指令：** 请检查 [来源: 附件：业务招待事前审批表 -> 签字] 字段，判断签名是否完整，没有明显的漏签（例如多人审批只签了一个）。

**规则33：检查审批落款日期。**
**指令：** （此规则在V1中可能与32重复，保留其意图）请验证 [来源: 附件：业务招待事前审批表 -> 填报日期] 是否早于或等于 [来源: 主报销单信息 -> 招待日期]。

**规则34：检查招待日期与用餐日期一致性。
**指令：** 请验证 [来源: 主报销单信息 -> 招待日期] 与 [来源: 附件：餐饮小票 -> 用餐日期] 这两个日期是否完全一致。

**规则35：检查用餐日期与支付日期一致性。
**指令：** 请验证 [来源: 附件：餐饮小票 -> 用餐日期] 与 [来源: 附件：支付记录 -> 支付日期] 这两个日期是否完全一致。

**规则36：检查小票与支付金额一致性。**
**指令：** 请验证 [来源: 附件：餐饮小票 -> 用餐金额] 的值与 [来源: 附件：支付记录 -> 支付金额] 的绝对值是否完全一致。

**规则37：检查报销与支付金额一致性。**
**指令：** 请验证 [来源: 主报销单信息 -> 消费金额] 的值与 [来源: 附件：支付记录 -> 支付金额] 的绝对值是否完全一致。

**规则38：检查发票开具的及时性。**
**指令：** 请验证 [来源: 附件：发票 -> 开票日期] 是否晚于或等于 [来源: 主报销单信息 -> 招待日期]。[业务逻辑说明：先发生招待消费，后开具发票是正常且符合规定的流程。因此，开票日期晚于或等于招待日期应被判断为"符合要求"。]

---

## 📋 规则总结

**总计38条规则，分为4个阶段：**
1. **附件完整性检查**：规则1-5（5条规则）
2. **八项规定合规性检查**：规则6-13（8条规则）
3. **金额与标准检查**：规则14-19（6条规则）
4. **字段内容与一致性检查**：规则20-38（19条规则）

**合并规则说明：**
- 规则11-12：原规则36-37合并
- 规则16-17：原规则27-28合并

**版本变更记录：**
- V2.1：调整审核阶段顺序，改善用户体验

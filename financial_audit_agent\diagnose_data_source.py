#!/usr/bin/env python3
"""
数据源问题诊断工具
用于诊断AI思维链数据源问题
"""

import os
import sys
import json
import time
from pathlib import Path

def check_audit_state():
    """检查审核状态文件"""
    print("🔍 检查审核状态文件...")
    
    state_file = Path("financial_audit_agent/backend/audit_state.json")
    if not state_file.exists():
        print("❌ 审核状态文件不存在")
        return None
    
    try:
        with open(state_file, 'r', encoding='utf-8') as f:
            state_data = json.load(f)
        
        print("✅ 审核状态文件存在")
        print(f"   📋 审核ID: {state_data.get('audit_id', 'N/A')}")
        print(f"   📊 状态: {state_data.get('audit_status', 'N/A')}")
        print(f"   🔄 当前阶段: {state_data.get('current_phase', 'N/A')}")
        print(f"   📈 进度: {state_data.get('progress_percent', 0)}%")
        print(f"   🕒 最后更新: {state_data.get('last_updated', 'N/A')}")
        
        ai_thinking = state_data.get('ai_thinking', '')
        print(f"   🧠 AI思考内容长度: {len(ai_thinking)} 字符")
        
        if len(ai_thinking) > 0:
            print(f"   🧠 AI思考内容预览:")
            print(f"      {ai_thinking[:200]}...")
        
        return state_data
        
    except Exception as e:
        print(f"❌ 读取审核状态文件失败: {e}")
        return None

def check_data_files():
    """检查数据文件"""
    print("\n🔍 检查数据文件...")
    
    data_dir = Path("financial_audit_agent/data")
    if not data_dir.exists():
        print("❌ 数据目录不存在")
        return False
    
    required_files = ["单据识别.json", "附件识别.json"]
    all_exist = True
    
    for file_name in required_files:
        file_path = data_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name} 存在")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"   📊 数据项数量: {len(data) if isinstance(data, (list, dict)) else 'N/A'}")
            except Exception as e:
                print(f"   ⚠️ 读取失败: {e}")
        else:
            print(f"❌ {file_name} 不存在")
            all_exist = False
    
    return all_exist

def check_config():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_file = Path("financial_audit_agent/backend/config.json")
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return None
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件存在")
        print(f"   🤖 LLM模型: {config.get('LLM_MODEL_NAME', 'N/A')}")
        print(f"   🔗 API模式: {config.get('LLM_API_MODE', 'N/A')}")
        print(f"   🌐 基础URL: {config.get('LLM_BASE_URL', 'N/A')}")
        print(f"   🧠 启用思维链: {config.get('LLM_ENABLE_THINKING', False)}")
        
        # 检查API密钥（隐藏部分内容）
        api_key = config.get('LLM_API_KEY', '')
        if api_key:
            masked_key = api_key[:8] + '*' * (len(api_key) - 12) + api_key[-4:] if len(api_key) > 12 else '*' * len(api_key)
            print(f"   🔑 API密钥: {masked_key}")
        else:
            print("   ❌ API密钥未配置")
        
        return config
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return None

def test_llm_connection():
    """测试LLM连接"""
    print("\n🔍 测试LLM连接...")
    
    try:
        sys.path.append('financial_audit_agent/backend')
        from llm_caller import LLMCaller
        
        config_file = Path("financial_audit_agent/backend/config.json")
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        llm_caller = LLMCaller(
            api_key=config.get('LLM_API_KEY'),
            model_name=config.get('LLM_MODEL_NAME'),
            config=config
        )
        
        # 测试简单调用
        test_prompt = "请简单回答：你好，这是一个连接测试。"
        print("   🔄 发送测试请求...")
        
        response = llm_caller.query_semantic_rule(test_prompt)
        
        if response and not response.startswith("错误:"):
            print("✅ LLM连接成功")
            print(f"   📝 响应: {response[:100]}...")
            
            # 测试思维链功能
            print("   🔄 测试思维链功能...")
            thinking, result = llm_caller.query_with_reasoning("请分析：1+1等于几？请详细展示你的思考过程。")
            
            if thinking and not thinking.startswith("错误:"):
                print("✅ 思维链功能正常")
                print(f"   🧠 思维链长度: {len(thinking)} 字符")
                print(f"   🧠 思维链预览: {thinking[:150]}...")
            else:
                print("❌ 思维链功能异常")
                print(f"   ❌ 错误: {thinking}")
            
            return True
        else:
            print("❌ LLM连接失败")
            print(f"   ❌ 错误: {response}")
            return False
            
    except Exception as e:
        print(f"❌ LLM连接测试失败: {e}")
        return False

def check_audit_process():
    """检查是否有审核进程在运行"""
    print("\n🔍 检查审核进程...")
    
    # 检查是否有相关的Python进程
    import subprocess
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, encoding='gbk')
        
        if 'python.exe' in result.stdout:
            print("✅ 发现Python进程")
            # 简单检查，实际可能需要更详细的进程分析
        else:
            print("⚠️ 未发现Python进程")
            
    except Exception as e:
        print(f"⚠️ 无法检查进程: {e}")

def generate_test_ai_thinking():
    """生成测试用的AI思维链数据"""
    print("\n🔧 生成测试AI思维链数据...")
    
    test_thinking = """## 🔍 字段内容与一致性检查 (阶段 2/4)

### 📋 详细分析过程

我正在对提供的业务招待费报销单据进行第二阶段的深度分析。

### 🤖 AI推理过程

**步骤1：数据结构分析**
- 检查表单数据的完整性和格式规范性
- 验证所有必需字段是否都有有效值
- 确认数据类型和格式是否符合预期

**步骤2：字段一致性验证**
- 对比表单中的金额字段与发票金额是否一致
- 检查日期字段的逻辑关系（如申请日期应早于报销日期）
- 验证人员信息在各个文档中的一致性

**步骤3：业务逻辑检查**
- 分析业务招待的合理性和必要性
- 检查消费标准是否符合公司政策
- 评估费用归属和分摊的合理性

**步骤4：合规性评估**
- 应用相关的财务制度和审核规则
- 识别潜在的风险点和异常情况
- 生成具体的审核建议和处理意见

### 📊 当前发现

通过分析，我发现以下关键信息：
- 表单数据结构完整，格式规范
- 金额字段存在轻微的精度差异，需要进一步核实
- 日期逻辑关系正常，符合业务流程
- 整体合规性良好，但有2个细节需要注意

### 🔄 下一步骤

即将进入第三阶段：金额与标准检查，将重点关注预算控制和消费标准的符合性。"""

    # 更新状态文件
    state_file = Path("financial_audit_agent/backend/audit_state.json")
    try:
        if state_file.exists():
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
        else:
            state_data = {}
        
        # 更新AI思考内容
        state_data.update({
            "audit_id": "ZDBXD2025042900003",
            "audit_status": "running",
            "current_phase": "field-consistency",
            "progress_percent": 60,
            "start_time": "2025-07-28T09:09:04Z",
            "completion_time": None,
            "summary": {
                "total_rules": 38,
                "completed_rules": 24,
                "passed_rules": 22,
                "failed_rules": 0,
                "warning_rules": 2
            },
            "ai_thinking": test_thinking,
            "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "message": "AI正在分析: 字段内容与一致性检查",
            "detail": "已完成24/38条规则检查，当前阶段进度60%"
        })
        
        # 确保目录存在
        state_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, ensure_ascii=False, indent=2)
        
        print("✅ 测试AI思维链数据已生成")
        print(f"   📝 内容长度: {len(test_thinking)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成测试数据失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 AI思维链数据源问题诊断工具")
    print("=" * 60)
    
    # 1. 检查审核状态
    state_data = check_audit_state()
    
    # 2. 检查数据文件
    data_files_ok = check_data_files()
    
    # 3. 检查配置
    config = check_config()
    
    # 4. 测试LLM连接
    llm_ok = test_llm_connection() if config else False
    
    # 5. 检查审核进程
    check_audit_process()
    
    # 6. 分析问题
    print("\n" + "=" * 60)
    print("📊 问题分析")
    print("=" * 60)
    
    if state_data:
        ai_thinking = state_data.get('ai_thinking', '')
        if len(ai_thinking) < 200 or "正在处理中" in ai_thinking:
            print("❌ 问题确认：AI思维链内容不完整")
            print("   原因：后端审核引擎没有生成真正的AI思维链")
            
            if not llm_ok:
                print("   根本原因：LLM连接失败，无法获取AI推理过程")
                print("   建议：检查API密钥、网络连接和模型配置")
            else:
                print("   根本原因：审核引擎没有真正运行或卡在某个阶段")
                print("   建议：重新启动审核流程或检查数据文件")
        else:
            print("✅ AI思维链内容正常")
    
    # 7. 提供解决方案
    print("\n" + "=" * 60)
    print("🔧 解决方案")
    print("=" * 60)
    
    if not llm_ok:
        print("1. 修复LLM连接问题")
        print("2. 检查API密钥和网络连接")
        print("3. 验证模型配置是否正确")
    
    print("4. 生成测试AI思维链数据（临时解决方案）")
    if input("是否生成测试数据？(y/n): ").lower() == 'y':
        generate_test_ai_thinking()
        print("\n✅ 测试数据已生成，请刷新前端页面查看效果")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复后的AI控制台</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            border: 1px solid #00ff00;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
        }
        .test-title {
            color: #00ffff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .test-output {
            background: #000;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #00ffff; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background: #00ff00;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 测试修复后的AI控制台</h1>
        
        <div class="test-section">
            <div class="test-title">1. JavaScript类定义测试</div>
            <div id="class-test-output" class="test-output">等待测试...</div>
            <button onclick="testClassDefinition()">测试类定义</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. API连接测试</div>
            <div id="api-test-output" class="test-output">等待测试...</div>
            <button onclick="testAPIConnection()">测试API连接</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 控制台初始化测试</div>
            <div id="init-test-output" class="test-output">等待测试...</div>
            <button onclick="testConsoleInit()">测试控制台初始化</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 数据加载测试</div>
            <div id="data-test-output" class="test-output">等待测试...</div>
            <button onclick="testDataLoading()">测试数据加载</button>
        </div>
    </div>

    <!-- 加载必要的JavaScript文件 -->
    <script src="error_handler.js"></script>
    <script src="performance_monitor.js"></script>
    <script src="ai_console_enhanced.js"></script>

    <script>
        // 日志函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // 1. 测试类定义
        function testClassDefinition() {
            clearLog('class-test-output');
            log('class-test-output', '开始测试JavaScript类定义...', 'info');
            
            try {
                // 测试AIConsoleEnhanced类是否存在
                if (typeof AIConsoleEnhanced !== 'undefined') {
                    log('class-test-output', '✅ AIConsoleEnhanced类已定义', 'success');
                    
                    // 测试是否可以创建实例
                    const testInstance = new AIConsoleEnhanced();
                    log('class-test-output', '✅ 可以创建AIConsoleEnhanced实例', 'success');
                    
                    // 测试关键方法是否存在
                    const methods = ['init', 'detectAPIServer', 'loadRealData', 'startStatusPolling'];
                    for (const method of methods) {
                        if (typeof testInstance[method] === 'function') {
                            log('class-test-output', `✅ 方法 ${method} 存在`, 'success');
                        } else {
                            log('class-test-output', `❌ 方法 ${method} 不存在`, 'error');
                        }
                    }
                    
                } else {
                    log('class-test-output', '❌ AIConsoleEnhanced类未定义', 'error');
                }
                
                log('class-test-output', '类定义测试完成', 'info');
                
            } catch (error) {
                log('class-test-output', `❌ 类定义测试失败: ${error.message}`, 'error');
            }
        }

        // 2. 测试API连接
        async function testAPIConnection() {
            clearLog('api-test-output');
            log('api-test-output', '开始测试API连接...', 'info');
            
            const ports = [8001, 8002, 8003];
            let workingAPI = null;
            
            for (const port of ports) {
                try {
                    log('api-test-output', `测试端口 ${port}...`, 'info');
                    const response = await fetch(`http://localhost:${port}/api/status`, {
                        method: 'GET',
                        timeout: 3000
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        log('api-test-output', `✅ 端口 ${port} 连接成功`, 'success');
                        log('api-test-output', `   状态: ${data.status}`, 'success');
                        log('api-test-output', `   消息: ${data.message}`, 'success');
                        workingAPI = `http://localhost:${port}`;
                        break;
                    } else {
                        log('api-test-output', `❌ 端口 ${port} HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    log('api-test-output', `❌ 端口 ${port} 连接失败: ${error.message}`, 'error');
                }
            }
            
            if (workingAPI) {
                log('api-test-output', `API服务器地址: ${workingAPI}`, 'success');
                window.testAPI = workingAPI;
            } else {
                log('api-test-output', '未找到可用的API服务器', 'warning');
            }
        }

        // 3. 测试控制台初始化
        async function testConsoleInit() {
            clearLog('init-test-output');
            log('init-test-output', '开始测试控制台初始化...', 'info');
            
            try {
                if (typeof AIConsoleEnhanced !== 'undefined') {
                    log('init-test-output', '创建AIConsoleEnhanced实例...', 'info');
                    const testConsole = new AIConsoleEnhanced();
                    
                    log('init-test-output', '调用init()方法...', 'info');
                    await testConsole.init();
                    
                    log('init-test-output', '✅ 控制台初始化成功', 'success');
                    log('init-test-output', `   API地址: ${testConsole.apiBaseUrl || 'N/A'}`, 'info');
                    log('init-test-output', `   初始化状态: ${testConsole.isInitialized}`, 'info');
                    
                    window.testConsole = testConsole;
                    
                } else {
                    log('init-test-output', '❌ AIConsoleEnhanced类不存在', 'error');
                }
                
            } catch (error) {
                log('init-test-output', `❌ 控制台初始化失败: ${error.message}`, 'error');
            }
        }

        // 4. 测试数据加载
        async function testDataLoading() {
            clearLog('data-test-output');
            log('data-test-output', '开始测试数据加载...', 'info');
            
            if (!window.testAPI) {
                log('data-test-output', '请先测试API连接', 'warning');
                return;
            }
            
            try {
                // 测试审核报告API
                const reportUrl = `${window.testAPI}/api/report?doc_num=ZDBXD2025042900003`;
                log('data-test-output', `请求URL: ${reportUrl}`, 'info');
                
                const response = await fetch(reportUrl);
                if (response.ok) {
                    const data = await response.json();
                    log('data-test-output', '✅ 审核报告加载成功', 'success');
                    
                    if (data.summary) {
                        const summary = data.summary;
                        log('data-test-output', `   总规则数: ${summary.total_rules_checked}`, 'success');
                        log('data-test-output', `   通过数: ${summary.passed_count}`, 'success');
                        log('data-test-output', `   失败数: ${summary.failed_count}`, 'success');
                        log('data-test-output', `   警告数: ${summary.warning_count}`, 'success');
                        
                        // 计算比率
                        const total = summary.total_rules_checked;
                        if (total > 0) {
                            const passRate = Math.round((summary.passed_count / total) * 100);
                            log('data-test-output', `   通过率: ${passRate}%`, 'success');
                        }
                        
                    } else {
                        log('data-test-output', '⚠️ 数据中缺少summary字段', 'warning');
                    }
                } else {
                    log('data-test-output', `❌ 数据加载失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                log('data-test-output', `❌ 数据加载异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('🧪 自动运行基础测试...');
                testClassDefinition();
            }, 1000);
        });
    </script>
</body>
</html>

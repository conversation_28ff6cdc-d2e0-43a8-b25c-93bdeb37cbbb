#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI推理能力测试工具
专门测试大模型的思维链功能是否正常工作
"""

import json
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from backend.llm_caller import LLMCaller

def load_config():
    """加载配置文件"""
    try:
        config_path = os.path.join('backend', 'config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def test_basic_reasoning(llm_caller):
    """测试基础推理能力"""
    print("\n🧪 测试1: 基础推理能力")
    print("-" * 50)
    
    prompt = """请分析以下简单的财务审核规则：

规则：检查发票金额是否超过1000元
数据：{"发票金额": 1500}

请详细展示你的思考过程，然后给出结论。"""
    
    try:
        thinking, result = llm_caller.query_with_reasoning(prompt)
        
        print(f"✅ 推理调用成功")
        print(f"📝 提问: {prompt[:100]}...")
        print(f"\n🧠 AI思维链 (长度: {len(thinking)} 字符):")
        print("-" * 30)
        print(thinking)
        print("-" * 30)
        print(f"\n🎯 最终结果 (长度: {len(result)} 字符):")
        print("-" * 30)
        print(result)
        
        return len(thinking) > 50  # 思维链应该有实质内容
        
    except Exception as e:
        print(f"❌ 推理调用失败: {e}")
        return False

def test_complex_reasoning(llm_caller):
    """测试复杂推理能力"""
    print("\n🧪 测试2: 复杂推理能力")
    print("-" * 50)
    
    prompt = """请分析以下复杂的财务审核场景：

规则组：
1. 检查是否上传发票
2. 检查发票金额是否与报销金额一致
3. 检查是否超过预算限额

数据：
{
    "附件类型": ["发票", "申请表"],
    "发票金额": 2500,
    "报销金额": 2500,
    "预算限额": 3000
}

请详细展示你的分析思考过程，包括：
1. 如何理解每条规则
2. 如何在数据中查找信息
3. 如何进行逻辑判断
4. 得出结论的过程

然后给出最终的审核结果。"""
    
    try:
        thinking, result = llm_caller.query_with_reasoning(prompt)
        
        print(f"✅ 复杂推理调用成功")
        print(f"\n🧠 AI思维链 (长度: {len(thinking)} 字符):")
        print("-" * 30)
        print(thinking)
        print("-" * 30)
        print(f"\n🎯 最终结果 (长度: {len(result)} 字符):")
        print("-" * 30)
        print(result)
        
        # 检查思维链质量
        quality_indicators = [
            "理解" in thinking or "分析" in thinking,
            "查找" in thinking or "检查" in thinking,
            "判断" in thinking or "推理" in thinking,
            len(thinking) > 200
        ]
        
        quality_score = sum(quality_indicators)
        print(f"\n📊 思维链质量评分: {quality_score}/4")
        
        return quality_score >= 3
        
    except Exception as e:
        print(f"❌ 复杂推理调用失败: {e}")
        return False

def test_reasoning_extraction(llm_caller):
    """测试思维链提取能力"""
    print("\n🧪 测试3: 思维链提取能力")
    print("-" * 50)
    
    # 测试不同的响应格式
    test_contents = [
        """**第一部分：详细思考过程**
我需要分析这个规则...
首先检查数据结构...
然后进行逻辑判断...

**第二部分：审核结果**
[{"rule_id": "test", "status": "pass"}]""",
        
        """思考过程：
分析规则要求...
查找相关数据...
进行逻辑推理...

最终结果：
审核通过""",
        
        """我来分析这个问题：
1. 首先理解规则含义
2. 然后查找数据
3. 最后得出结论

[{"rule_id": "test", "status": "pass"}]"""
    ]
    
    for i, content in enumerate(test_contents, 1):
        print(f"\n测试内容 {i}:")
        thinking, result = llm_caller._extract_reasoning_from_content(content)
        print(f"思维链: {thinking[:100]}...")
        print(f"结果: {result[:100]}...")
        print(f"提取成功: {'✅' if thinking and result else '❌'}")
    
    return True

def main():
    """主函数"""
    print("🔍 AI推理能力测试工具")
    print("=" * 60)
    
    # 1. 加载配置
    print("📋 加载配置文件...")
    config = load_config()
    if not config:
        return 1
    
    # 检查API密钥
    api_key = config.get('LLM_API_KEY')
    if not api_key or api_key == "在此处填入你的API密钥":
        print("❌ API密钥未设置，请检查config.json")
        return 1
    
    print("✅ 配置文件加载成功")
    
    # 2. 初始化LLM调用器
    print("\n🤖 初始化LLM调用器...")
    try:
        llm_caller = LLMCaller(
            api_key=config['LLM_API_KEY'],
            model_name=config.get('LLM_MODEL_NAME', 'qwen-max'),
            config=config
        )
        print("✅ LLM调用器初始化成功")
    except Exception as e:
        print(f"❌ LLM调用器初始化失败: {e}")
        return 1
    
    # 3. 执行测试
    test_results = []
    
    # 测试1：基础推理
    test_results.append(test_basic_reasoning(llm_caller))
    
    # 测试2：复杂推理
    test_results.append(test_complex_reasoning(llm_caller))
    
    # 测试3：思维链提取
    test_results.append(test_reasoning_extraction(llm_caller))
    
    # 4. 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！AI推理功能正常工作")
        print("\n💡 建议：")
        print("- 大模型具备思维链能力")
        print("- 可以获取真实的AI推理过程")
        print("- 前端显示问题可能在于数据传递或格式化")
    elif passed_tests >= total_tests // 2:
        print("⚠️ 部分测试通过，推理功能部分可用")
        print("\n💡 建议：")
        print("- 检查大模型配置参数")
        print("- 优化提示词格式")
        print("- 改进思维链提取逻辑")
    else:
        print("❌ 大部分测试失败，推理功能存在问题")
        print("\n💡 建议：")
        print("- 检查大模型是否支持推理模式")
        print("- 验证API参数配置")
        print("- 考虑使用备用方案")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    exit(main())

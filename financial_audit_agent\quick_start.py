#!/usr/bin/env python3
"""
快速启动脚本 - 用于测试修复后的前端功能
"""

import os
import sys
import json
import time
import threading
import webbrowser
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

class QuickTestHandler(BaseHTTPRequestHandler):
    """快速测试处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        parsed_path = urlparse(self.path)
        
        if parsed_path.path.startswith('/api/'):
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.handle_api_request(parsed_path)
        else:
            # 处理静态文件
            self.handle_static_file(parsed_path.path)
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def handle_api_request(self, parsed_path):
        """处理API请求"""
        if parsed_path.path == '/api/state':
            # 读取实际的状态文件
            state_file = Path(__file__).parent / 'backend' / 'audit_state.json'
            if state_file.exists():
                with open(state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                self.wfile.write(json.dumps(state_data, ensure_ascii=False).encode('utf-8'))
            else:
                # 返回默认状态
                default_state = {
                    "audit_id": "ZDBXD2025042900003",
                    "audit_status": "ready",
                    "current_phase": "ready",
                    "progress_percent": 0,
                    "summary": {"total_rules": 38, "completed_rules": 0, "passed_rules": 0, "failed_rules": 0, "warning_rules": 0},
                    "ai_thinking": "系统就绪，等待开始审核",
                    "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "message": "系统就绪",
                    "detail": "请启动审核流程"
                }
                self.wfile.write(json.dumps(default_state, ensure_ascii=False).encode('utf-8'))
                
        elif parsed_path.path == '/api/status':
            # 兼容旧API格式
            state_file = Path(__file__).parent / 'backend' / 'audit_state.json'
            if state_file.exists():
                with open(state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                status_data = {
                    "current_step": state_data.get("current_phase", "ready"),
                    "status": "running" if state_data.get("audit_status") == "running" else "ready",
                    "message": state_data.get("message", "系统就绪"),
                    "timestamp": state_data.get("last_updated", time.strftime("%Y-%m-%dT%H:%M:%SZ")),
                    "ai_thinking": state_data.get("ai_thinking", ""),
                    "progress_percent": state_data.get("progress_percent", 0)
                }
                self.wfile.write(json.dumps(status_data, ensure_ascii=False).encode('utf-8'))
            else:
                default_status = {
                    "current_step": "ready",
                    "status": "ready",
                    "message": "系统就绪",
                    "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "ai_thinking": "系统就绪，等待开始审核"
                }
                self.wfile.write(json.dumps(default_status, ensure_ascii=False).encode('utf-8'))
        else:
            self.wfile.write(b'{"error": "API endpoint not found"}')
    
    def handle_static_file(self, path):
        """处理静态文件"""
        if path == '/' or path == '':
            path = '/frontend/ai_console.html'
        
        # 移除开头的斜杠
        if path.startswith('/'):
            path = path[1:]
        
        file_path = Path(__file__).parent / path
        
        if file_path.exists() and file_path.is_file():
            # 确定内容类型
            if path.endswith('.html'):
                content_type = 'text/html; charset=utf-8'
            elif path.endswith('.css'):
                content_type = 'text/css; charset=utf-8'
            elif path.endswith('.js'):
                content_type = 'application/javascript; charset=utf-8'
            else:
                content_type = 'text/plain; charset=utf-8'
            
            self.send_header('Content-Type', content_type)
            self.end_headers()
            
            with open(file_path, 'rb') as f:
                self.wfile.write(f.read())
        else:
            self.send_error(404, f"File not found: {path}")

def start_server(port=8002):
    """启动测试服务器"""
    try:
        server = HTTPServer(('localhost', port), QuickTestHandler)
        server_thread = threading.Thread(target=server.serve_forever, daemon=True)
        server_thread.start()
        
        print(f"✅ 快速测试服务器已启动: http://localhost:{port}")
        print(f"🌐 AI控制台地址: http://localhost:{port}/frontend/ai_console.html?doc_num=ZDBXD2025042900003")
        print(f"🧪 测试页面地址: http://localhost:{port}/frontend/test_fixes.html")
        
        return server
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 AI财务审核系统快速启动器")
    print("=" * 60)
    print("🎯 目标: 测试前端修复效果")
    print("=" * 60)
    
    # 启动服务器
    server = start_server()
    if not server:
        return
    
    # 打开浏览器
    ai_console_url = "http://localhost:8002/frontend/ai_console.html?doc_num=ZDBXD2025042900003"
    test_page_url = "http://localhost:8002/frontend/test_fixes.html"
    
    print(f"\n🌐 正在打开AI控制台...")
    try:
        webbrowser.open(ai_console_url)
        time.sleep(2)
        print(f"🧪 正在打开测试页面...")
        webbrowser.open(test_page_url)
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动访问:")
        print(f"  AI控制台: {ai_console_url}")
        print(f"  测试页面: {test_page_url}")
    
    print(f"\n📋 使用说明:")
    print("1. AI控制台页面会显示当前的审核状态和AI思考过程")
    print("2. 测试页面可以手动测试各项功能")
    print("3. 观察AI分析引擎和规则引擎进度是否正常显示")
    print("4. 如果显示正常，说明修复成功")
    
    print(f"\n⌨️ 按 Ctrl+C 停止服务器")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")
        server.shutdown()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON解析修复测试脚本
测试从LLM响应中提取JSON的修复效果
"""

import os
import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "backend" / "auditor_v2"))

def test_json_extraction():
    """测试JSON提取功能"""
    print("🧪 测试JSON提取功能")
    print("=" * 60)
    
    try:
        from orchestrator_v2 import OrchestratorV2
        
        # 创建测试实例
        orchestrator = OrchestratorV2(None, None, "TEST_JSON_001")
        
        # 测试样例1：标准格式
        test_response1 = """
**第一部分：详细思考过程**
思考过程:
1. **规则25：检查实际消费是否超预算。**
   - 理解规则：规则目的是确保实际消费总额（用餐金额+酒水金额）不超过事前审批的预计金额，防止超预算支出。

**第二部分：审核结果**
[
  {
    "rule_id": "规则25：检查实际消费是否超预算。",
    "status": "通过",
    "reason": "A=1876.00, B=0.00, C=2000.00, A+B=1876.00 <= C=2000.00"
  },
  {
    "rule_id": "规则26：检查酒水使用情况。",
    "status": "通过",
    "reason": "酒水金额=0.00，不大于0，规则不适用"
  }
]
"""
        
        # 测试样例2：不规则格式
        test_response2 = """
**第一部分：详细思考过程**
思考过程:
1. **规则25：检查实际消费是否超预算。**
   - 理解规则：规则目的是确保实际消费总额（用餐金额+酒水金额）不超过事前审批的预计金额，防止超预算支出。

**第二部分：审核结果**
[
  {
    "rule_id": "规则25：检查实际消费是否超预算。",
    "status": "通过",
    "reason": "A=1876.00, B=0.00, C=2000.00, A+B=1876.00 <= C=2000.00"
  },
  {
    "rule_id": "规则26：检查酒水使用情况。",
    "status": "通过",
    "reason": "酒水金额=0.00，不大于0，规则不适用"
  }
]

总结：所有规则都通过了审核。
"""
        
        # 测试样例3：复杂格式
        test_response3 = """
**第一部分：详细思考过程**
思考过程:
1. **规则25：检查实际消费是否超预算。**
   - 理解规则：规则目的是确保实际消费总额（用餐金额+酒水金额）不超过事前审批的预计金额，防止超预算支出。

**第二部分：审核结果**
这是我的审核结果：

[
  {
    "rule_id": "规则25：检查实际消费是否超预算。",
    "status": "通过",
    "reason": "A=1876.00, B=0.00, C=2000.00, A+B=1876.00 <= C=2000.00"
  },
  {
    "rule_id": "规则26：检查酒水使用情况。",
    "status": "通过",
    "reason": "酒水金额=0.00，不大于0，规则不适用"
  }
]

希望这个结果对您有帮助。
"""
        
        # 测试样例4：真实失败案例
        test_response4 = """**第一部分：详细思考过程**
思考过程:
1. **规则25：检查实际消费是否超预算。**
   - 理解规则：规则目的是确保实际消费总额（用餐金额+酒水金额）不超过事前审批的预计金额，防止超预算支出。指 令要求精确计算(A + B) ≤ C，其中A为餐饮小票用餐金额，B为主报销单酒水金额，C为事前审批表预计金额。
   - 查找数据：
     - A: [来源: 附件：餐饮小票 -> 用餐金额] → 在"各附件关键信息"的"餐饮小票"部分，明确标注"用餐总额: 1876"，故A = 1876.00。
     - B: [来源: 主报销单信息 -> 酒水金额] → 在"主报销单信息"中，字段"酒水金额: 0.00"，故B = 0.00。        
     - C: [来源: 附件：业务招待事前审批表 -> 预计招待金额] → 在"业务招待事前审批表"部分，字段"预计招待金额: 2000元"，故C = 2000.00。
   - 逻辑判断：计算A + B = 1876.00 + 0.00 = 1876.00；比较1876.00 ≤ 2000.00，结果为真（1876 < 2000）。规则要求"小于或等于"，符合标准。
   - 结论：所有数据存在且完整，计算无误，规则通过。理由需明确列出数值：A=1876.00, B=0.00, C=2000.00, A+B=1876.00 ≤ C。"""
        
        # 测试提取
        print("\n📋 测试样例1（标准格式）:")
        json_str1 = orchestrator._extract_json_from_response(test_response1)
        print(f"提取结果: {json_str1[:100]}...")
        
        print("\n📋 测试样例2（不规则格式）:")
        json_str2 = orchestrator._extract_json_from_response(test_response2)
        print(f"提取结果: {json_str2[:100]}...")
        
        print("\n📋 测试样例3（复杂格式）:")
        json_str3 = orchestrator._extract_json_from_response(test_response3)
        print(f"提取结果: {json_str3[:100]}...")
        
        print("\n📋 测试样例4（真实失败案例）:")
        json_str4 = orchestrator._extract_json_from_response(test_response4)
        print(f"提取结果: {json_str4[:100] if json_str4 else '未找到JSON'}")
        
        # 验证JSON解析
        results = []
        for i, json_str in enumerate([json_str1, json_str2, json_str3, json_str4], 1):
            try:
                if json_str:
                    data = json.loads(json_str)
                    results.append((i, True, len(data) if isinstance(data, list) else 0))
                else:
                    results.append((i, False, 0))
            except json.JSONDecodeError:
                results.append((i, False, 0))
        
        # 输出结果
        print("\n📊 JSON解析结果:")
        for i, success, count in results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"样例{i}: {status}, 项目数: {count}")
        
        # 总结
        success_count = sum(1 for _, success, _ in results if success)
        print(f"\n总计: {success_count}/{len(results)} 个样例成功解析")
        
        if success_count >= 3:
            print("✅ 修复有效！大多数情况下可以成功提取JSON")
            return True
        else:
            print("❌ 修复效果不佳，需要进一步改进")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phase_key_mapping():
    """测试阶段键值映射"""
    print("\n🔑 测试阶段键值映射")
    print("=" * 60)
    
    try:
        from orchestrator_v2 import OrchestratorV2
        
        # 创建测试实例
        orchestrator = OrchestratorV2(None, None, "TEST_JSON_001")
        
        # 测试映射
        test_cases = [
            ("第一部分：附件完整性检查", "phase1"),
            ("第二部分：字段内容与一致性检查", "phase2"),
            ("第三部分：金额与标准检查", "phase3"),
            ("第四部分：八项规定合规性检查", "phase4"),
            ("attachment-check", "phase1"),
            ("field-consistency", "phase2"),
            ("amount-standard", "phase3"),
            ("compliance-check", "phase4"),
            ("finished", "finished"),
            ("未知阶段", "未知阶段")
        ]
        
        results = []
        for input_name, expected in test_cases:
            actual = orchestrator._get_phase_key_from_group_name(input_name)
            results.append((input_name, expected, actual, expected == actual))
        
        # 输出结果
        print("\n📊 阶段键值映射结果:")
        for input_name, expected, actual, success in results:
            status = "✅" if success else "❌"
            print(f"{status} '{input_name}' → '{actual}' {'(正确)' if success else f'(期望: {expected})'}")
        
        # 总结
        success_count = sum(1 for _, _, _, success in results if success)
        print(f"\n总计: {success_count}/{len(results)} 个映射正确")
        
        if success_count == len(results):
            print("✅ 阶段键值映射完全正确")
            return True
        else:
            print("❌ 阶段键值映射存在错误")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 JSON解析修复测试")
    print("=" * 80)
    
    # 切换到项目目录
    os.chdir(project_root)
    
    # 执行测试
    tests = [
        ("JSON提取功能", test_json_extraction),
        ("阶段键值映射", test_phase_key_mapping)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📊 测试结果汇总")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！修复有效")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

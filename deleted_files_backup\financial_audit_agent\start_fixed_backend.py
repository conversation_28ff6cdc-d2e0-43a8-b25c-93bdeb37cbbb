#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版后台启动器 - 解决404问题
"""

import os
import sys
import json
import argparse
import time
import socket
import threading
import webbrowser
import http.server
import socketserver
import subprocess
from pathlib import Path
from urllib.parse import urlparse


def find_available_port(start_port):
    """查找可用端口"""
    for port in range(start_port, start_port + 20):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return port
            except OSError:
                continue
    return None


def start_web_server(project_root, port=8002):
    """启动Web服务器"""
    
    class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()

        def log_message(self, format, *args):
            print(f"[WEB] {args[0]} {args[1]}")

    # 确保在正确的目录中启动
    original_cwd = os.getcwd()
    
    try:
        # 切换到项目根目录
        os.chdir(project_root)
        print(f"[WEB] 工作目录: {os.getcwd()}")
        
        # 验证关键文件
        frontend_dir = Path("frontend")
        console_file = frontend_dir / "ai_console.html"
        
        print(f"[WEB] 检查frontend目录: {frontend_dir.exists()}")
        print(f"[WEB] 检查ai_console.html: {console_file.exists()}")
        
        if not frontend_dir.exists():
            print(f"[错误] frontend目录不存在: {frontend_dir.absolute()}")
            return None
            
        if not console_file.exists():
            print(f"[错误] ai_console.html不存在: {console_file.absolute()}")
            return None
        
        # 查找可用端口
        web_port = find_available_port(port)
        if not web_port:
            print(f"[错误] 无法找到可用端口（从{port}开始）")
            return None
        
        # 启动Web服务器
        def run_web_server():
            try:
                with socketserver.TCPServer(("", web_port), CORSRequestHandler) as httpd:
                    print(f"[WEB] 服务器启动成功: http://localhost:{web_port}")
                    print(f"[WEB] AI控制台: http://localhost:{web_port}/frontend/ai_console.html")
                    httpd.serve_forever()
            except Exception as e:
                print(f"[错误] Web服务器异常: {e}")
                import traceback
                traceback.print_exc()
        
        # 在后台线程中启动
        web_thread = threading.Thread(target=run_web_server, daemon=True)
        web_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
        return web_port
        
    except Exception as e:
        print(f"[错误] Web服务器启动失败: {e}")
        return None
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)


def start_api_server(project_root, port=8001):
    """启动API服务器"""
    
    class AuditAPIHandler(http.server.BaseHTTPRequestHandler):
        def do_GET(self):
            parsed_path = urlparse(self.path)
            
            # 设置CORS头
            self.send_response(200)
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            
            # API响应
            if parsed_path.path == '/api/status':
                # 读取状态文件
                status_file = Path(project_root) / "backend" / "audit_status.json"
                
                if status_file.exists():
                    try:
                        with open(status_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        
                        # 添加引擎状态信息
                        current_step = response.get('current_step', 'ready')
                        if current_step == 'report-generation':
                            response['engine_status'] = {
                                'deterministic': {'completed': 12, 'total': 12, 'progress': 100},
                                'keyword': {'completed': 14, 'total': 14, 'progress': 100},
                                'semantic': {'completed': 12, 'total': 12, 'progress': 100}
                            }
                            response['progress'] = 100
                        
                        print(f"[API] 状态: {current_step}")
                        
                    except Exception as e:
                        print(f"[API] 读取状态文件失败: {e}")
                        response = {"error": f"Failed to read status: {e}"}
                else:
                    response = {
                        "current_step": "ready",
                        "status": "online",
                        "message": "系统就绪",
                        "progress": 0
                    }
            
            elif parsed_path.path.startswith('/api/report'):
                # 读取审核报告
                from urllib.parse import parse_qs
                query_params = parse_qs(parsed_path.query)
                doc_num = query_params.get('doc_num', [None])[0]
                
                if doc_num:
                    report_file = Path(project_root) / "audit_reports" / f"audit_report_{doc_num}.json"
                    if report_file.exists():
                        try:
                            with open(report_file, 'r', encoding='utf-8') as f:
                                response = json.load(f)
                            print(f"[API] 报告: {doc_num}")
                        except Exception as e:
                            response = {"error": f"Failed to read report: {e}"}
                    else:
                        response = {"error": "Report not found"}
                else:
                    response = {"error": "Document number required"}
            
            else:
                response = {"error": "API endpoint not found"}
            
            self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
        
        def log_message(self, format, *args):
            print(f"[API] {args[0]} {args[1]}")
    
    # 查找可用端口
    api_port = find_available_port(port)
    if not api_port:
        print(f"[错误] 无法找到可用API端口（从{port}开始）")
        return None
    
    # 启动API服务器
    def run_api_server():
        try:
            with socketserver.TCPServer(("", api_port), AuditAPIHandler) as httpd:
                print(f"[API] 服务器启动成功: http://localhost:{api_port}")
                httpd.serve_forever()
        except Exception as e:
            print(f"[错误] API服务器异常: {e}")
    
    # 在后台线程中启动
    api_thread = threading.Thread(target=run_api_server, daemon=True)
    api_thread.start()
    
    # 等待服务器启动
    time.sleep(1)
    
    return api_port


def run_audit_engine(project_root, doc_num):
    """运行审核引擎"""
    try:
        print(f"[审核] 开始审核文档: {doc_num}")
        
        # 构建命令
        orchestrator_path = Path(project_root) / "backend" / "auditor_v2" / "orchestrator_v2.py"
        
        if not orchestrator_path.exists():
            print(f"[错误] 审核引擎不存在: {orchestrator_path}")
            return False
        
        # 运行审核引擎
        cmd = [sys.executable, str(orchestrator_path), "--doc-num", doc_num]
        print(f"[审核] 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"[审核] 审核完成成功")
            return True
        else:
            print(f"[审核] 审核失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[错误] 审核引擎执行失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="修复版AI财务审核系统启动器")
    parser.add_argument("--doc-num", required=True, help="文档编号")
    parser.add_argument("--no-browser", action="store_true", help="不自动打开浏览器")
    parser.add_argument("--no-audit", action="store_true", help="只启动服务器，不运行审核")
    
    args = parser.parse_args()
    
    # 确定项目根目录
    project_root = Path(__file__).parent.absolute()
    print(f"[系统] 项目根目录: {project_root}")
    
    # 验证项目结构
    required_dirs = ["frontend", "backend", "audit_reports"]
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            print(f"[错误] 缺少必需目录: {dir_path}")
            return 1
        print(f"[验证] {dir_name}目录存在: ✓")
    
    print("\n" + "="*50)
    print("修复版AI财务审核系统启动器")
    print("="*50)
    
    # 1. 启动API服务器
    print("\n[步骤1] 启动API服务器...")
    api_port = start_api_server(project_root)
    
    # 2. 启动Web服务器
    print("\n[步骤2] 启动Web服务器...")
    web_port = start_web_server(project_root)
    
    if not web_port:
        print("[错误] Web服务器启动失败，无法继续")
        return 1
    
    # 3. 运行审核引擎（如果需要）
    if not args.no_audit:
        print(f"\n[步骤3] 运行审核引擎...")
        audit_success = run_audit_engine(project_root, args.doc_num)
        if not audit_success:
            print("[警告] 审核引擎执行失败，但Web服务继续运行")
    
    # 4. 打开浏览器（如果需要）
    if not args.no_browser:
        print(f"\n[步骤4] 打开浏览器...")
        console_url = f"http://localhost:{web_port}/frontend/ai_console.html?doc_num={args.doc_num}"
        try:
            time.sleep(2)  # 等待服务器完全启动
            webbrowser.open(console_url)
            print(f"[浏览器] 已打开: {console_url}")
        except Exception as e:
            print(f"[警告] 无法自动打开浏览器: {e}")
            print(f"[手动] 请访问: {console_url}")
    
    # 5. 显示服务信息
    print("\n" + "="*50)
    print("服务启动完成！")
    print("="*50)
    print(f"[Web服务] http://localhost:{web_port}")
    print(f"[AI控制台] http://localhost:{web_port}/frontend/ai_console.html?doc_num={args.doc_num}")
    if api_port:
        print(f"[API服务] http://localhost:{api_port}")
    print("="*50)
    
    # 6. 保持服务运行
    print("\n[运行] 服务正在运行...")
    print("[提示] 按 Ctrl+C 停止所有服务")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n[停止] 正在停止所有服务...")
        print("[完成] 所有服务已停止")
        return 0


if __name__ == "__main__":
    sys.exit(main())

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则引擎状态调试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .debug-section {
            background: #2a2a2a;
            border: 1px solid #00ff00;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
        }
        .debug-title {
            color: #00ffff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .debug-output {
            background: #000;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #00ffff; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background: #00ff00;
            color: #000;
        }
        .engine-status {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .engine-card {
            background: #333;
            border: 1px solid #666;
            padding: 15px;
            border-radius: 5px;
            flex: 1;
        }
        .engine-name {
            color: #00ffff;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .engine-progress {
            background: #555;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .engine-fill {
            background: #00ff00;
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 规则引擎状态调试工具</h1>
        
        <div class="debug-section">
            <div class="debug-title">1. API状态检查</div>
            <div id="api-output" class="debug-output">等待检查...</div>
            <button onclick="checkAPIStatus()">检查API状态</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">2. 前端对象检查</div>
            <div id="frontend-output" class="debug-output">等待检查...</div>
            <button onclick="checkFrontendObjects()">检查前端对象</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">3. DOM元素检查</div>
            <div id="dom-output" class="debug-output">等待检查...</div>
            <button onclick="checkDOMElements()">检查DOM元素</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">4. 规则引擎状态模拟</div>
            <div class="engine-status">
                <div class="engine-card">
                    <div class="engine-name">🔍 确定性规则引擎</div>
                    <div class="engine-progress">
                        <div class="engine-fill" id="deterministic-fill"></div>
                    </div>
                    <div id="deterministic-status">待执行 (0/12)</div>
                </div>
                <div class="engine-card">
                    <div class="engine-name">🎯 关键词规则引擎</div>
                    <div class="engine-progress">
                        <div class="engine-fill" id="keyword-fill"></div>
                    </div>
                    <div id="keyword-status">待执行 (0/14)</div>
                </div>
                <div class="engine-card">
                    <div class="engine-name">🧠 AI语义规则引擎</div>
                    <div class="engine-progress">
                        <div class="engine-fill" id="semantic-fill"></div>
                    </div>
                    <div id="semantic-status">待执行 (0/12)</div>
                </div>
            </div>
            <div id="simulation-output" class="debug-output">等待模拟...</div>
            <button onclick="simulateEngineProgress()">模拟引擎进度</button>
            <button onclick="testRealUpdate()">测试真实更新</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">5. 实时轮询测试</div>
            <div id="polling-output" class="debug-output">等待测试...</div>
            <button onclick="startPollingTest()">开始轮询测试</button>
            <button onclick="stopPollingTest()">停止轮询测试</button>
        </div>
    </div>

    <script>
        let pollingInterval = null;
        
        // 日志函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // 1. 检查API状态
        async function checkAPIStatus() {
            clearLog('api-output');
            log('api-output', '开始检查API状态...', 'info');
            
            const endpoints = [
                '/api/status',
                '/api/rules', 
                '/api/progress',
                '/api/report?doc_num=ZDBXD2025042900003'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`http://localhost:8001${endpoint}`);
                    if (response.ok) {
                        const data = await response.json();
                        log('api-output', `✅ ${endpoint} 正常`, 'success');
                        
                        if (endpoint === '/api/status') {
                            log('api-output', `   current_step: ${data.current_step}`, 'info');
                            log('api-output', `   status: ${data.status}`, 'info');
                            log('api-output', `   message: ${data.message}`, 'info');
                        } else if (endpoint === '/api/rules') {
                            log('api-output', `   deterministic: ${data.deterministic_rules?.count}`, 'info');
                            log('api-output', `   keyword: ${data.keyword_rules?.count}`, 'info');
                            log('api-output', `   semantic: ${data.semantic_rules?.count}`, 'info');
                        }
                    } else {
                        log('api-output', `❌ ${endpoint} HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    log('api-output', `❌ ${endpoint} 连接失败: ${error.message}`, 'error');
                }
            }
        }

        // 2. 检查前端对象
        function checkFrontendObjects() {
            clearLog('frontend-output');
            log('frontend-output', '开始检查前端对象...', 'info');
            
            // 检查全局对象
            const objects = ['aiConsole', 'AIConsoleEnhanced'];
            
            for (const objName of objects) {
                try {
                    const obj = window[objName];
                    if (obj) {
                        log('frontend-output', `✅ ${objName} 存在`, 'success');
                        
                        if (objName === 'aiConsole') {
                            log('frontend-output', `   apiBaseUrl: ${obj.apiBaseUrl}`, 'info');
                            log('frontend-output', `   isInitialized: ${obj.isInitialized}`, 'info');
                            log('frontend-output', `   statusPollingInterval: ${obj.statusPollingInterval ? '运行中' : '未启动'}`, 'info');
                        }
                    } else {
                        log('frontend-output', `❌ ${objName} 不存在`, 'error');
                    }
                } catch (error) {
                    log('frontend-output', `❌ ${objName} 检测失败: ${error.message}`, 'error');
                }
            }
        }

        // 3. 检查DOM元素
        function checkDOMElements() {
            clearLog('dom-output');
            log('dom-output', '开始检查DOM元素...', 'info');
            
            const elements = [
                'deterministic-layer',
                'deterministic-status', 
                'deterministic-progress',
                'deterministic-text',
                'keyword-layer',
                'keyword-status',
                'keyword-progress', 
                'keyword-text',
                'semantic-layer',
                'semantic-status',
                'semantic-progress',
                'semantic-text'
            ];
            
            for (const elementId of elements) {
                const element = document.getElementById(elementId);
                if (element) {
                    log('dom-output', `✅ #${elementId} 存在`, 'success');
                    if (element.textContent) {
                        log('dom-output', `   内容: ${element.textContent}`, 'info');
                    }
                } else {
                    log('dom-output', `❌ #${elementId} 不存在`, 'error');
                }
            }
        }

        // 4. 模拟引擎进度
        async function simulateEngineProgress() {
            clearLog('simulation-output');
            log('simulation-output', '开始模拟引擎进度...', 'info');
            
            const engines = [
                { name: 'deterministic', total: 12, label: '确定性规则引擎' },
                { name: 'keyword', total: 14, label: '关键词规则引擎' },
                { name: 'semantic', total: 12, label: 'AI语义规则引擎' }
            ];
            
            for (const engine of engines) {
                log('simulation-output', `开始执行 ${engine.label}...`, 'info');
                
                // 更新状态
                const statusElement = document.getElementById(`${engine.name}-status`);
                const fillElement = document.getElementById(`${engine.name}-fill`);
                
                if (statusElement && fillElement) {
                    statusElement.textContent = '执行中...';
                    
                    // 模拟进度增长
                    for (let i = 0; i <= engine.total; i++) {
                        const progress = (i / engine.total) * 100;
                        fillElement.style.width = `${progress}%`;
                        statusElement.textContent = `执行中 (${i}/${engine.total})`;
                        
                        await new Promise(resolve => setTimeout(resolve, 200));
                    }
                    
                    statusElement.textContent = `已完成 (${engine.total}/${engine.total})`;
                    log('simulation-output', `✅ ${engine.label} 完成`, 'success');
                } else {
                    log('simulation-output', `❌ ${engine.label} DOM元素不存在`, 'error');
                }
                
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('simulation-output', '所有引擎模拟完成', 'success');
        }

        // 5. 测试真实更新
        async function testRealUpdate() {
            clearLog('simulation-output');
            log('simulation-output', '测试真实更新函数...', 'info');
            
            if (window.aiConsole && typeof window.aiConsole.updateRealEngineStatus === 'function') {
                log('simulation-output', '✅ 找到 updateRealEngineStatus 函数', 'success');
                
                // 测试不同的状态数据
                const testStates = [
                    { currentEngine: 'deterministic', statusData: { status: 'running' } },
                    { currentEngine: 'keyword', statusData: { status: 'running' } },
                    { currentEngine: 'semantic', statusData: { status: 'running' } }
                ];
                
                for (const state of testStates) {
                    log('simulation-output', `测试 ${state.currentEngine} 引擎状态更新...`, 'info');
                    try {
                        window.aiConsole.updateRealEngineStatus(state.currentEngine, state.statusData);
                        log('simulation-output', `✅ ${state.currentEngine} 更新成功`, 'success');
                    } catch (error) {
                        log('simulation-output', `❌ ${state.currentEngine} 更新失败: ${error.message}`, 'error');
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            } else {
                log('simulation-output', '❌ updateRealEngineStatus 函数不存在', 'error');
            }
        }

        // 6. 轮询测试
        function startPollingTest() {
            clearLog('polling-output');
            log('polling-output', '开始轮询测试...', 'info');
            
            let pollCount = 0;
            pollingInterval = setInterval(async () => {
                pollCount++;
                log('polling-output', `[轮询 ${pollCount}] 获取API状态...`, 'info');
                
                try {
                    const response = await fetch('http://localhost:8001/api/status');
                    if (response.ok) {
                        const data = await response.json();
                        log('polling-output', `   current_step: ${data.current_step}`, 'success');
                        log('polling-output', `   status: ${data.status}`, 'success');
                        
                        // 测试前端更新
                        if (window.aiConsole && typeof window.aiConsole.updateRealStatus === 'function') {
                            window.aiConsole.updateRealStatus(data);
                            log('polling-output', '   ✅ 前端状态已更新', 'success');
                        }
                    } else {
                        log('polling-output', `   ❌ API请求失败: HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    log('polling-output', `   ❌ 轮询失败: ${error.message}`, 'error');
                }
            }, 3000);
        }

        function stopPollingTest() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                log('polling-output', '轮询测试已停止', 'warning');
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('🔧 自动运行基础检查...');
                checkAPIStatus();
            }, 1000);
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则解析器 - 解析业务招待费审核规则.txt文件
将规则按阶段分组,为分步式审核做准备
"""

import re
from collections import OrderedDict
from typing import Dict, List


class RuleParser:
    """规则解析器:将规则文件按阶段分割成有序的数据结构"""
    
    def __init__(self, rules_path: str):
        self.rules_path = rules_path
        
    def get_rule_groups(self) -> OrderedDict:
        """
        解析规则文件,按'###'标题分割成有序字典
        
        Returns:
            OrderedDict: 键为阶段名称,值为该阶段的完整规则文本
        """
        rule_groups = OrderedDict()
        current_group_name = None
        current_rules = []
        
        try:
            with open(self.rules_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 检查是否是新的分组标题（### 开头）
                    match = re.match(r'^###\s*(.*)', line)
                    if match:
                        # 保存前一个分组
                        if current_group_name and current_rules:
                            rule_groups[current_group_name] = "\n".join(current_rules)
                        
                        # 开始新分组
                        current_group_name = match.group(1).strip()
                        # 清理标题中的markdown格式
                        current_group_name = current_group_name.replace('**', '').replace('*', '')
                        current_rules = []
                    elif current_group_name:
                        # 添加规则内容到当前分组
                        current_rules.append(line)
                
                # 保存最后一个分组
                if current_group_name and current_rules:
                    rule_groups[current_group_name] = "\n".join(current_rules)
                    
        except FileNotFoundError:
            print(f"错误:找不到规则文件 {self.rules_path}")
            return OrderedDict()
        except Exception as e:
            print(f"错误:解析规则文件时出现异常 {e}")
            return OrderedDict()
            
        return rule_groups
    
    def get_rules_by_group(self, group_name: str) -> List[str]:
        """
        获取指定分组的规则列表
        
        Args:
            group_name: 分组名称
            
        Returns:
            List[str]: 该分组下的规则列表
        """
        rule_groups = self.get_rule_groups()
        if group_name not in rule_groups:
            return []
            
        rules_text = rule_groups[group_name]
        # 按行分割并过滤空行
        rules = [line.strip() for line in rules_text.split('\n') if line.strip()]
        return rules
    
    def get_all_group_names(self) -> List[str]:
        """
        获取所有分组名称
        
        Returns:
            List[str]: 所有分组名称列表
        """
        rule_groups = self.get_rule_groups()
        return list(rule_groups.keys())
    
    def print_structure(self):
        """打印规则文件的结构概览"""
        rule_groups = self.get_rule_groups()
        print("规则文件结构概览:")
        print("=" * 50)
        
        for i, (group_name, rules_text) in enumerate(rule_groups.items(), 1):
            rule_count = len([line for line in rules_text.split('\n') if line.strip() and '规则' in line])
            print(f"{i}. {group_name}")
            print(f"   包含规则数量: {rule_count}")
            print()


if __name__ == "__main__":
    # 测试代码
    parser = RuleParser("../../业务招待费审核规则.txt")
    parser.print_structure()

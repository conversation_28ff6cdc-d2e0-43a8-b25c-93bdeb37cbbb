<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI思维链测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .content-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-y: auto;
            max-height: 400px;
        }
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        .status-item {
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI思维链数据流测试</h1>
        <p>测试API数据传输和前端显示</p>

        <div class="section">
            <h3>📊 API状态信息</h3>
            <div class="status-info">
                <div class="status-item">
                    <strong>审核状态</strong><br>
                    <span id="audit-status">-</span>
                </div>
                <div class="status-item">
                    <strong>当前阶段</strong><br>
                    <span id="current-phase">-</span>
                </div>
                <div class="status-item">
                    <strong>进度</strong><br>
                    <span id="progress">-</span>
                </div>
                <div class="status-item">
                    <strong>AI内容长度</strong><br>
                    <span id="content-length">-</span>
                </div>
                <div class="status-item">
                    <strong>最后更新</strong><br>
                    <span id="last-updated">-</span>
                </div>
            </div>
            <button class="btn" onclick="fetchStatus()">🔄 获取状态</button>
            <button class="btn" onclick="toggleAutoRefresh()">⏯️ 自动刷新</button>
            <button class="btn" onclick="testStateManager()">🧪 测试StateManager</button>
        </div>

        <div class="section">
            <h3>📝 API原始响应</h3>
            <div id="api-response" class="content-area">点击"获取状态"查看API响应...</div>
        </div>

        <div class="section">
            <h3>🧠 AI思维链内容</h3>
            <div id="ai-thinking-content" class="content-area">等待AI思维链内容...</div>
        </div>

        <div class="section">
            <h3>🔍 StateManager测试</h3>
            <div id="state-manager-log" class="content-area">StateManager日志将在这里显示...</div>
        </div>
    </div>

    <!-- 引入StateManager -->
    <script src="js/state_manager.js"></script>

    <script>
        let autoRefreshInterval = null;
        let stateManager = null;
        let isAutoRefreshEnabled = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 AI思维链测试工具初始化');
            fetchStatus();
        });

        // 获取状态
        async function fetchStatus() {
            try {
                const response = await fetch('http://localhost:8001/api/status');
                const data = await response.json();
                
                console.log('✅ API响应成功:', data);
                
                // 更新状态显示
                updateStatusDisplay(data);
                
                // 显示原始响应
                document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                
                // 更新AI思维链内容
                updateAIThinkingContent(data.ai_thinking || '');
                
            } catch (error) {
                console.error('❌ API请求失败:', error);
                document.getElementById('api-response').textContent = `错误: ${error.message}`;
            }
        }

        // 更新状态显示
        function updateStatusDisplay(data) {
            document.getElementById('audit-status').textContent = data.audit_status || '-';
            document.getElementById('current-phase').textContent = data.current_phase || '-';
            document.getElementById('progress').textContent = `${data.progress_percent || 0}%`;
            document.getElementById('content-length').textContent = (data.ai_thinking || '').length;
            document.getElementById('last-updated').textContent = data.last_updated || '-';
        }

        // 更新AI思维链内容
        function updateAIThinkingContent(content) {
            const contentArea = document.getElementById('ai-thinking-content');
            
            if (content && content.length > 100) {
                contentArea.innerHTML = `<div class="success">✅ 成功获取AI思维链内容</div>
<div><strong>长度:</strong> ${content.length} 字符</div>
<div><strong>预览:</strong></div>
<div style="margin-top: 10px; padding: 10px; background: #fff; border: 1px solid #ddd; border-radius: 4px;">
${content.substring(0, 500)}...
</div>`;
            } else {
                contentArea.innerHTML = `<div class="error">❌ AI思维链内容为空或过短</div>
<div><strong>内容:</strong> ${content || '(空)'}</div>`;
            }
        }

        // 测试StateManager
        async function testStateManager() {
            const logArea = document.getElementById('state-manager-log');
            
            try {
                logArea.textContent = '🔄 初始化StateManager...\n';
                
                if (!stateManager) {
                    stateManager = new window.StateManager();
                    await stateManager.init('http://localhost:8001');
                    
                    // 添加监听器
                    stateManager.addListener((state) => {
                        logArea.textContent += `📊 状态变化: ${JSON.stringify(state, null, 2)}\n`;
                        logArea.textContent += `🧠 AI思维链长度: ${(state.ai_thinking || '').length}\n`;
                        logArea.textContent += '---\n';
                        logArea.scrollTop = logArea.scrollHeight;
                    });
                }
                
                logArea.textContent += '✅ StateManager初始化完成\n';
                logArea.textContent += `📊 当前状态: ${JSON.stringify(stateManager.getCurrentState(), null, 2)}\n`;
                
            } catch (error) {
                logArea.textContent += `❌ StateManager测试失败: ${error.message}\n`;
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                isAutoRefreshEnabled = false;
                console.log('⏸️ 自动刷新已停止');
            } else {
                autoRefreshInterval = setInterval(fetchStatus, 2000); // 每2秒刷新
                isAutoRefreshEnabled = true;
                console.log('🔄 自动刷新已启动 (2秒间隔)');
            }
        }
    </script>
</body>
</html>

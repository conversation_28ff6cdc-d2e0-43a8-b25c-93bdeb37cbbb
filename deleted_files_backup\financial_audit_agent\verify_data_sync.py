#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证前后端数据同步的最终测试脚本
"""

import json
import time
import requests
from pathlib import Path


def test_complete_data_flow():
    """测试完整的数据流"""
    print("=" * 60)
    print("前后端数据同步最终验证")
    print("=" * 60)
    
    # 1. 验证API服务器
    print("1. 验证API服务器...")
    try:
        response = requests.get('http://localhost:8001/api/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API服务器正常运行")
            print(f"   状态: {data.get('status')}")
            print(f"   消息: {data.get('message')}")
        else:
            print(f"❌ API服务器异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API服务器连接失败: {e}")
        return False
    
    # 2. 验证审核报告API
    print("\n2. 验证审核报告API...")
    try:
        response = requests.get('http://localhost:8001/api/report?doc_num=ZDBXD2025042900003', timeout=5)
        if response.status_code == 200:
            data = response.json()
            summary = data.get('summary', {})
            print(f"✅ 审核报告API正常")
            print(f"   总规则: {summary.get('total_rules_checked')}")
            print(f"   通过: {summary.get('passed_count')}")
            print(f"   失败: {summary.get('failed_count')}")
            print(f"   警告: {summary.get('warning_count')}")
            
            # 验证前端需要的数据格式
            required_fields = ['total_rules_checked', 'passed_count', 'failed_count', 'warning_count']
            missing_fields = [field for field in required_fields if field not in summary]
            
            if missing_fields:
                print(f"⚠️ 缺少必需字段: {missing_fields}")
            else:
                print("✅ 数据格式完整，前端可以正确解析")
                
        else:
            print(f"❌ 审核报告API异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 审核报告API连接失败: {e}")
        return False
    
    # 3. 验证Web服务器
    print("\n3. 验证Web服务器...")
    try:
        response = requests.get('http://localhost:8002/frontend/ai_console.html', timeout=5)
        if response.status_code == 200:
            print(f"✅ Web服务器正常运行")
            print(f"   前端页面可访问")
        else:
            print(f"❌ Web服务器异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Web服务器连接失败: {e}")
        return False
    
    # 4. 模拟前端数据更新
    print("\n4. 模拟前端数据更新...")
    
    # 更新状态文件以模拟实时数据变化
    status_file = Path(__file__).parent / "backend" / "audit_status.json"
    
    test_statuses = [
        {
            "current_step": "data-loading",
            "status": "running",
            "message": "正在加载数据文件...",
            "progress": 25,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "current_step": "rule-parsing",
            "status": "running", 
            "message": "正在解析审核规则...",
            "progress": 50,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "current_step": "audit-complete",
            "status": "completed",
            "message": "审核完成！",
            "progress": 100,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "final_stats": {
                "total_rules_checked": 38,
                "passed_count": 35,
                "failed_count": 1,
                "warning_count": 2,
                "total": 38,
                "passed": 35,
                "warning": 2,
                "failed": 1
            }
        }
    ]
    
    for i, status in enumerate(test_statuses, 1):
        print(f"\n   [{i}/3] 更新状态: {status['message']}")
        
        try:
            # 更新状态文件
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
            
            # 验证API能读取到更新
            time.sleep(1)
            response = requests.get('http://localhost:8001/api/status', timeout=3)
            if response.status_code == 200:
                api_data = response.json()
                if api_data.get('current_step') == status['current_step']:
                    print(f"   ✅ API成功读取更新: {api_data.get('message')}")
                else:
                    print(f"   ⚠️ API数据可能未及时更新")
            
            time.sleep(2)  # 等待前端轮询
            
        except Exception as e:
            print(f"   ❌ 状态更新失败: {e}")
    
    # 5. 生成前端验证指南
    print("\n5. 前端验证指南")
    print("-" * 40)
    print("请在浏览器中验证以下内容:")
    print("✅ 页面能够自动加载审核数据")
    print("✅ 统计数据正确显示（总计38，通过35，失败1，警告2）")
    print("✅ 通过率显示为92%")
    print("✅ 风险率显示为8%")
    print("✅ 浏览器控制台显示API调用日志")
    print("✅ 页面内容动态更新，不是静态显示")
    
    print("\n如果前端仍显示静态内容，请:")
    print("1. 打开浏览器开发者工具（F12）")
    print("2. 查看控制台是否有错误信息")
    print("3. 检查网络面板中的API调用")
    print("4. 刷新页面重新加载")
    
    return True


def generate_frontend_test_script():
    """生成前端测试脚本"""
    print("\n" + "=" * 60)
    print("前端测试脚本")
    print("=" * 60)
    print("请在浏览器控制台中运行以下代码来测试数据同步:")
    print()
    
    test_script = '''
// 前端数据同步测试脚本
console.log("🧪 开始前端数据同步测试");

// 1. 检查API连接
async function testAPI() {
    try {
        const response = await fetch('http://localhost:8001/api/status');
        const data = await response.json();
        console.log("✅ API连接正常:", data);
        return true;
    } catch (e) {
        console.log("❌ API连接失败:", e);
        return false;
    }
}

// 2. 加载审核报告
async function loadReport() {
    try {
        const response = await fetch('http://localhost:8001/api/report?doc_num=ZDBXD2025042900003');
        const data = await response.json();
        console.log("✅ 审核报告加载成功:", data.summary);
        
        // 手动更新前端显示
        if (window.aiConsole && window.aiConsole.updateRealStats) {
            window.aiConsole.updateRealStats(data.summary);
            console.log("✅ 手动更新前端统计完成");
        }
        
        return data;
    } catch (e) {
        console.log("❌ 审核报告加载失败:", e);
        return null;
    }
}

// 3. 检查前端对象
function checkFrontend() {
    console.log("🔍 检查前端对象:");
    console.log("   aiConsole存在:", !!window.aiConsole);
    console.log("   apiBaseUrl:", window.aiConsole?.apiBaseUrl);
    console.log("   realAuditData:", !!window.aiConsole?.realAuditData);
}

// 运行所有测试
async function runAllTests() {
    console.log("🚀 开始完整测试...");
    
    checkFrontend();
    
    const apiOk = await testAPI();
    if (apiOk) {
        await loadReport();
    }
    
    console.log("✅ 测试完成！如果数据仍未显示，请检查控制台错误信息。");
}

// 自动运行测试
runAllTests();
'''
    
    print(test_script)
    print("=" * 60)


def main():
    """主函数"""
    success = test_complete_data_flow()
    
    if success:
        generate_frontend_test_script()
        
        print("\n" + "=" * 60)
        print("验证完成！")
        print("=" * 60)
        print("✅ 后端API服务器正常运行")
        print("✅ 审核报告数据格式正确")
        print("✅ Web服务器正常提供前端页面")
        print("✅ 数据同步机制已建立")
        print()
        print("🌐 请访问: http://localhost:8002/frontend/ai_console.html?doc_num=ZDBXD2025042900003")
        print("🔧 如需调试，请在浏览器控制台运行上述测试脚本")
        print()
        print("如果前端仍显示静态内容，问题可能在于:")
        print("1. 前端JavaScript加载或执行错误")
        print("2. 浏览器缓存问题（尝试硬刷新 Ctrl+F5）")
        print("3. CORS或网络连接问题")
    else:
        print("\n❌ 验证失败，请检查服务器状态")


if __name__ == "__main__":
    main()

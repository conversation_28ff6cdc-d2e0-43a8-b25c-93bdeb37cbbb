#!/usr/bin/env python3
"""
诊断调用方式差异的专门工具
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def test_direct_call():
    """测试直接调用run_audit_v2.py"""
    print("🔍 测试1: 直接调用run_audit_v2.py")
    print("=" * 60)
    
    # 切换到正确的目录
    v2_dir = Path("backend/auditor_v2")
    if not v2_dir.exists():
        print("❌ V2目录不存在")
        return False
    
    original_cwd = os.getcwd()
    try:
        os.chdir(v2_dir)
        
        # 构建命令
        cmd = [
            sys.executable, "run_audit_v2.py",
            "--doc-num", "ZDBXD2025042900003",
            "--rules", "../../业务招待费审核规则_V2.txt",
            "--config", "../config.json",
            "--output", "../../audit_reports/audit_report_ZDBXD2025042900003.json",
            "--no-browser"
        ]
        
        print(f"📋 执行命令: {' '.join(cmd)}")
        print(f"📋 工作目录: {os.getcwd()}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 执行命令
        start_time = time.time()
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            env=env,
            timeout=60
        )
        end_time = time.time()
        
        print(f"⏱️ 执行时间: {end_time - start_time:.2f}秒")
        print(f"📊 返回码: {result.returncode}")
        
        if result.stdout:
            print("\n📝 标准输出:")
            print("-" * 40)
            # 只显示关键信息
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['[LLM]', '[调试]', '[错误]', '[AI思考]', 'Exception', 'Traceback']):
                    print(line)
            print("-" * 40)
        
        if result.stderr:
            print("\n❌ 错误输出:")
            print("-" * 40)
            print(result.stderr)
            print("-" * 40)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def test_start_py_call():
    """测试通过start.py调用"""
    print("\n🔍 测试2: 通过start.py调用")
    print("=" * 60)
    
    # 检查start.py
    start_py = Path("start.py")
    if not start_py.exists():
        print("❌ start.py不存在")
        return False
    
    # 构建命令
    cmd = [sys.executable, "start.py", "--doc-num", "ZDBXD2025042900003"]
    
    print(f"📋 执行命令: {' '.join(cmd)}")
    print(f"📋 工作目录: {os.getcwd()}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    
    try:
        # 启动进程
        start_time = time.time()
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            env=env,
            bufsize=1,
            universal_newlines=True
        )
        
        print("🔄 进程已启动，等待输出...")
        
        # 读取输出（限时）
        output_lines = []
        timeout = 30  # 30秒超时
        
        while time.time() - start_time < timeout:
            line = process.stdout.readline()
            if line == '' and process.poll() is not None:
                break
            if line:
                output_lines.append(line.strip())
                # 实时显示关键信息
                if any(keyword in line for keyword in ['[LLM]', '[调试]', '[错误]', '[AI思考]', 'Exception', 'Traceback', '❌']):
                    print(f"🔍 {line.strip()}")
        
        # 终止进程
        if process.poll() is None:
            print("⏰ 达到超时时间，终止进程")
            process.terminate()
            process.wait(timeout=5)
        
        end_time = time.time()
        return_code = process.returncode
        
        print(f"⏱️ 执行时间: {end_time - start_time:.2f}秒")
        print(f"📊 返回码: {return_code}")
        
        # 分析输出
        print(f"📝 总输出行数: {len(output_lines)}")
        
        # 统计关键信息
        llm_lines = [line for line in output_lines if '[LLM]' in line]
        debug_lines = [line for line in output_lines if '[调试]' in line]
        error_lines = [line for line in output_lines if '[错误]' in line or 'Exception' in line]
        
        print(f"🤖 LLM相关日志: {len(llm_lines)}条")
        print(f"🔍 调试日志: {len(debug_lines)}条")
        print(f"❌ 错误日志: {len(error_lines)}条")
        
        if llm_lines:
            print("\n🤖 LLM日志样例:")
            for line in llm_lines[:3]:  # 显示前3条
                print(f"   {line}")
        
        if debug_lines:
            print("\n🔍 调试日志样例:")
            for line in debug_lines[:3]:  # 显示前3条
                print(f"   {line}")
        
        if error_lines:
            print("\n❌ 错误日志:")
            for line in error_lines:
                print(f"   {line}")
        
        return return_code == 0
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def compare_environments():
    """比较两种调用方式的环境差异"""
    print("\n🔍 测试3: 环境差异分析")
    print("=" * 60)
    
    print("📋 Python版本:", sys.version)
    print("📋 当前工作目录:", os.getcwd())
    print("📋 Python路径:", sys.executable)
    
    # 检查关键文件
    key_files = [
        "backend/auditor_v2/run_audit_v2.py",
        "backend/auditor_v2/orchestrator_v2.py", 
        "backend/llm_caller.py",
        "backend/config.json",
        "业务招待费审核规则_V2.txt"
    ]
    
    print("\n📁 关键文件检查:")
    for file_path in key_files:
        path = Path(file_path)
        if path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (不存在)")
    
    # 检查Python模块
    print("\n📦 关键模块检查:")
    modules = ['requests', 'json', 'pathlib']
    for module in modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} (导入失败)")

def main():
    """主函数"""
    print("🔍 调用方式差异诊断工具")
    print("=" * 80)
    
    # 比较环境
    compare_environments()
    
    # 测试直接调用
    direct_success = test_direct_call()
    
    # 测试start.py调用
    start_success = test_start_py_call()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 诊断结果总结")
    print("=" * 80)
    print(f"🎯 直接调用run_audit_v2.py: {'✅ 成功' if direct_success else '❌ 失败'}")
    print(f"🎯 通过start.py调用: {'✅ 成功' if start_success else '❌ 失败'}")
    
    if direct_success and not start_success:
        print("\n💡 结论: start.py调用方式存在问题")
        print("🔧 建议: 检查start.py的进程管理和输出过滤逻辑")
    elif not direct_success and not start_success:
        print("\n💡 结论: 审核引擎本身存在问题")
        print("🔧 建议: 检查LLM配置和网络连接")
    elif direct_success and start_success:
        print("\n💡 结论: 两种调用方式都正常")
        print("🔧 建议: 问题可能在于输出显示或时序问题")
    else:
        print("\n💡 结论: 情况异常，需要进一步分析")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试双重写入修复效果
验证AI思维链和审核结果是否都能正确写入
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "backend" / "auditor_v2"))

def test_dual_writing_mechanism():
    """测试双重写入机制"""
    print("🔧 测试双重写入机制修复效果")
    print("=" * 80)
    
    try:
        from orchestrator_v2 import ReportFileManager
        
        test_doc_num = "TEST_DUAL_WRITING_001"
        manager = ReportFileManager(test_doc_num)
        
        # 模拟一个完整的阶段处理
        phase_key = "phase1"
        phase_name = "附件完整性检查"
        
        # 模拟AI思维链
        ai_thinking = """## 🔍 附件完整性检查 (阶段 1/4)

### 📋 开始分析
正在启动附件完整性检查的详细分析...

### 🔍 规则检查过程
1. **规则1：检查是否上传发票**
   - 分析数据结构中的发票信息
   - 验证发票文件的存在性和完整性
   - 结果：✅ 发票已上传且格式正确

2. **规则2：检查是否上传事前审批表**
   - 检查审批表的完整性
   - 验证审批流程的合规性
   - 结果：✅ 审批表已上传且内容完整

### 📊 阶段总结
附件完整性检查已完成，所有必要文件均已上传且格式正确。"""

        # 模拟审核结果
        audit_results = [
            {
                "rule_id": "规则1:检查是否上传发票.",
                "status": "通过",
                "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'发票', 满足要求."
            },
            {
                "rule_id": "规则2:检查是否上传事前审批表.",
                "status": "通过",
                "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'业务招待事前审批表', 满足要求."
            },
            {
                "rule_id": "规则3:检查是否上传用餐小票.",
                "status": "通过",
                "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'餐饮小票', 满足要求."
            }
        ]
        
        print(f"📝 模拟阶段: {phase_name}")
        print(f"   AI思维链长度: {len(ai_thinking)} 字符")
        print(f"   审核结果数量: {len(audit_results)} 条")
        
        # 1. 写入AI思维链
        print(f"\n1️⃣ 写入AI思维链...")
        manager.update_ai_thinking(
            phase_key=phase_key,
            ai_thinking=ai_thinking,
            phase_name=phase_name,
            status="completed",
            message="JSON解析成功，思维链和结果都已保存",
            detail=f"成功解析{len(audit_results)}条规则结果"
        )
        
        # 2. 写入审核结果
        print(f"2️⃣ 写入审核结果...")
        manager.update_audit_results(
            phase_key=phase_key,
            phase_results=audit_results,
            phase_name=phase_name
        )
        
        # 3. 验证写入结果
        print(f"\n📊 验证写入结果:")
        report_data = manager.get_report_data()
        
        # 检查AI思维链
        ai_chain = report_data.get("ai_thinking_chain", {})
        phases_history = ai_chain.get("phases_history", {})
        
        if phase_key in phases_history:
            phase_data = phases_history[phase_key]
            saved_thinking = phase_data.get("ai_thinking", "")
            
            print(f"   ✅ AI思维链写入成功")
            print(f"     - 阶段键值: {phase_key}")
            print(f"     - 阶段名称: {phase_data.get('phase_name', 'N/A')}")
            print(f"     - 思维链长度: {len(saved_thinking)} 字符")
            print(f"     - 状态: {phase_data.get('status', 'N/A')}")
            print(f"     - 时间戳: {phase_data.get('timestamp', 'N/A')}")
            
            # 验证内容完整性
            if "## 🔍 附件完整性检查" in saved_thinking:
                print(f"     - ✅ 思维链内容完整")
            else:
                print(f"     - ❌ 思维链内容不完整")
        else:
            print(f"   ❌ AI思维链写入失败: 未找到阶段 {phase_key}")
        
        # 检查审核结果
        details = report_data.get("details", [])
        summary = report_data.get("summary", {})
        
        if details:
            print(f"   ✅ 审核结果写入成功")
            print(f"     - 结果数量: {len(details)} 条")
            print(f"     - 总规则数: {summary.get('total_rules_checked', 0)}")
            print(f"     - 通过数量: {summary.get('passed_count', 0)}")
            print(f"     - 失败数量: {summary.get('failed_count', 0)}")
            print(f"     - 警告数量: {summary.get('warning_count', 0)}")
            
            # 验证阶段信息
            phase_keys = set(d.get("phase_key") for d in details)
            if phase_key in phase_keys:
                print(f"     - ✅ 阶段信息正确")
            else:
                print(f"     - ❌ 阶段信息错误")
        else:
            print(f"   ❌ 审核结果写入失败: details数组为空")
        
        # 检查combined_thinking
        combined_thinking = ai_chain.get("combined_thinking", "")
        if combined_thinking and combined_thinking != "审核分析进行中...":
            print(f"   ✅ 组合思维链已更新")
            print(f"     - 长度: {len(combined_thinking)} 字符")
        else:
            print(f"   ⚠️ 组合思维链未更新 (这是已知问题)")
        
        # 总体评估
        ai_success = phase_key in phases_history
        results_success = len(details) > 0
        
        if ai_success and results_success:
            print(f"\n🎉 双重写入机制修复成功!")
            print(f"   - AI思维链: ✅ 成功")
            print(f"   - 审核结果: ✅ 成功")
            return True
        else:
            print(f"\n❌ 双重写入机制仍有问题:")
            print(f"   - AI思维链: {'✅ 成功' if ai_success else '❌ 失败'}")
            print(f"   - 审核结果: {'✅ 成功' if results_success else '❌ 失败'}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'manager' in locals():
                if os.path.exists(manager.report_file_path):
                    os.remove(manager.report_file_path)
                    print("🧹 测试文件已清理")
        except:
            pass

def test_phase_key_mapping():
    """测试阶段键值映射修复"""
    print("\n🔑 测试阶段键值映射修复")
    print("=" * 80)
    
    try:
        from orchestrator_v2 import OrchestratorV2
        
        # 创建测试实例
        orchestrator = OrchestratorV2(None, None, "TEST_MAPPING_001")
        
        # 测试各种阶段名称格式
        test_cases = [
            ("第一部分：附件完整性检查", "phase1"),
            ("附件完整性检查", "phase1"),
            ("第二部分：字段内容与一致性检查", "phase2"),
            ("字段内容与一致性检查", "phase2"),
            ("第三部分：金额与标准检查", "phase3"),
            ("金额与标准检查", "phase3"),
            ("第四部分：八项规定合规性检查", "phase4"),
            ("八项规定合规性检查", "phase4"),
            ("attachment-check", "phase1"),
            ("field-consistency", "phase2"),
            ("amount-standard", "phase3"),
            ("compliance-check", "phase4"),
            ("finished", "finished"),
            ("未知阶段", "未知阶段")
        ]
        
        results = []
        for input_name, expected in test_cases:
            actual = orchestrator._get_phase_key_from_group_name(input_name)
            success = (expected == actual)
            results.append((input_name, expected, actual, success))
            
            status = "✅" if success else "❌"
            print(f"{status} '{input_name}' → '{actual}' {'(正确)' if success else f'(期望: {expected})'}")
        
        # 总结
        success_count = sum(1 for _, _, _, success in results if success)
        print(f"\n总计: {success_count}/{len(results)} 个映射正确")
        
        return success_count == len(results)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 双重写入机制修复验证")
    print("=" * 100)
    
    # 切换到项目目录
    os.chdir(project_root)
    
    # 执行测试
    tests = [
        ("双重写入机制", test_dual_writing_mechanism),
        ("阶段键值映射", test_phase_key_mapping)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📊 测试结果汇总")
    print("=" * 100)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 双重写入机制修复成功！")
        print("\n🎯 下一步行动:")
        print("1. 运行实际审核验证修复效果")
        print("2. 确认AI思维链和审核结果都能正确写入")
        print("3. 验证所有阶段的数据完整性")
        return 0
    else:
        print("⚠️ 双重写入机制仍需进一步修复")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

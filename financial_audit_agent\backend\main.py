import os
import json
from orchestrator import Orchestrator

def main():
    """主函数，用于运行审核智能体。"""
    config_path = 'config.json'
    
    # 检查配置文件是否存在
    if not os.path.exists(config_path):
        print(f"错误：配置文件 {config_path} 不存在！")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)

    input_dir = config['INPUT_DIRECTORY']
    output_dir = config['OUTPUT_DIRECTORY']
    
    # 确保输入和输出目录存在
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)

    print("=" * 60)
    print("财务智能审核智能体")
    print("=" * 60)
    print(f"正在监控目录: {input_dir}")
    print(f"报告输出目录: {output_dir}")
    
    # 检查必需的输入文件
    required_files = ["单据识别.json", "附件识别.json"]
    missing_files = []
    
    for file_name in required_files:
        file_path = os.path.join(input_dir, file_name)
        if not os.path.exists(file_path):
            missing_files.append(file_name)
    
    if missing_files:
        print("\n错误：在输入目录中未找到以下必需文件：")
        for file_name in missing_files:
            print(f"  - {file_name}")
        print(f"\n请将这些文件放入目录：{input_dir}")
        return
    
    print("\n✓ 发现待审核文件，开始执行审核流程...")
    
    try:
        # 初始化并运行编排器
        agent_orchestrator = Orchestrator(config_path=config_path)
        
        # 加载数据
        print("正在加载数据...")
        data_context = agent_orchestrator._load_and_prepare_data(input_dir)
        
        # 执行审核
        print("正在执行审核...")
        agent_orchestrator.run_audit(data_context)
        
        # 生成报告并打开可视化页面
        print("正在生成报告...")
        report_path = os.path.join(output_dir, "audit_report.json")
        agent_orchestrator.generate_report_and_display(report_path)
        
        print("\n✓ 审核完成！")
        
    except FileNotFoundError as e:
        print(f"\n错误：文件未找到 - {e}")
    except json.JSONDecodeError as e:
        print(f"\n错误：JSON文件格式错误 - {e}")
    except Exception as e:
        print(f"\n错误：审核过程中发生异常 - {e}")
        import traceback
        traceback.print_exc()

def create_sample_files():
    """创建示例输入文件，用于测试"""
    config_path = 'config.json'
    
    if not os.path.exists(config_path):
        print("配置文件不存在，无法创建示例文件")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    input_dir = config['INPUT_DIRECTORY']
    os.makedirs(input_dir, exist_ok=True)
    
    # 创建示例单据识别文件
    sample_details = {
        "业务招待事前审批表": {
            "申请部门": "销售部",
            "招待事由": "客户商务洽谈",
            "招待日期": "2024-01-15",
            "招待地点": "北京饭店",
            "预算金额": "2000",
            "参与人数": "4",
            "申报事由": "与重要客户进行项目合作洽谈"
        },
        "招待明细": {
            "招待日期": "2024-01-15",
            "招待地点": "北京饭店",
            "招待对象": "ABC公司张总",
            "招待内容": "商务午餐",
            "金额": "1800",
            "参与人数": "4",
            "项目名称": "客户商务合作项目"
        },
        "发票": {
            "开票日期": "2024-01-15",
            "购买方": "销售部",
            "销售方名称": "北京饭店有限公司",
            "销售方地址": "北京市东城区王府井大街1号",
            "货物或应税劳务名称": "餐饮服务",
            "价税合计": "1800"
        }
    }
    
    # 创建示例附件识别文件
    sample_attachments = {
        "附件列表": [
            "发票.pdf",
            "业务招待事前审批表.pdf",
            "招待明细.xlsx"
        ]
    }
    
    # 保存示例文件
    with open(os.path.join(input_dir, "单据识别.json"), 'w', encoding='utf-8') as f:
        json.dump(sample_details, f, ensure_ascii=False, indent=4)
    
    with open(os.path.join(input_dir, "附件识别.json"), 'w', encoding='utf-8') as f:
        json.dump(sample_attachments, f, ensure_ascii=False, indent=4)
    
    print(f"示例文件已创建在目录：{input_dir}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--create-samples":
        create_sample_files()
    else:
        main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI思维链数据存储架构重构测试脚本
验证新架构的功能和性能
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "backend" / "auditor_v2"))

def test_report_file_manager():
    """测试报告文件管理器"""
    print("🧪 测试报告文件管理器")
    print("=" * 60)
    
    try:
        from orchestrator_v2 import ReportFileManager
        
        # 创建测试文档编号
        test_doc_num = "TEST_ARCH_001"
        
        # 初始化报告文件管理器
        print(f"📝 初始化报告文件管理器: {test_doc_num}")
        manager = ReportFileManager(test_doc_num)
        
        # 测试AI思维链更新
        print("🔄 测试AI思维链更新...")
        test_thinking = """## 🔍 附件完整性检查 (阶段 1/4)

### 📋 开始分析

正在启动附件完整性检查的详细分析...

### 🔍 规则检查过程

1. **规则1：检查是否上传发票**
   - 分析数据结构中的发票信息
   - 验证发票文件的存在性和完整性
   - 结果：✅ 发票已上传且格式正确

2. **规则2：检查发票金额**
   - 提取发票中的金额信息
   - 与申请表中的金额进行对比
   - 结果：✅ 金额一致

### 📊 阶段总结

附件完整性检查已完成，所有必要文件均已上传且格式正确。"""

        manager.update_ai_thinking(
            phase_key="phase1",
            ai_thinking=test_thinking,
            phase_name="附件完整性检查",
            status="completed",
            message="阶段1完成",
            detail="检查了2条规则"
        )
        
        # 测试第二阶段
        print("🔄 测试第二阶段更新...")
        test_thinking_2 = """## 🔍 字段内容与一致性检查 (阶段 2/4)

### 📋 开始分析

正在启动字段内容与一致性检查的详细分析...

### 🔍 规则检查过程

1. **规则3：检查申请人信息一致性**
   - 对比表单和发票中的申请人信息
   - 验证身份信息的准确性
   - 结果：✅ 信息一致

2. **规则4：检查日期一致性**
   - 验证各文档中的日期信息
   - 检查时间逻辑的合理性
   - 结果：✅ 日期逻辑正确

### 📊 阶段总结

字段内容与一致性检查已完成，所有信息均保持一致。"""

        manager.update_ai_thinking(
            phase_key="phase2",
            ai_thinking=test_thinking_2,
            phase_name="字段内容与一致性检查",
            status="completed",
            message="阶段2完成",
            detail="检查了2条规则"
        )
        
        # 测试最终报告更新
        print("📊 测试最终报告更新...")
        test_summary = {
            "total_rules_checked": 4,
            "passed_count": 4,
            "failed_count": 0,
            "warning_count": 0
        }
        
        test_details = [
            {"rule_id": "规则1", "status": "PASS", "message": "发票已上传且格式正确"},
            {"rule_id": "规则2", "status": "PASS", "message": "金额一致"},
            {"rule_id": "规则3", "status": "PASS", "message": "申请人信息一致"},
            {"rule_id": "规则4", "status": "PASS", "message": "日期逻辑正确"}
        ]
        
        test_review_comments = "经审核，本次业务招待费报销符合相关规定，建议予以通过。"
        
        manager.update_final_report(test_summary, test_details, test_review_comments)
        
        # 验证报告文件内容
        print("✅ 验证报告文件内容...")
        report_data = manager.get_report_data()
        
        # 检查关键字段
        checks = [
            ("summary", "摘要信息"),
            ("details", "详细结果"),
            ("review_comments", "审批意见"),
            ("ai_thinking_chain", "AI思维链"),
            ("audit_metadata", "审核元数据")
        ]
        
        for field, desc in checks:
            if field in report_data:
                print(f"  ✅ {desc}: 存在")
                if field == "ai_thinking_chain":
                    ai_chain = report_data[field]
                    print(f"    - 组合思维链长度: {len(ai_chain.get('combined_thinking', ''))}")
                    print(f"    - 阶段历史数量: {len(ai_chain.get('phases_history', {}))}")
            else:
                print(f"  ❌ {desc}: 缺失")
        
        print(f"📁 报告文件路径: {manager.report_file_path}")
        print("✅ 报告文件管理器测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告文件管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_integration():
    """测试API集成"""
    print("\n🌐 测试API集成")
    print("=" * 60)
    
    try:
        import requests
        
        # 测试状态API
        print("📡 测试状态API...")
        response = requests.get("http://localhost:8001/api/status", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            ai_thinking = data.get("ai_thinking", "")
            print(f"✅ API响应成功")
            print(f"📊 AI思维链长度: {len(ai_thinking)}")
            
            if len(ai_thinking) > 1000:
                print("✅ AI思维链内容丰富")
            else:
                print("⚠️ AI思维链内容较少")
                
            return True
        else:
            print(f"❌ API响应失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️ API服务器未运行，跳过API测试")
        return True
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 测试性能")
    print("=" * 60)
    
    try:
        from orchestrator_v2 import ReportFileManager
        
        test_doc_num = "PERF_TEST_001"
        manager = ReportFileManager(test_doc_num)
        
        # 测试大量更新的性能
        print("🔄 测试大量AI思维链更新性能...")
        start_time = time.time()
        
        for i in range(10):
            large_thinking = f"测试思维链内容 {i+1}\n" * 100  # 创建较大的内容
            manager.update_ai_thinking(
                phase_key=f"test_phase_{i}",
                ai_thinking=large_thinking,
                phase_name=f"测试阶段{i+1}",
                status="running"
            )
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"✅ 10次更新耗时: {elapsed:.2f}秒")
        print(f"📊 平均每次更新: {elapsed/10:.3f}秒")
        
        if elapsed < 5.0:
            print("✅ 性能良好")
        else:
            print("⚠️ 性能需要优化")
            
        # 清理测试文件
        if os.path.exists(manager.report_file_path):
            os.remove(manager.report_file_path)
            print("🧹 测试文件已清理")
            
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI思维链数据存储架构重构测试")
    print("=" * 80)
    
    tests = [
        ("报告文件管理器", test_report_file_manager),
        ("API集成", test_api_integration),
        ("性能测试", test_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📊 测试结果汇总")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！新架构工作正常")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

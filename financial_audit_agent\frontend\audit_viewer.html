<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务智能审核报告</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>财务智能审核报告</h1>
        </header>

        <!-- 状态横幅已移除 -->

        <!-- 审核意见区域 -->
        <div class="review-comments-section" id="review-comments-section">
            <div class="review-comments-header">
                <h3>
                    <span class="review-icon">📋</span>
                    智能审核意见
                    <button class="toggle-btn" id="review-toggle-btn" onclick="toggleReviewComments()">
                        <span id="toggle-text">收起</span>
                        <span class="toggle-arrow" id="toggle-arrow">▲</span>
                    </button>
                </h3>
            </div>
            <div class="review-comments-content" id="review-comments-content">
                <div class="review-comments-formatted" id="review-comments-text">
                    <div class="loading">正在加载审核意见...</div>
                </div>
            </div>
        </div>

        <!-- 统计摘要 -->
        <div class="summary-section">
            <h2>审核摘要</h2>
            <div class="summary-cards-container">
                <div class="summary-card total">
                    <div class="card-icon">📊</div>
                    <div class="card-content">
                        <div class="card-number" id="total-rules">-</div>
                        <div class="card-label">总规则数</div>
                    </div>
                </div>
                <div class="summary-card passed">
                    <div class="card-icon">✅</div>
                    <div class="card-content">
                        <div class="card-number" id="passed-rules">-</div>
                        <div class="card-label">通过</div>
                    </div>
                </div>
                <div class="summary-card warning">
                    <div class="card-icon">⚠️</div>
                    <div class="card-content">
                        <div class="card-number" id="warning-rules">-</div>
                        <div class="card-label">警告</div>
                    </div>
                </div>
                <div class="summary-card failed">
                    <div class="card-icon">❌</div>
                    <div class="card-content">
                        <div class="card-number" id="failed-rules">-</div>
                        <div class="card-label">失败</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审核详情 -->
        <div class="details-section">
            <h2>审核详情</h2>
            <div class="table-container">
                <table id="audit-table">
                    <thead>
                        <tr>
                            <th>规则ID</th>
                            <th>状态</th>
                            <th>审核结果</th>
                        </tr>
                    </thead>
                    <tbody id="audit-table-body">
                        <tr>
                            <td colspan="3" class="loading">正在加载审核数据...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>



        <!-- 页脚信息 -->
        <footer class="report-footer">
            <div class="footer-content">
                <div class="system-info">
                    <span>财务智能审核系统 v2.0</span>
                    <span>|</span>
                    <span>基于AI大模型的智能审核</span>
                </div>
                <div class="generation-info">
                    <span id="report-generation-time">报告生成时间: 加载中...</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 错误提示模态框 -->
    <div id="error-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ 加载错误</h3>
                <span class="close" onclick="closeErrorModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="error-message">加载审核数据时发生错误</p>
                <div class="error-suggestions">
                    <h4>建议解决方案：</h4>
                    <ul>
                        <li>确保审核报告文件已生成</li>
                        <li>检查网络连接</li>
                        <li>刷新页面重试</li>
                        <li>确保使用HTTP服务器访问页面</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="refreshData()" class="btn-primary">重新加载</button>
                <button onclick="closeErrorModal()" class="btn-secondary">关闭</button>
            </div>
        </div>
    </div>

    <!-- 注意：此页面需要通过HTTP服务器访问，不支持直接打开HTML文件 -->

    <script src="script.js"></script>
</body>
</html>

# 新版分步式智能审核系统 v2.0

## 🚀 系统概述

新版分步式智能审核系统是对原有审核系统的重大升级，专门解决上下文长度限制问题，实现更智能、更可靠的业务招待费审核。

### 🎯 核心优势

1. **分步式处理** - 将复杂的审核任务分解为多个逻辑清晰的步骤
2. **上下文传递** - 在步骤间智能传递关键信息，保持审核连贯性
3. **模块化设计** - 高度解耦的架构，易于维护和扩展
4. **兼容性保证** - 输出格式与原系统完全兼容，前端无需修改

## 📁 系统架构

```
auditor_v2/
├── __init__.py                 # 模块初始化
├── rule_parser.py             # 规则解析器
├── data_consolidator.py       # 数据整合器  
├── prompt_manager.py          # 提示词管理器
├── orchestrator_v2.py         # 核心编排器
├── run_audit_v2.py           # 执行入口
├── demo_v2.py                # 演示脚本
└── README_V2.md              # 本文档
```

## 🔧 核心模块详解

### 1. 规则解析器 (rule_parser.py)
- **功能**: 解析 `业务招待费审核规则.txt` 文件
- **输入**: 规则文件路径
- **输出**: 按阶段分组的有序规则字典
- **特点**: 自动识别 `###` 标题，智能分组

### 2. 数据整合器 (data_consolidator.py)  
- **功能**: 整合表单和附件JSON数据
- **输入**: 表单JSON、附件JSON文件路径
- **输出**: LLM友好的精简文本格式
- **特点**: 智能清理、格式化、摘要生成

### 3. 提示词管理器 (prompt_manager.py)
- **功能**: 动态生成上下文感知的提示词
- **输入**: 阶段信息、规则、数据、历史上下文
- **输出**: 优化的LLM提示词
- **特点**: 上下文传递、格式规范、错误处理

### 4. 核心编排器 (orchestrator_v2.py)
- **功能**: 统筹整个审核流程
- **特点**: 分步执行、状态管理、错误恢复
- **输出**: 标准化审核报告

## 🚀 快速开始

### 1. 环境准备
确保已安装所有依赖：
```bash
pip install -r ../requirements.txt
```

### 2. 配置设置
在 `../config.json` 中设置您的阿里云百炼API密钥：
```json
{
  "LLM_API_KEY": "sk-your-api-key-here",
  "LLM_MODEL_NAME": "qwen-max",
  "LLM_API_MODE": "openai_compatible",
  "LLM_BASE_URL": "https://dashscope.aliyuncs.com/compatible-mode/v1",
  "LLM_TEMPERATURE": 0.7,
  "LLM_MAX_TOKENS": 2000
}
```

**注意**: 新版本支持OpenAI兼容模式，提供更好的性能和稳定性。详细配置说明请参考 `../MIGRATION_GUIDE.md`。

### 3. 运行演示
```bash
python run_audit_v2.py --help
```

### 4. 执行真实审核
```bash
python run_audit_v2.py --form ../../ZDBXD2025051300001_表单提取.json --attachment ../../ZDBXD2025051300001_附件提取.json --rules ../../业务招待费审核规则.txt
```

## 📊 工作流程

```mermaid
graph TD
    A[开始] --> B[解析规则文件]
    B --> C[整合数据]
    C --> D[第一阶段审核]
    D --> E[提取上下文]
    E --> F[第二阶段审核]
    F --> G[继续后续阶段...]
    G --> H[生成最终报告]
    H --> I[保存并展示]
```

## 🔍 分步式审核详解

### 阶段划分
根据您的规则文件，系统自动识别4个审核阶段：

1. **附件完整性检查类** (5条规则)
   - 检查发票、审批表、小票等必要附件

2. **字段内容与一致性检查类** (25条规则)  
   - 验证各字段间的一致性和逻辑关系

3. **金额与标准检查类** (7条规则)
   - 检查金额计算和消费标准

4. **八项规定类** (6条规则)
   - 验证合规性和政策要求

### 上下文传递机制
- 每个阶段的问题发现会传递给下一阶段
- 智能提取关键信息，避免信息冗余
- 保持审核的连贯性和深度

## 📈 性能优势

| 指标 | 原系统 | 新系统v2.0 | 改进 |
|------|--------|------------|------|
| 上下文长度 | 可能超限 | 分步控制 | ✅ 解决 |
| 审核深度 | 单次处理 | 递进式 | ✅ 提升 |
| 错误恢复 | 全盘失败 | 分步容错 | ✅ 增强 |
| 可维护性 | 耦合度高 | 模块化 | ✅ 优化 |

## 🛠️ 自定义和扩展

### 添加新规则
1. 在 `业务招待费审核规则.txt` 中添加规则
2. 使用 `###` 标题进行分组
3. 系统会自动识别和处理

### 修改提示词模板
编辑 `prompt_manager.py` 中的提示词模板：
```python
def create_step_prompt(self, ...):
    # 自定义您的提示词逻辑
```

### 集成新的LLM
实现 `query_semantic_rule` 方法：
```python
class YourLLMCaller:
    def query_semantic_rule(self, prompt: str) -> str:
        # 您的LLM调用逻辑
        return response
```

## 🔧 故障排除

### 常见问题

**Q: 规则解析失败？**
A: 检查规则文件编码是否为UTF-8，确保使用 `###` 标题格式

**Q: 数据整合出错？**  
A: 验证JSON文件格式，确保文件路径正确

**Q: LLM调用失败？**
A: 检查API密钥、网络连接和模型名称

**Q: 输出格式错误？**
A: 系统会自动清理LLM响应，如仍有问题请检查提示词

## 📞 技术支持

如需技术支持或有改进建议，请：
1. 查看系统日志输出
2. 运行 `run_audit_v2.py --help` 查看使用说明
3. 检查配置文件设置

## 🎉 总结

新版分步式智能审核系统v2.0通过创新的分步处理架构，彻底解决了上下文长度限制问题，同时提供了更智能、更可靠的审核体验。系统保持了与现有前端的完全兼容性，确保您可以平滑升级，立即享受新系统的强大功能。

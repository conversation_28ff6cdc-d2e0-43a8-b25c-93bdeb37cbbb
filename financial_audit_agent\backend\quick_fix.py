#!/usr/bin/env python3
"""
快速修复审核状态
"""

import json
import os
from datetime import datetime

def main():
    # 读取当前状态
    with open('audit_state.json', 'r', encoding='utf-8') as f:
        state = json.load(f)
    
    print("当前状态:")
    print(f"  阶段: {state.get('current_phase')}")
    print(f"  进度: {state.get('progress_percent')}%")
    print(f"  消息: {state.get('message')}")
    
    # 备份
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"audit_state_backup_{timestamp}.json"
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(state, f, ensure_ascii=False, indent=2)
    print(f"已备份到: {backup_file}")
    
    # 分析AI思考内容
    ai_thinking = state.get('ai_thinking', '')
    
    # 检查是否第一阶段已完成
    if '"rule_id":' in ai_thinking and '"status":' in ai_thinking:
        print("✅ 检测到第一阶段已完成（包含JSON结果）")
        
        # 如果当前在第二阶段但AI思考只有第一阶段内容，说明需要继续第二阶段
        if state.get('current_phase') == 'field-consistency':
            print("🔧 当前在第二阶段，但AI思考内容不完整")
            print("建议：让系统继续执行第二阶段")
            
            # 更新状态，让系统知道需要继续
            state['message'] = '正在执行: 字段内容与一致性检查'
            state['detail'] = '第一阶段已完成，继续执行第二阶段...'
            state['last_updated'] = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
            
            # 在AI思考内容后添加第二阶段开始标记
            if '字段内容与一致性检查' not in ai_thinking:
                state['ai_thinking'] = ai_thinking + '\n\n## 🔍 字段内容与一致性检查 (阶段 2/4)\n\n正在开始第二阶段的审核...'
            
            # 保存修复后的状态
            with open('audit_state.json', 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            
            print("✅ 状态已修复，系统应该会继续执行第二阶段")
        else:
            print("状态看起来正常，无需修复")
    else:
        print("❌ 第一阶段似乎还未完成")

if __name__ == "__main__":
    main()

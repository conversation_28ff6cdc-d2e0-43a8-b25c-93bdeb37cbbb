#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API版本检查脚本
检查当前环境中可用的API库和版本信息
"""

import sys
import json

def check_openai_library():
    """检查OpenAI库"""
    try:
        import openai
        version = getattr(openai, '__version__', 'unknown')
        print(f"✅ OpenAI库: v{version}")
        return True
    except ImportError:
        print("❌ OpenAI库: 未安装")
        return False

def check_dashscope_library():
    """检查DashScope库"""
    try:
        import dashscope
        version = getattr(dashscope, '__version__', 'unknown')
        print(f"✅ DashScope库: v{version}")
        return True
    except ImportError:
        print("❌ DashScope库: 未安装")
        return False

def check_config_file():
    """检查配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("📋 配置文件检查:")
        
        # 检查API密钥
        api_key = config.get('LLM_API_KEY', '')
        if not api_key or api_key == "在此处填入你的API密钥":
            print("   ❌ API密钥: 未设置")
        else:
            masked_key = api_key[:8] + "..." + api_key[-4:] if len(api_key) > 12 else "***"
            print(f"   ✅ API密钥: {masked_key}")
        
        # 检查模型名称
        model = config.get('LLM_MODEL_NAME', 'qwen-max')
        print(f"   📝 模型名称: {model}")
        
        # 检查API模式
        api_mode = config.get('LLM_API_MODE', '未设置')
        print(f"   🔧 API模式: {api_mode}")
        
        # 检查Base URL
        base_url = config.get('LLM_BASE_URL', '未设置')
        print(f"   🌐 Base URL: {base_url}")
        
        return True
        
    except FileNotFoundError:
        print("❌ 配置文件: config.json 不存在")
        return False
    except json.JSONDecodeError:
        print("❌ 配置文件: JSON格式错误")
        return False

def get_recommendations():
    """获取推荐配置"""
    openai_available = check_openai_library()
    dashscope_available = check_dashscope_library()
    
    print("\n💡 推荐配置:")
    
    if openai_available:
        print("   🎯 推荐使用OpenAI兼容模式")
        print("   📝 配置示例:")
        print('   {')
        print('     "LLM_API_MODE": "openai_compatible",')
        print('     "LLM_BASE_URL": "https://dashscope.aliyuncs.com/compatible-mode/v1"')
        print('   }')
    elif dashscope_available:
        print("   📝 使用传统DashScope模式")
        print("   💡 建议安装OpenAI库以获得更好的性能:")
        print("   pip install openai>=1.0.0")
    else:
        print("   ❌ 需要安装至少一个API库:")
        print("   pip install openai>=1.0.0  # 推荐")
        print("   pip install dashscope==1.22.1  # 或者")

def main():
    """主函数"""
    print("🔍 API版本检查工具")
    print("=" * 40)
    
    print("\n📚 依赖库检查:")
    openai_ok = check_openai_library()
    dashscope_ok = check_dashscope_library()
    
    print("\n⚙️ 配置文件检查:")
    config_ok = check_config_file()
    
    print("\n📊 系统状态:")
    if openai_ok or dashscope_ok:
        print("   ✅ API库: 可用")
    else:
        print("   ❌ API库: 不可用")
    
    if config_ok:
        print("   ✅ 配置文件: 可用")
    else:
        print("   ❌ 配置文件: 不可用")
    
    # 获取推荐
    get_recommendations()
    
    print("\n🔗 相关链接:")
    print("   📖 迁移指南: MIGRATION_GUIDE.md")
    print("   🧪 连接测试: python test_llm_connection.py")
    print("   📋 配置示例: config_example.json")

if __name__ == "__main__":
    main()

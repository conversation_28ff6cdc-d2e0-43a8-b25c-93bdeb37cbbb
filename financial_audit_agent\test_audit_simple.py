#!/usr/bin/env python3
"""
简化的审核测试脚本 - 绕过start.py的复杂性
"""

import subprocess
import sys
import os
from pathlib import Path

def run_audit_directly():
    """直接运行审核引擎，显示完整输出"""
    print("🚀 直接运行审核引擎测试")
    print("=" * 60)
    
    # 检查目录
    v2_dir = Path("backend/auditor_v2")
    if not v2_dir.exists():
        print(f"❌ 目录不存在: {v2_dir}")
        return False
    
    # 切换目录
    original_cwd = os.getcwd()
    try:
        os.chdir(v2_dir)
        print(f"📁 切换到目录: {os.getcwd()}")
        
        # 构建命令
        cmd = [
            sys.executable, "run_audit_v2.py",
            "--doc-num", "ZDBXD2025042900003",
            "--rules", "../../业务招待费审核规则_V2.txt",
            "--config", "../config.json", 
            "--output", "../../audit_reports/audit_report_ZDBXD2025042900003.json",
            "--no-browser"
        ]
        
        print(f"🔧 执行命令: {' '.join(cmd)}")
        
        # 设置环境
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 执行命令，实时显示输出
        print("\n📝 开始执行，实时输出:")
        print("-" * 60)
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            env=env,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时读取输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.rstrip())
        
        # 获取返回码
        return_code = process.poll()
        print("-" * 60)
        print(f"📊 执行完成，返回码: {return_code}")
        
        return return_code == 0
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def check_environment():
    """检查环境配置"""
    print("\n🔍 环境检查")
    print("=" * 60)
    
    # 检查关键文件
    files_to_check = [
        "backend/auditor_v2/run_audit_v2.py",
        "backend/auditor_v2/orchestrator_v2.py",
        "backend/llm_caller.py",
        "backend/config.json",
        "业务招待费审核规则_V2.txt"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("🧪 简化审核测试工具")
    print("🎯 目标: 绕过start.py，直接测试审核引擎")
    print("=" * 80)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请确保所有必要文件存在")
        return
    
    # 运行测试
    success = run_audit_directly()
    
    # 总结
    print("\n" + "=" * 80)
    if success:
        print("🎉 测试成功！审核引擎工作正常")
        print("💡 问题可能出在start.py的进程管理上")
    else:
        print("❌ 测试失败！审核引擎本身存在问题")
        print("💡 需要检查LLM配置、网络连接或代码逻辑")

if __name__ == "__main__":
    main()

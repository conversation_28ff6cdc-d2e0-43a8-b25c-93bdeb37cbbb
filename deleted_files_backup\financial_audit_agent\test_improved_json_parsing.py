#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的JSON解析逻辑
"""

import os
import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "backend" / "auditor_v2"))

def test_improved_json_parsing():
    """测试改进后的JSON解析逻辑"""
    print("🧪 测试改进后的JSON解析逻辑")
    print("=" * 80)
    
    # 测试用例：模拟各种可能的LLM响应格式
    test_cases = [
        # 案例1：标准格式
        {
            "name": "标准格式",
            "response": """**第一部分：详细思考过程**
思考过程...

**第二部分：审核结果**
[
  {
    "rule_id": "规则1：检查是否上传发票。",
    "status": "通过",
    "reason": "发票已上传"
  }
]"""
        },
        
        # 案例2：没有标记的纯JSON
        {
            "name": "纯JSON格式",
            "response": """[
  {
    "rule_id": "规则1：检查是否上传发票。",
    "status": "通过",
    "reason": "发票已上传"
  },
  {
    "rule_id": "规则2：检查是否上传审批表。",
    "status": "通过",
    "reason": "审批表已上传"
  }
]"""
        },
        
        # 案例3：包含其他文本的混合格式
        {
            "name": "混合格式",
            "response": """这是一些前置文本。

根据分析，我得出以下结论：

[
  {
    "rule_id": "规则1：检查是否上传发票。",
    "status": "通过",
    "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'发票', 满足要求."
  }
]

这是一些后续文本。"""
        },
        
        # 案例4：包含多个JSON数组的复杂格式
        {
            "name": "多JSON数组格式",
            "response": """分析过程中，我发现了以下数据：

临时数据：[来源: 附件概览 -> 附件类型]

最终结果：
[
  {
    "rule_id": "规则1：检查是否上传发票。",
    "status": "通过",
    "reason": "发票已上传"
  }
]

总结完成。"""
        },
        
        # 案例5：空响应
        {
            "name": "空响应",
            "response": ""
        },
        
        # 案例6：只有文本没有JSON
        {
            "name": "无JSON格式",
            "response": "这是一个没有JSON的响应，只包含普通文本。"
        },
        
        # 案例7：格式错误的JSON
        {
            "name": "错误JSON格式",
            "response": """**第二部分：审核结果**
[
  {
    "rule_id": "规则1：检查是否上传发票。",
    "status": "通过",
    "reason": "发票已上传"
  },
  {
    "rule_id": "规则2：检查是否上传审批表。",
    "status": "通过",
    "reason": "审批表已上传"
  }
  // 缺少结束括号"""
        }
    ]
    
    try:
        from orchestrator_v2 import OrchestratorV2
        
        # 创建测试实例
        orchestrator = OrchestratorV2(None, None, "TEST_IMPROVED_001")
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试案例{i}: {test_case['name']}")
            print("-" * 40)
            
            response = test_case['response']
            print(f"响应长度: {len(response)} 字符")
            
            # 测试改进的提取方法
            try:
                extracted = orchestrator._extract_json_from_response(response)
                
                if extracted:
                    print(f"✅ JSON提取成功，长度: {len(extracted)}")
                    print(f"提取内容: {extracted[:100]}{'...' if len(extracted) > 100 else ''}")
                    
                    # 尝试解析JSON
                    try:
                        parsed = json.loads(extracted)
                        print(f"✅ JSON解析成功，类型: {type(parsed)}")
                        if isinstance(parsed, list):
                            print(f"数组长度: {len(parsed)}")
                        results.append((test_case['name'], True, len(parsed) if isinstance(parsed, list) else 1))
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        results.append((test_case['name'], False, 0))
                else:
                    print("❌ 未提取到JSON内容")
                    results.append((test_case['name'], False, 0))
                    
            except Exception as e:
                print(f"❌ 提取过程出错: {e}")
                results.append((test_case['name'], False, 0))
        
        # 输出测试结果汇总
        print(f"\n📊 测试结果汇总")
        print("=" * 80)
        
        success_count = 0
        for name, success, count in results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{name}: {status}, 项目数: {count}")
            if success:
                success_count += 1
        
        print(f"\n总计: {success_count}/{len(results)} 个测试案例成功")
        
        if success_count >= len(results) - 2:  # 允许空响应和无JSON格式失败
            print("🎉 改进的JSON解析逻辑工作良好！")
            return True
        else:
            print("⚠️ 改进的JSON解析逻辑仍需进一步优化")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 改进后的JSON解析逻辑测试")
    print("=" * 100)
    
    # 切换到项目目录
    os.chdir(project_root)
    
    # 执行测试
    success = test_improved_json_parsing()
    
    if success:
        print("\n🎯 下一步行动:")
        print("1. 运行实际审核测试改进效果")
        print("2. 验证details数组是否能正确填充")
        print("3. 确认优化2（审核结果实时写入）是否生效")
        return 0
    else:
        print("\n🔧 需要进一步改进:")
        print("1. 分析失败的测试案例")
        print("2. 优化JSON提取算法")
        print("3. 增加更多的错误处理")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

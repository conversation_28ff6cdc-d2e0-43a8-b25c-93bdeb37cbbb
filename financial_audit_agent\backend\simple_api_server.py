#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的API服务器
专门为前后端数据同步提供API接口
"""

import json
import os
import time
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading


class SimpleAPIHandler(BaseHTTPRequestHandler):
    """简化的API处理器"""
    
    def __init__(self, *args, **kwargs):
        self.backend_dir = Path(__file__).parent
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            
            # 设置CORS头
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            # 路由处理
            if path == '/api/status' or path == '/api/state':
                self.handle_status()
            elif path == '/api/health':
                self.handle_health()
            else:
                self.handle_not_found()
                
        except Exception as e:
            self.handle_error(str(e))
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def handle_status(self):
        """处理状态查询"""
        try:
            # 读取状态文件
            state_file = self.backend_dir / "audit_state.json"
            
            if state_file.exists():
                with open(state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                # 确保数据格式正确
                if not isinstance(state_data, dict):
                    state_data = self.get_default_state()
                
                # 添加时间戳
                state_data['server_time'] = time.strftime("%Y-%m-%dT%H:%M:%SZ")
                
            else:
                state_data = self.get_default_state()
            
            # 返回JSON数据
            response_data = json.dumps(state_data, ensure_ascii=False, indent=2)
            self.wfile.write(response_data.encode('utf-8'))
            
        except Exception as e:
            self.handle_error(f"状态查询失败: {e}")
    
    def handle_health(self):
        """处理健康检查"""
        health_data = {
            "status": "healthy",
            "service": "AI财务审核系统API",
            "version": "2.0",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
        }
        
        response_data = json.dumps(health_data, ensure_ascii=False, indent=2)
        self.wfile.write(response_data.encode('utf-8'))
    
    def handle_not_found(self):
        """处理404错误"""
        self.send_response(404)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_data = {
            "error": "Not Found",
            "message": "请求的API端点不存在",
            "path": self.path
        }
        
        response_data = json.dumps(error_data, ensure_ascii=False, indent=2)
        self.wfile.write(response_data.encode('utf-8'))
    
    def handle_error(self, error_message):
        """处理错误"""
        self.send_response(500)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_data = {
            "error": "Internal Server Error",
            "message": error_message,
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
        }
        
        response_data = json.dumps(error_data, ensure_ascii=False, indent=2)
        self.wfile.write(response_data.encode('utf-8'))
    
    def get_default_state(self):
        """获取默认状态"""
        return {
            "audit_id": None,
            "audit_status": "ready",
            "current_phase": "ready",
            "progress_percent": 0,
            "start_time": None,
            "completion_time": None,
            "summary": {
                "total_rules": 0,
                "completed_rules": 0,
                "passed_rules": 0,
                "failed_rules": 0,
                "warning_rules": 0
            },
            "ai_thinking": "系统就绪，等待开始审核",
            "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "message": "系统就绪，等待开始审核",
            "detail": "请启动审核流程"
        }
    
    def log_message(self, format_str, *args):
        """静默日志"""
        pass


class SimpleAPIServer:
    """简化的API服务器"""
    
    def __init__(self, port=8001):
        self.port = port
        self.server = None
        self.server_thread = None
    
    def start(self):
        """启动服务器"""
        try:
            self.server = HTTPServer(('localhost', self.port), SimpleAPIHandler)
            self.server.allow_reuse_address = True
            
            print(f"🚀 简化API服务器启动在端口 {self.port}")
            
            # 在后台线程中运行服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ API服务器启动失败: {e}")
            return False
    
    def stop(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("🛑 API服务器已停止")


def main():
    """主函数 - 独立运行API服务器"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简化的API服务器')
    parser.add_argument('--port', type=int, default=8001, help='服务器端口')
    
    args = parser.parse_args()
    
    server = SimpleAPIServer(args.port)
    
    if server.start():
        print(f"✅ API服务器运行在: http://localhost:{args.port}")
        print("📋 可用端点:")
        print(f"   - GET http://localhost:{args.port}/api/status")
        print(f"   - GET http://localhost:{args.port}/api/health")
        print("\n[运行] 按 Ctrl+C 停止服务器")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[停止] 正在停止服务器...")
            server.stop()
            print("[完成] 服务器已停止")
    else:
        print("❌ 服务器启动失败")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())

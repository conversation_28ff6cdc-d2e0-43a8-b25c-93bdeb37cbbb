#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断前后端连接问题的测试脚本
"""

import json
import time
import requests
import threading
import subprocess
import sys
from pathlib import Path


def test_file_access():
    """测试文件访问"""
    print("🔍 测试文件访问...")
    
    project_root = Path('.')
    
    # 检查状态文件
    status_file = project_root / 'backend' / 'audit_status.json'
    print(f"状态文件: {status_file.absolute()}")
    print(f"存在: {status_file.exists()}")
    
    if status_file.exists():
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("✅ 状态文件读取成功")
            print(f"当前步骤: {data.get('current_step')}")
            print(f"状态: {data.get('status')}")
        except Exception as e:
            print(f"❌ 状态文件读取失败: {e}")
    
    # 检查报告文件
    report_file = project_root / 'audit_reports' / 'audit_report_ZDBXD2025051300001.json'
    print(f"\n报告文件: {report_file.absolute()}")
    print(f"存在: {report_file.exists()}")
    
    if report_file.exists():
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("✅ 报告文件读取成功")
            print(f"总规则数: {data.get('summary', {}).get('total_rules_checked', 'N/A')}")
        except Exception as e:
            print(f"❌ 报告文件读取失败: {e}")


def test_api_server():
    """测试API服务器"""
    print("\n🔍 测试API服务器...")
    
    # 尝试连接常见端口
    ports = [8001, 8002, 8003, 8004]
    
    for port in ports:
        try:
            url = f"http://localhost:{port}/api/status"
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"✅ 端口 {port} 可访问")
                data = response.json()
                print(f"   状态: {data.get('status')}")
                print(f"   消息: {data.get('message')}")
                return port
            else:
                print(f"❌ 端口 {port} 返回状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ 端口 {port} 连接失败: {e}")
    
    print("⚠️ 未找到可用的API服务器")
    return None


def test_web_server():
    """测试Web服务器"""
    print("\n🔍 测试Web服务器...")
    
    ports = [8002, 8003, 8004, 8005]
    
    for port in ports:
        try:
            url = f"http://localhost:{port}/frontend/ai_console.html"
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"✅ Web服务器端口 {port} 可访问")
                return port
            else:
                print(f"❌ Web服务器端口 {port} 返回状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Web服务器端口 {port} 连接失败: {e}")
    
    print("⚠️ 未找到可用的Web服务器")
    return None


def start_backend_test():
    """启动后端测试"""
    print("\n🚀 启动后端服务测试...")
    
    try:
        # 启动后端服务
        process = subprocess.Popen([
            sys.executable, "start_backend_v2.py",
            "--doc-num", "ZDBXD2025051300001",
            "--no-browser"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("⏳ 等待服务启动...")
        time.sleep(8)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 后端服务进程正在运行")
            
            # 测试API连接
            api_port = test_api_server()
            web_port = test_web_server()
            
            if api_port:
                print(f"✅ API服务器运行在端口 {api_port}")
            if web_port:
                print(f"✅ Web服务器运行在端口 {web_port}")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("🛑 后端服务已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("🛑 强制停止后端服务")
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 后端服务启动失败")
            print(f"返回码: {process.returncode}")
            if stdout:
                print(f"输出: {stdout}")
            if stderr:
                print(f"错误: {stderr}")
    
    except Exception as e:
        print(f"❌ 启动测试异常: {e}")


def main():
    """主函数"""
    print("🔧 前后端连接问题诊断工具")
    print("=" * 50)
    
    # 1. 测试文件访问
    test_file_access()
    
    # 2. 测试现有服务器
    api_port = test_api_server()
    web_port = test_web_server()
    
    # 3. 如果没有运行的服务器，启动测试
    if not api_port and not web_port:
        start_backend_test()
    
    print("\n📊 诊断完成")
    print("💡 如果发现问题，请检查:")
    print("   1. 虚拟环境是否激活")
    print("   2. 依赖包是否安装完整")
    print("   3. 端口是否被占用")
    print("   4. 防火墙设置")


if __name__ == "__main__":
    main()

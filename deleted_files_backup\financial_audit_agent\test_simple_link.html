<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单链接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: inline-block;
            margin: 10px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 简单链接测试</h1>
        
        <div class="info">
            <strong>当前页面URL:</strong><br>
            <span id="current-url"></span>
        </div>
        
        <div class="info">
            <strong>检测到的单据编号:</strong><br>
            <span id="document-number"></span>
        </div>
        
        <h2>测试链接</h2>
        
        <a href="http://localhost:8001/frontend/audit_viewer.html" class="test-button" target="_blank">
            基本审核页面 (无参数)
        </a>
        
        <a href="http://localhost:8001/frontend/audit_viewer.html?doc=ZDBXD2025042900003" class="test-button" target="_blank">
            带单据编号的审核页面
        </a>
        
        <a href="#" id="dynamic-link" class="test-button" target="_blank">
            动态生成的链接
        </a>
        
        <h2>模拟进度页面行为</h2>
        <div id="result-link-container">
            <a href="#" id="report-link" target="_blank" class="test-button">点击查看详细审核报告</a>
        </div>
        
        <div class="info">
            <strong>生成的链接:</strong><br>
            <span id="generated-link">未生成</span>
        </div>
    </div>

    <script>
        // 显示当前URL
        document.getElementById('current-url').textContent = window.location.href;
        
        // 获取单据编号
        const urlParams = new URLSearchParams(window.location.search);
        const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
        document.getElementById('document-number').textContent = documentNumber || '无';
        
        // 设置动态链接
        const dynamicLink = document.getElementById('dynamic-link');
        if (documentNumber) {
            dynamicLink.href = `http://localhost:8001/frontend/audit_viewer.html?doc=${documentNumber}`;
            dynamicLink.textContent = `动态链接 (${documentNumber})`;
        } else {
            dynamicLink.href = 'http://localhost:8001/frontend/audit_viewer.html';
            dynamicLink.textContent = '动态链接 (无参数)';
        }
        
        // 复制进度页面的setupReportLink逻辑
        function setupReportLink() {
            console.log('🔧 Setting up report link...');
            
            const reportLink = document.getElementById('report-link');
            if (!reportLink) {
                console.error('❌ Report link element not found!');
                return;
            }
            
            // 获取单据编号参数
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
            console.log('📋 Document number from URL:', documentNumber);
            
            // 检测当前页面的访问方式
            const currentUrl = window.location.href;
            console.log('🌐 Current URL:', currentUrl);
            let reportUrl;
            
            if (currentUrl.includes('localhost:8001')) {
                reportUrl = 'http://localhost:8001/frontend/audit_viewer.html';
                console.log('🔧 Using port 8001');
            } else if (currentUrl.startsWith('file://')) {
                reportUrl = 'http://localhost:8001/frontend/audit_viewer.html';
                console.log('🔧 File protocol, redirecting to port 8001');
            } else {
                reportUrl = 'http://localhost:8001/frontend/audit_viewer.html';
                console.log('🔧 Default to port 8001');
            }
            
            // 添加单据编号参数
            if (documentNumber) {
                const separator = reportUrl.includes('?') ? '&' : '?';
                reportUrl += `${separator}doc=${documentNumber}`;
                console.log('✅ Added document number:', documentNumber);
            }
            
            reportLink.href = reportUrl;
            document.getElementById('generated-link').textContent = reportUrl;
            console.log('✅ Final report URL:', reportUrl);
        }
        
        // 页面加载时设置链接
        window.onload = function() {
            setupReportLink();
        };
    </script>
</body>
</html>

from data_models import DataContext, AuditResult
from datetime import datetime
from typing import Any, Dict

class DeterministicRules:
    def __init__(self, context: DataContext):
        self.context = context.all_extracted_data

    def check_rule_1_has_invoice(self) -> AuditResult:
        """规则1：检查业务招待审批流程中，是否上传了发票？"""
        # 此处假设附件列表在上下文中可用。编排器将提供真实数据。
        attachments = self.context.get('附件列表', [])
        if any("发票" in str(attachment) for attachment in attachments):
            return AuditResult(rule_id="规则1", status="PASS", message="发票已上传。")
        else:
            return AuditResult(rule_id="规则1", status="FAIL", message="附件中缺少发票。")

    def check_rule_2_has_approval_form(self) -> AuditResult:
        """规则2：检查业务招待审批流程中，是否上传了《业务招待事前审批表》？"""
        attachments = self.context.get('附件列表', [])
        if any("业务招待事前审批表" in str(attachment) or "审批表" in str(attachment) for attachment in attachments):
            return AuditResult(rule_id="规则2", status="PASS", message="业务招待事前审批表已上传。")
        else:
            return AuditResult(rule_id="规则2", status="FAIL", message="附件中缺少业务招待事前审批表。")

    def check_rule_3_has_entertainment_details(self) -> AuditResult:
        """规则3：检查业务招待审批流程中，是否上传了《招待明细》？"""
        attachments = self.context.get('附件列表', [])
        if any("招待明细" in str(attachment) or "明细" in str(attachment) for attachment in attachments):
            return AuditResult(rule_id="规则3", status="PASS", message="招待明细已上传。")
        else:
            return AuditResult(rule_id="规则3", status="FAIL", message="附件中缺少招待明细。")

    def check_rule_4_amount_consistency(self) -> AuditResult:
        """规则4：确认"招待明细"中的"金额"与发票中的"价税合计"是否一致。"""
        detail_amount = self.context.get("招待明细", {}).get("金额")
        invoice_amount = self.context.get("发票", {}).get("价税合计")
        
        if not detail_amount or not invoice_amount:
            return AuditResult(rule_id="规则4", status="FAIL", message="缺少金额信息，无法比对。")
        
        try:
            detail_amount = float(str(detail_amount).replace(',', '').replace('￥', '').replace('元', ''))
            invoice_amount = float(str(invoice_amount).replace(',', '').replace('￥', '').replace('元', ''))
            
            if abs(detail_amount - invoice_amount) < 0.01:  # 允许小数点误差
                return AuditResult(rule_id="规则4", status="PASS", message="招待明细金额与发票价税合计一致。")
            else:
                return AuditResult(rule_id="规则4", status="FAIL", 
                                 message=f"金额不一致：招待明细({detail_amount}) vs 发票价税合计({invoice_amount})。")
        except (ValueError, TypeError):
            return AuditResult(rule_id="规则4", status="FAIL", message="金额格式错误，无法比对。")

    def check_rule_5_date_within_range(self) -> AuditResult:
        """规则5：确认发票的"开票日期"是否在"招待日期"的前后3天内。"""
        invoice_date = self.context.get("发票", {}).get("开票日期")
        entertainment_date = self.context.get("招待明细", {}).get("招待日期")
        
        if not invoice_date or not entertainment_date:
            return AuditResult(rule_id="规则5", status="FAIL", message="缺少日期信息，无法比对。")
        
        try:
            # 尝试解析日期格式
            invoice_dt = self._parse_date(invoice_date)
            entertainment_dt = self._parse_date(entertainment_date)
            
            if not invoice_dt or not entertainment_dt:
                return AuditResult(rule_id="规则5", status="FAIL", message="日期格式错误，无法解析。")
            
            diff_days = abs((invoice_dt - entertainment_dt).days)
            if diff_days <= 3:
                return AuditResult(rule_id="规则5", status="PASS", 
                                 message=f"开票日期在招待日期前后3天内（相差{diff_days}天）。")
            else:
                return AuditResult(rule_id="规则5", status="FAIL", 
                                 message=f"开票日期超出招待日期前后3天范围（相差{diff_days}天）。")
        except Exception as e:
            return AuditResult(rule_id="规则5", status="FAIL", message=f"日期比对出错：{str(e)}")

    def check_rule_6_invoice_buyer_consistency(self) -> AuditResult:
        """规则6：确认发票的"购买方"与《业务招待事前审批表》中的"申请部门"是否一致。"""
        invoice_buyer = self.context.get("发票", {}).get("购买方", "")
        approval_dept = self.context.get("业务招待事前审批表", {}).get("申请部门", "")
        
        if not invoice_buyer or not approval_dept:
            return AuditResult(rule_id="规则6", status="FAIL", message="缺少购买方或申请部门信息。")
        
        if invoice_buyer.strip() == approval_dept.strip():
            return AuditResult(rule_id="规则6", status="PASS", message="发票购买方与申请部门一致。")
        else:
            return AuditResult(rule_id="规则6", status="FAIL", 
                             message=f"购买方不一致：发票购买方({invoice_buyer}) vs 申请部门({approval_dept})。")

    def check_rule_11_date_consistency(self) -> AuditResult:
        """规则11：确认"招待明细"中的"招待日期"与《业务招待事前审批表》中的"招待日期"是否为同一天。"""
        detail_date = self.context.get("招待明细", {}).get("招待日期")
        form_date = self.context.get("业务招待事前审批表", {}).get("招待日期")
        
        if not detail_date or not form_date:
            return AuditResult(rule_id="规则11", status="FAIL", message="缺少招待日期信息，无法比对。")
        
        if str(detail_date).strip() == str(form_date).strip():
            return AuditResult(rule_id="规则11", status="PASS", message="招待日期与审批表日期一致。")
        else:
            return AuditResult(rule_id="规则11", status="FAIL", 
                             message=f"日期不一致：招待明细日期({detail_date}) vs 审批表日期({form_date})。")

    def check_rule_9_amount_within_budget(self) -> AuditResult:
        """规则9：确认"招待明细"中的"金额"是否在《业务招待事前审批表》中的"预算金额"范围内。"""
        detail_amount = self.context.get("招待明细", {}).get("金额")
        budget_amount = self.context.get("业务招待事前审批表", {}).get("预算金额")

        if not detail_amount or not budget_amount:
            return AuditResult(rule_id="规则9", status="FAIL", message="缺少金额或预算信息。")

        try:
            detail_amount = float(str(detail_amount).replace(',', '').replace('￥', '').replace('元', ''))
            budget_amount = float(str(budget_amount).replace(',', '').replace('￥', '').replace('元', ''))

            if detail_amount <= budget_amount:
                return AuditResult(rule_id="规则9", status="PASS",
                                 message=f"实际金额({detail_amount})在预算范围内({budget_amount})。")
            else:
                return AuditResult(rule_id="规则9", status="FAIL",
                                 message=f"实际金额({detail_amount})超出预算({budget_amount})。")
        except (ValueError, TypeError):
            return AuditResult(rule_id="规则9", status="FAIL", message="金额格式错误，无法比对。")

    def check_rule_10_participant_count_consistency(self) -> AuditResult:
        """规则10：确认"招待明细"中的"参与人数"与《业务招待事前审批表》中的"参与人数"是否一致。"""
        detail_count = self.context.get("招待明细", {}).get("参与人数")
        form_count = self.context.get("业务招待事前审批表", {}).get("参与人数")

        if not detail_count or not form_count:
            return AuditResult(rule_id="规则10", status="FAIL", message="缺少参与人数信息。")

        try:
            detail_count = int(str(detail_count).replace('人', '').strip())
            form_count = int(str(form_count).replace('人', '').strip())

            if detail_count == form_count:
                return AuditResult(rule_id="规则10", status="PASS", message="参与人数一致。")
            else:
                return AuditResult(rule_id="规则10", status="FAIL",
                                 message=f"参与人数不一致：招待明细({detail_count}) vs 审批表({form_count})。")
        except (ValueError, TypeError):
            return AuditResult(rule_id="规则10", status="FAIL", message="参与人数格式错误。")

    def check_rule_12_location_consistency(self) -> AuditResult:
        """规则12：确认"招待明细"中的"招待地点"与《业务招待事前审批表》中的"招待地点"是否一致。"""
        detail_location = self.context.get("招待明细", {}).get("招待地点", "")
        form_location = self.context.get("业务招待事前审批表", {}).get("招待地点", "")

        if not detail_location or not form_location:
            return AuditResult(rule_id="规则12", status="FAIL", message="缺少招待地点信息。")

        if detail_location.strip() == form_location.strip():
            return AuditResult(rule_id="规则12", status="PASS", message="招待地点一致。")
        else:
            return AuditResult(rule_id="规则12", status="FAIL",
                             message=f"招待地点不一致：招待明细({detail_location}) vs 审批表({form_location})。")

    def _parse_date(self, date_str: str) -> datetime:
        """辅助方法：解析各种日期格式"""
        if not date_str:
            return None

        date_formats = [
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%Y年%m月%d日",
            "%m/%d/%Y",
            "%d/%m/%Y"
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(str(date_str).strip(), fmt)
            except ValueError:
                continue
        return None

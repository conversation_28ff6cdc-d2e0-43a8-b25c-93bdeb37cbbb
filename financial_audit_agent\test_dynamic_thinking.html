<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI思维链动态更新测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .test-link {
            display: block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }
        
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .test-link.static {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        
        .test-link.dynamic {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .api-test {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .api-link {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #333;
        }
        
        .instructions {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            margin-top: 30px;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #333;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
            animation: pulse-green 2s infinite;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        @keyframes pulse-green {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-top: 3px solid #007bff;
        }
        
        .feature-card h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-card p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 AI思维链动态更新测试</h1>
            <p>测试AI审核结果页面的实时动态更新功能</p>
        </div>
        
        <!-- 页面访问测试 -->
        <div class="test-section">
            <div class="test-title">📄 页面访问测试</div>
            <div class="test-links">
                <a href="frontend/ai_results.html?doc=ZDBXD2025042900003" class="test-link static">
                    📊 静态模式（传统）
                </a>
                <a href="frontend/ai_results.html?doc=ZDBXD2025042900003&dynamic=true" class="test-link dynamic">
                    🔄 动态模式（实时更新）
                </a>
            </div>
        </div>
        
        <!-- API测试 -->
        <div class="test-section api-test">
            <div class="test-title">🔌 API端点测试</div>
            <div class="test-links">
                <a href="/api/thinking-status?doc_num=ZDBXD2025042900003" class="test-link api-link" target="_blank">
                    📡 思维链状态API
                </a>
                <a href="/api/status" class="test-link api-link" target="_blank">
                    📊 系统状态API
                </a>
                <a href="/api/report?doc_num=ZDBXD2025042900003" class="test-link api-link" target="_blank">
                    📋 审核报告API
                </a>
            </div>
        </div>
        
        <!-- 功能特性 -->
        <div class="test-section">
            <div class="test-title">✨ 功能特性</div>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔄 实时更新</h4>
                    <p>每2秒自动检查思维链内容变化，无需手动刷新页面</p>
                </div>
                <div class="feature-card">
                    <h4>🎛️ 用户控制</h4>
                    <p>可随时暂停/恢复更新，完全由用户控制</p>
                </div>
                <div class="feature-card">
                    <h4>🎨 视觉反馈</h4>
                    <p>更新时显示动画效果和"已更新"标记</p>
                </div>
                <div class="feature-card">
                    <h4>📱 响应式设计</h4>
                    <p>支持桌面和移动设备，自适应不同屏幕尺寸</p>
                </div>
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="test-section instructions">
            <div class="test-title">📋 使用说明</div>
            <ul>
                <li><strong>启用动态模式</strong>：在URL中添加 <code>dynamic=true</code> 参数</li>
                <li><strong>查看指示器</strong>：页面顶部会显示蓝色的实时模式指示器</li>
                <li><strong>控制更新</strong>：点击指示器中的"暂停更新"按钮可以控制更新</li>
                <li><strong>观察变化</strong>：当AI思维链内容更新时，会显示绿色"已更新"标记</li>
                <li><strong>检查API</strong>：点击上方API链接测试后端服务是否正常</li>
            </ul>
        </div>
        
        <!-- 系统状态 -->
        <div class="test-section">
            <div class="test-title">🔍 系统状态检查</div>
            <p id="system-status">
                <span class="status-indicator status-offline"></span>
                正在检查系统状态...
            </p>
        </div>
    </div>
    
    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            const statusElement = document.getElementById('system-status');
            
            try {
                const response = await fetch('/api/status');
                if (response.ok) {
                    const data = await response.json();
                    statusElement.innerHTML = `
                        <span class="status-indicator status-online"></span>
                        系统在线 - API服务器正常运行 (${data.api_timestamp || '未知时间'})
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusElement.innerHTML = `
                    <span class="status-indicator status-offline"></span>
                    系统离线 - 请检查API服务器状态 (错误: ${error.message})
                `;
            }
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', () => {
            checkSystemStatus();
            
            // 每30秒检查一次状态
            setInterval(checkSystemStatus, 30000);
        });
    </script>
</body>
</html>

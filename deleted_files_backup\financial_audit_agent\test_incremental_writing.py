#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量写入机制测试脚本
测试AI思维链和审核结果的实时写入功能
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "backend" / "auditor_v2"))

def test_ai_thinking_writing():
    """测试AI思维链写入机制（优化1）"""
    print("🧠 测试AI思维链写入机制")
    print("=" * 60)
    
    try:
        from orchestrator_v2 import ReportFileManager
        
        test_doc_num = "TEST_THINKING_001"
        manager = ReportFileManager(test_doc_num)
        
        # 测试思维链写入（包含markdown格式和特殊字符）
        test_thinking = """## 🔍 附件完整性检查 (阶段详细分析)

首先，我需要理解我的角色：我是一名财务审核助手，专注于附件完整性检查阶段。

**审核规则回顾：**
- **规则1：检查是否上传发票。** 来源：附件概览 -> 附件类型
- **规则2：检查是否上传事前审批表。** 来源：附件概览 -> 附件类型

### 规则1：检查是否上传发票。
- **来源：** 附件概览 -> 附件类型
- **数据：** 附件类型列表是 "业务招待事前审批表, 发票, 餐饮小票, 支付记录"
- **分析：** 列表中明确包含"发票"。因此，规则满足。
- **状态：** 通过
- **理由：** 根据来源，附件类型包含"发票"。

这是一个包含JSON格式的思维链：
```json
{
  "test": "这不应该影响思维链的保存",
  "special_chars": "特殊字符：\"引号\"、'单引号'、\\反斜杠"
}
```

**结论**：所有规则都应通过。"""
        
        # 写入思维链
        manager.update_ai_thinking(
            phase_key="phase1",
            ai_thinking=test_thinking,
            phase_name="附件完整性检查",
            status="completed",
            message="测试完成",
            detail="包含markdown和JSON的复杂思维链"
        )
        
        # 验证写入结果
        report_data = manager.get_report_data()
        
        if "ai_thinking_chain" in report_data and "phases_history" in report_data["ai_thinking_chain"]:
            phase1_data = report_data["ai_thinking_chain"]["phases_history"].get("phase1")
            if phase1_data:
                saved_thinking = phase1_data.get("ai_thinking", "")
                
                # 验证内容完整性
                if "## 🔍 附件完整性检查" in saved_thinking:
                    print("✅ 思维链标题保存正确")
                else:
                    print("❌ 思维链标题丢失")
                
                if "```json" in saved_thinking:
                    print("✅ JSON代码块保存正确")
                else:
                    print("❌ JSON代码块丢失")
                
                if "特殊字符：\"引号\"" in saved_thinking:
                    print("✅ 特殊字符保存正确")
                else:
                    print("❌ 特殊字符处理有问题")
                
                if len(saved_thinking) == len(test_thinking):
                    print("✅ 思维链长度完全一致")
                else:
                    print(f"⚠️ 思维链长度差异: 原始{len(test_thinking)}, 保存{len(saved_thinking)}")
                
                print(f"📊 思维链保存长度: {len(saved_thinking)} 字符")
                return True
            else:
                print("❌ phase1数据未找到")
                return False
        else:
            print("❌ AI思维链结构不完整")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'manager' in locals():
                if os.path.exists(manager.report_file_path):
                    os.remove(manager.report_file_path)
                    print("🧹 测试文件已清理")
        except:
            pass

def test_incremental_audit_results():
    """测试审核结果增量写入机制（优化2）"""
    print("\n📊 测试审核结果增量写入机制")
    print("=" * 60)
    
    try:
        from orchestrator_v2 import ReportFileManager
        
        test_doc_num = "TEST_INCREMENTAL_001"
        manager = ReportFileManager(test_doc_num)
        
        # 模拟4个阶段的审核结果
        phase_results = [
            # 阶段1：附件完整性检查
            {
                "phase_key": "phase1",
                "phase_name": "附件完整性检查",
                "results": [
                    {"rule_id": "规则1：检查是否上传发票", "status": "通过", "reason": "发票已上传"},
                    {"rule_id": "规则2：检查是否上传审批表", "status": "通过", "reason": "审批表已上传"},
                    {"rule_id": "规则3：检查是否上传小票", "status": "通过", "reason": "小票已上传"}
                ]
            },
            # 阶段2：字段一致性检查
            {
                "phase_key": "phase2", 
                "phase_name": "字段内容与一致性检查",
                "results": [
                    {"rule_id": "规则6：检查招待发起主体", "status": "通过", "reason": "发起主体正确"},
                    {"rule_id": "规则7：检查招待对象", "status": "警告", "reason": "招待对象需要进一步确认"},
                    {"rule_id": "规则8：检查发起主体一致性", "status": "通过", "reason": "一致性检查通过"}
                ]
            },
            # 阶段3：金额标准检查
            {
                "phase_key": "phase3",
                "phase_name": "金额与标准检查", 
                "results": [
                    {"rule_id": "规则25：检查实际消费是否超预算", "status": "通过", "reason": "未超预算"},
                    {"rule_id": "规则26：检查酒水使用情况", "status": "通过", "reason": "无酒水消费"},
                    {"rule_id": "规则27-28：检查人均消费是否超标", "status": "失败", "reason": "人均消费超标"}
                ]
            },
            # 阶段4：合规性检查
            {
                "phase_key": "phase4",
                "phase_name": "八项规定合规性检查",
                "results": [
                    {"rule_id": "规则31：检查是否违反八项规定", "status": "通过", "reason": "符合八项规定"},
                    {"rule_id": "规则32：检查招待标准", "status": "通过", "reason": "招待标准合规"}
                ]
            }
        ]
        
        # 逐阶段写入审核结果
        for phase_data in phase_results:
            print(f"📝 写入阶段 {phase_data['phase_key']}: {phase_data['phase_name']}")
            
            manager.update_audit_results(
                phase_key=phase_data["phase_key"],
                phase_results=phase_data["results"],
                phase_name=phase_data["phase_name"]
            )
            
            # 验证当前状态
            report_data = manager.get_report_data()
            current_details = report_data.get("details", [])
            current_summary = report_data.get("summary", {})
            
            print(f"  当前details数量: {len(current_details)}")
            print(f"  当前统计: 总计{current_summary.get('total_rules_checked', 0)}, "
                  f"通过{current_summary.get('passed_count', 0)}, "
                  f"警告{current_summary.get('warning_count', 0)}, "
                  f"失败{current_summary.get('failed_count', 0)}")
            
            time.sleep(0.1)  # 模拟时间间隔
        
        # 最终验证
        final_report = manager.get_report_data()
        final_details = final_report.get("details", [])
        final_summary = final_report.get("summary", {})
        
        print(f"\n📊 最终结果验证:")
        print(f"  总规则数: {len(final_details)} (期望: 11)")
        print(f"  统计数据: {final_summary}")
        
        # 验证数据结构
        success_checks = []
        
        # 检查1：总数是否正确
        expected_total = sum(len(p["results"]) for p in phase_results)
        if len(final_details) == expected_total:
            success_checks.append("✅ 总规则数正确")
        else:
            success_checks.append(f"❌ 总规则数错误: 期望{expected_total}, 实际{len(final_details)}")
        
        # 检查2：统计是否正确
        expected_pass = sum(1 for p in phase_results for r in p["results"] if r["status"] == "通过")
        expected_warning = sum(1 for p in phase_results for r in p["results"] if r["status"] == "警告")
        expected_fail = sum(1 for p in phase_results for r in p["results"] if r["status"] == "失败")
        
        if final_summary.get("passed_count") == expected_pass:
            success_checks.append("✅ 通过数量正确")
        else:
            success_checks.append(f"❌ 通过数量错误: 期望{expected_pass}, 实际{final_summary.get('passed_count')}")
        
        if final_summary.get("warning_count") == expected_warning:
            success_checks.append("✅ 警告数量正确")
        else:
            success_checks.append(f"❌ 警告数量错误: 期望{expected_warning}, 实际{final_summary.get('warning_count')}")
        
        if final_summary.get("failed_count") == expected_fail:
            success_checks.append("✅ 失败数量正确")
        else:
            success_checks.append(f"❌ 失败数量错误: 期望{expected_fail}, 实际{final_summary.get('failed_count')}")
        
        # 检查3：阶段信息是否正确
        phase_keys = set(d.get("phase_key") for d in final_details)
        expected_phases = set(p["phase_key"] for p in phase_results)
        if phase_keys == expected_phases:
            success_checks.append("✅ 阶段信息正确")
        else:
            success_checks.append(f"❌ 阶段信息错误: 期望{expected_phases}, 实际{phase_keys}")
        
        # 输出检查结果
        for check in success_checks:
            print(f"  {check}")
        
        # 总体评估
        failed_checks = [c for c in success_checks if c.startswith("❌")]
        if not failed_checks:
            print("🎉 增量写入机制测试完全成功！")
            return True
        else:
            print(f"⚠️ 增量写入机制测试部分失败: {len(failed_checks)} 项检查失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试文件
        try:
            if 'manager' in locals():
                if os.path.exists(manager.report_file_path):
                    os.remove(manager.report_file_path)
                    print("🧹 测试文件已清理")
        except:
            pass

def main():
    """主测试函数"""
    print("🔧 增量写入机制优化测试")
    print("=" * 80)
    
    # 切换到项目目录
    os.chdir(project_root)
    
    # 执行测试
    tests = [
        ("AI思维链写入机制（优化1）", test_ai_thinking_writing),
        ("审核结果增量写入机制（优化2）", test_incremental_audit_results)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📊 测试结果汇总")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有优化测试通过！增量写入机制工作正常")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

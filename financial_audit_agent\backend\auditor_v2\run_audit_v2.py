#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版审核执行入口 - 分步式智能审核系统
作为程序的启动点,加载配置,初始化对象,执行审核,生成报告
"""

import json
import os
import sys
import argparse
import webbrowser
from pathlib import Path

# 设置编码以避免Windows控制台乱码
try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from encoding_utils import init_encoding, safe_print
    init_encoding()
except ImportError:
    # 如果导入失败,使用基本的编码设置
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul 2>&1')
        except:
            pass

    def safe_print(text):
        try:
            print(text)
        except UnicodeEncodeError:
            print(text.encode('utf-8', errors='replace').decode('utf-8'))

# 添加父目录到路径,以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # 尝试相对导入（作为包运行时）
    from .orchestrator_v2 import OrchestratorV2
except ImportError:
    # 回退到绝对导入（直接运行时）
    from orchestrator_v2 import OrchestratorV2

try:
    from llm_caller import LLMCaller
except ImportError:
    # 如果在auditor_v2目录中,尝试从上级目录导入
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from llm_caller import LLMCaller


def load_config(config_path: str = "../config.json") -> dict:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        dict: 配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print(f"[错误] 配置文件不存在: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"[错误] 配置文件格式错误: {e}")
        return {}


def validate_files(*file_paths) -> bool:
    """
    验证文件是否存在
    
    Args:
        *file_paths: 文件路径列表
        
    Returns:
        bool: 所有文件都存在返回True
    """
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"[错误] 文件不存在: {file_path}")
            return False
    return True


def save_report(report: dict, output_path: str) -> bool:
    """
    保存审核报告
    
    Args:
        report: 审核报告字典
        output_path: 输出文件路径
        
    Returns:
        bool: 保存成功返回True
    """
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"[文档] 审核报告已保存: {output_path}")
        return True
    except Exception as e:
        print(f"[错误] 保存报告失败: {e}")
        return False


def open_frontend(report_path: str):
    """
    尝试打开前端页面显示报告

    Args:
        report_path: 报告文件路径
    """
    try:
        # 使用智能Web启动器
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from auto_web_launcher import open_with_fallback

        print("[网络] 正在智能启动Web服务器并打开页面...")
        open_with_fallback(report_path)

    except ImportError:
        # 回退到原始方法
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)),
                                   "frontend", "audit_viewer.html")

        if os.path.exists(frontend_path):
            http_url = "http://localhost:8000/frontend/audit_viewer.html"

            # 尝试使用谷歌浏览器打开
            try:
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
                ]

                chrome_path = None
                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_path = path
                        break

                if chrome_path:
                    # 使用subprocess.run代替os.system来正确处理路径中的空格
                    import subprocess
                    try:
                        subprocess.run([chrome_path, "--new-window", http_url], check=False)
                        print(f"[网络] 已使用谷歌浏览器打开审核报告页面: {http_url}")
                    except Exception as e:
                        print(f"[警告] Chrome启动失败,使用默认浏览器: {e}")
                        webbrowser.open(http_url)
                        print(f"[网络] 已使用默认浏览器打开审核报告页面: {http_url}")
                else:
                    webbrowser.open(http_url)
                    print(f"[网络] 已使用默认浏览器打开审核报告页面: {http_url}")
            except:
                webbrowser.open(http_url)
                print(f"[网络] 已在浏览器中打开审核报告页面: {http_url}")

            print("[提示] 如果页面显示CORS错误,请先运行: python start_web_server.py")
        else:
            print("[警告] 前端页面不存在,请手动查看报告文件")
    except Exception as e:
        print(f"[警告] 无法打开前端页面: {e}")


def get_file_paths_from_config(config, args):
    """从配置文件获取文件路径,优先使用新的配置项,支持单据编号替换"""
    form_file = args.form
    attachment_file = args.attachment

    # 获取单据编号参数（支持多种参数名）
    document_number = getattr(args, 'document_number', None) or getattr(args, 'doc_num', None)

    print(f"[查找] 路径解析调试信息:")
    print(f"   - 原始表单路径: {form_file}")
    print(f"   - 原始附件路径: {attachment_file}")
    print(f"   - 文档编号: {document_number}")
    print(f"   - 配置中的表单路径: {config.get('FORM_EXTRACTION_PATH', 'None')}")
    print(f"   - 配置中的附件路径: {config.get('ATTACHMENT_EXTRACTION_PATH', 'None')}")

    # 优先使用配置文件中的路径（如果存在且有文档编号）
    if config.get('FORM_EXTRACTION_PATH') and document_number:
        config_form_path = config['FORM_EXTRACTION_PATH']

        # 替换路径中的占位符
        if '{document_number}' in config_form_path:
            form_file = config_form_path.replace('{document_number}', document_number)
            print(f"[成功] 使用配置文件路径（替换document_number）: {form_file}")
        elif '{doc_num}' in config_form_path:
            form_file = config_form_path.replace('{doc_num}', document_number)
            print(f"[成功] 使用配置文件路径（替换doc_num）: {form_file}")
        else:
            # 如果配置中没有占位符,但包含基础路径,则动态构建
            if 'C:\\Users\\<USER>\\Desktop\\测试附件\\测试记录' in config_form_path:
                base_path = r"C:\Users\<USER>\Desktop\测试附件\测试记录"
                form_file = os.path.join(base_path, document_number, "表单提取.json")
                print(f"[成功] 使用配置基础路径动态构建: {form_file}")
            else:
                form_file = config_form_path
                print(f"[成功] 使用配置文件原始路径: {form_file}")
    elif config.get('FORM_EXTRACTION_PATH'):
        # 没有文档编号时,直接使用配置路径
        form_file = config['FORM_EXTRACTION_PATH']
        print(f"[成功] 使用配置文件路径（无文档编号）: {form_file}")
    else:
        print(f"[警告] 使用命令行传入的表单路径: {form_file}")

    if config.get('ATTACHMENT_EXTRACTION_PATH') and document_number:
        config_attachment_path = config['ATTACHMENT_EXTRACTION_PATH']

        # 替换路径中的占位符
        if '{document_number}' in config_attachment_path:
            attachment_file = config_attachment_path.replace('{document_number}', document_number)
            print(f"[成功] 使用配置文件路径（替换document_number）: {attachment_file}")
        elif '{doc_num}' in config_attachment_path:
            attachment_file = config_attachment_path.replace('{doc_num}', document_number)
            print(f"[成功] 使用配置文件路径（替换doc_num）: {attachment_file}")
        else:
            # 如果配置中没有占位符,但包含基础路径,则动态构建
            if 'C:\\Users\\<USER>\\Desktop\\测试附件\\测试记录' in config_attachment_path:
                base_path = r"C:\Users\<USER>\Desktop\测试附件\测试记录"
                attachment_file = os.path.join(base_path, document_number, "附件提取.json")
                print(f"[成功] 使用配置基础路径动态构建: {attachment_file}")
            else:
                attachment_file = config_attachment_path
                print(f"[成功] 使用配置文件原始路径: {attachment_file}")
    elif config.get('ATTACHMENT_EXTRACTION_PATH'):
        # 没有文档编号时,直接使用配置路径
        attachment_file = config['ATTACHMENT_EXTRACTION_PATH']
        print(f"[成功] 使用配置文件路径（无文档编号）: {attachment_file}")
    else:
        print(f"[警告] 使用命令行传入的附件路径: {attachment_file}")

    # 回退到旧的配置项（兼容性）
    if 'CUSTOM_INPUT_FILES' in config:
        custom_files = config['CUSTOM_INPUT_FILES']
        if form_file == "../ZDBXD2025051300001_表单提取.json" and 'form_file' in custom_files:
            form_file = custom_files['form_file']
            print(f"[成功] 使用兼容配置的表单文件: {os.path.basename(form_file)}")

        if attachment_file == "../ZDBXD2025051300001_附件提取.json" and 'attachment_file' in custom_files:
            attachment_file = custom_files['attachment_file']
            print(f"[成功] 使用兼容配置的附件文件: {os.path.basename(attachment_file)}")

    # 最终验证文件是否存在
    print(f"[查找] 最终路径验证:")
    print(f"   - 表单文件: {form_file} {'[成功]存在' if os.path.exists(form_file) else '[错误]不存在'}")
    print(f"   - 附件文件: {attachment_file} {'[成功]存在' if os.path.exists(attachment_file) else '[错误]不存在'}")

    return form_file, attachment_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="新版分步式智能审核系统")
    parser.add_argument("--form", default="../ZDBXD2025051300001_表单提取.json",
                       help="表单JSON文件路径")
    parser.add_argument("--attachment", default="../ZDBXD2025051300001_附件提取.json",
                       help="附件JSON文件路径")
    parser.add_argument("--rules", default="../../业务招待费审核规则_V2.txt",
                       help="规则文件路径（默认使用优化后的V2.0规则）")
    parser.add_argument("--output", default="../audit_reports/audit_report_v2.json",
                       help="输出报告路径")
    parser.add_argument("--config", default="../config.json",
                       help="配置文件路径")
    parser.add_argument("--document-number", "--doc", "--doc-num", help="单据编号,用于动态路径替换")
    parser.add_argument("--no-browser", action="store_true",
                       help="不自动打开浏览器")

    args = parser.parse_args()

    print("[启动] 新版分步式智能审核系统 v2.0")
    print("=" * 50)

    # 1. 加载配置
    print("[清单] 加载配置...")
    config = load_config(args.config)
    if not config:
        print("[错误] 无法加载配置,程序退出")
        return 1

    # 检查API密钥
    if not config.get('LLM_API_KEY') or config['LLM_API_KEY'] == "在此处填入你的API密钥":
        print("[错误] 请在配置文件中设置有效的LLM_API_KEY")
        return 1

    # 1.5. 从配置获取文件路径
    print("[目录] 获取文件路径...")
    form_file, attachment_file = get_file_paths_from_config(config, args)

    # 2. 验证输入文件
    print("[目录] 验证输入文件...")
    if not validate_files(form_file, attachment_file, args.rules):
        print("[错误] 输入文件验证失败,程序退出")
        return 1
    
    print("[成功] 所有输入文件验证通过")
    
    # 3. 初始化LLM调用器
    print("[初始化] 初始化LLM调用器...")
    try:
        llm_caller = LLMCaller(
            api_key=config['LLM_API_KEY'],
            model_name=config.get('LLM_MODEL_NAME', 'qwen-max'),
            config=config
        )
        print("[成功] LLM调用器初始化成功")
    except Exception as e:
        print(f"[错误] LLM调用器初始化失败: {e}")
        return 1
    
    # 4. 执行审核
    print("\n[查找] 开始执行审核...")
    # 获取文档编号参数
    document_number = getattr(args, 'document_number', None) or getattr(args, 'doc_num', None)
    orchestrator = OrchestratorV2(config, llm_caller, document_number)

    try:
        final_report = orchestrator.run_audit(form_file, attachment_file, args.rules)

        if not final_report:
            print("[错误] 审核执行失败")
            return 1

    except Exception as e:
        print(f"[错误] 审核过程中发生异常: {e}")
        return 1

    # 4.5. 生成动态输出文件名
    # 优先使用命令行传入的文档编号
    if document_number:
        final_doc_num = document_number
        print(f"[文档] 使用命令行指定的文档编号: {final_doc_num}")
    else:
        # 回退到从数据中提取文档编号
        try:
            from .data_consolidator import DataConsolidator
            consolidator = DataConsolidator(form_file, attachment_file)
            final_doc_num = consolidator.get_document_number()
            print(f"[文档] 从数据中提取文档编号: {final_doc_num}")
        except Exception as e:
            final_doc_num = "default"
            print(f"[警告] 无法提取文档编号,使用默认值: {e}")

    # 如果用户没有指定自定义输出路径,使用动态文件名
    if args.output == "../audit_reports/audit_report_v2.json":
        dynamic_output = f"../audit_reports/audit_report_{final_doc_num}.json"
        print(f"[文档] 使用动态文件名: audit_report_{final_doc_num}.json")
    else:
        dynamic_output = args.output
        print(f"[文档] 使用指定文件名: {args.output}")

    # 5. 保存报告
    print("\n[保存] 保存审核报告...")
    if not save_report(final_report, dynamic_output):
        return 1
    
    # 6. 显示结果摘要
    print("\n[统计] 审核结果摘要:")
    print("-" * 30)
    summary = final_report.get('summary', {})
    print(f"总规则数: {summary.get('total_rules_checked', 0)}")
    print(f"[成功] 通过: {summary.get('passed_count', 0)}")
    print(f"[警告] 警告: {summary.get('warning_count', 0)}")
    print(f"[错误] 失败: {summary.get('failed_count', 0)}")
    
    # 7. 打开前端页面（可选）
    if not args.no_browser:
        print("\n[网络] 打开审核报告页面...")
        open_frontend(dynamic_output)
    
    print("\n[完成] 审核完成!")
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)

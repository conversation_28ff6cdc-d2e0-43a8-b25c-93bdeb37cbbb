#!/usr/bin/env python3
"""
模拟LLM调用器 - 用于测试完整的审核流程
生成真实的AI思维链和审核结果,无需真实的LLM API
"""

import json
import time
import random
from typing import Tuple, List, Dict, Any

class MockLLMCaller:
    """模拟LLM调用器,生成真实的AI思维链和审核结果"""
    
    def __init__(self, api_key: str, model_name: str, config: Dict[str, Any] = None):
        """初始化模拟LLM调用器"""
        self.api_key = api_key
        self.model = model_name
        self.config = config or {}
        self.call_count = 0
        
        print(f"🤖 [模拟LLM] 初始化成功 - 模型: {model_name}")
        print(f"🤖 [模拟LLM] 将生成真实的AI思维链和审核结果")
    
    def query_semantic_rule(self, prompt: str, max_retries: int = 3) -> str:
        """模拟语义规则查询"""
        self.call_count += 1
        
        # 模拟处理时间
        time.sleep(random.uniform(1, 3))
        
        # 根据提示内容生成相应的响应
        if "附件完整性" in prompt:
            return self._generate_attachment_response()
        elif "字段内容与一致性" in prompt:
            return self._generate_consistency_response()
        elif "金额与标准" in prompt:
            return self._generate_amount_response()
        elif "八项规定合规性" in prompt:
            return self._generate_compliance_response()
        else:
            return self._generate_default_response()
    
    def query_with_reasoning(self, prompt: str, max_retries: int = 3) -> Tuple[str, str]:
        """模拟带推理的查询,返回思维链和结果"""
        self.call_count += 1
        
        # 模拟处理时间
        time.sleep(random.uniform(2, 4))
        
        print(f"🤖 [模拟LLM] 第{self.call_count}次调用 - 生成AI思维链")
        
        # 根据提示内容生成相应的思维链和结果
        if "附件完整性" in prompt:
            thinking = self._generate_attachment_thinking()
            result = self._generate_attachment_response()
        elif "字段内容与一致性" in prompt:
            thinking = self._generate_consistency_thinking()
            result = self._generate_consistency_response()
        elif "金额与标准" in prompt:
            thinking = self._generate_amount_thinking()
            result = self._generate_amount_response()
        elif "八项规定合规性" in prompt:
            thinking = self._generate_compliance_thinking()
            result = self._generate_compliance_response()
        else:
            thinking = self._generate_default_thinking()
            result = self._generate_default_response()
        
        print(f"🤖 [模拟LLM] 生成思维链长度: {len(thinking)} 字符")
        print(f"🤖 [模拟LLM] 生成结果长度: {len(result)} 字符")
        
        return thinking, result
    
    def _generate_attachment_thinking(self) -> str:
        """生成附件完整性检查的思维链"""
        return """### 🔍 附件完整性检查分析过程

**第一步:理解审核要求**
我需要检查业务招待费报销所需的基础材料是否齐全.根据规则要求,必须包括:发票, 审批表, 小票, 支付记录等关键附件.

**第二步:分析提供的数据**
从JSON数据中,我看到了以下附件信息:
- 发票文件:已提供,格式为PDF
- 审批表:已提供,包含完整的审批流程
- 小票:已提供,显示具体消费明细
- 支付记录:已提供,显示银行转账记录

**第三步:逐项验证**
1. 发票完整性:✅ 发票信息完整,包含必要的税务信息
2. 审批流程:✅ 审批表显示完整的审批链条
3. 消费凭证:✅ 小票显示详细的消费项目
4. 支付证明:✅ 银行记录与报销金额一致

**第四步:综合判断**
所有必需的附件都已提供,且格式规范, 内容完整.附件之间的信息相互印证,没有发现明显的缺失或异常.

**第五步:风险评估**
附件完整性方面风险较低,建议通过此阶段的检查."""

    def _generate_consistency_thinking(self) -> str:
        """生成字段一致性检查的思维链"""
        return """### 🔍 字段内容与一致性检查分析过程

**第一步:数据结构分析**
我正在分析表单数据和附件数据之间的一致性.需要重点关注金额, 日期, 人员信息等关键字段的匹配情况.

**第二步:金额一致性验证**
- 报销申请金额:1,250.00元
- 发票金额:1,250.00元
- 小票总额:1,250.00元
- 银行支付金额:1,250.00元
✅ 所有金额字段完全一致

**第三步:日期逻辑检查**
- 消费日期:2024-07-15
- 申请日期:2024-07-16
- 审批日期:2024-07-17
- 报销日期:2024-07-18
✅ 日期逻辑合理,符合业务流程

**第四步:人员信息核验**
- 申请人:张三
- 审批人:李四（部门经理）
- 财务审核:王五
✅ 人员信息在各文档中保持一致

**第五步:业务场景分析**
这是一次客户招待活动,地点在高档餐厅,参与人员包括公司销售团队和重要客户.消费项目主要是餐饮费用,符合业务招待的性质.

**第六步:异常点识别**
发现一个需要注意的点:餐饮费用中包含了酒水消费,需要进一步确认是否符合公司的招待标准.

**第七步:合规性初步评估**
整体数据一致性良好,但酒水消费部分需要在下一阶段进行更详细的标准检查."""

    def _generate_amount_thinking(self) -> str:
        """生成金额标准检查的思维链"""
        return """### 🔍 金额与标准检查分析过程

**第一步:预算标准分析**
根据公司政策,业务招待费的标准为:
- 部门级客户招待:每人每次不超过200元
- 重要客户招待:每人每次不超过300元
- 本次招待涉及5人,预算上限应为1,500元

**第二步:消费明细分析**
从小票明细可以看到:
- 主食费用:800元（4道菜品）
- 酒水费用:350元（2瓶红酒）
- 服务费:100元（10%服务费）
- 总计:1,250元

**第三步:单人消费计算**
总费用1,250元 ÷ 5人 = 250元/人
这个标准在重要客户招待的范围内（300元/人以下）,符合预算要求.

**第四步:消费结构分析**
- 餐饮主体费用占比:64%（合理）
- 酒水费用占比:28%（需要关注）
- 服务费占比:8%（正常）

**第五步:行业对比**
与同行业类似招待活动相比,此次消费水平处于中等偏上,但仍在可接受范围内.

**第六步:风险点识别**
酒水消费占比较高（28%）,需要确认是否符合公司关于酒水消费的具体规定.

**第七步:标准符合性结论**
总体金额符合预算标准,但酒水消费比例需要进一步审查.建议在合规性检查阶段重点关注酒水政策的执行情况."""

    def _generate_compliance_thinking(self) -> str:
        """生成合规性检查的思维链"""
        return """### 🔍 八项规定合规性检查分析过程

**第一步:政策框架理解**
八项规定对公务接待有严格要求,虽然这是企业内部的业务招待,但仍需要参考相关精神,确保消费的合理性和必要性.

**第二步:招待必要性分析**
- 招待对象:重要合作客户
- 招待目的:商务洽谈和关系维护
- 招待时机:项目合作关键节点
✅ 招待具有明确的商业目的和必要性

**第三步:消费标准审查**
- 招待地点:中档商务餐厅（非奢华场所）
- 消费水平:人均250元（适中水平）
- 消费项目:以餐饮为主,无娱乐消费
✅ 消费标准总体合理

**第四步:酒水政策检查**
重点关注酒水消费:
- 酒水类型:红酒（非高档烈酒）
- 酒水价格:175元/瓶（中等价位）
- 消费数量:2瓶（5人聚餐合理）
⚠️ 需要确认公司是否有酒水消费的具体限制

**第五步:透明度评估**
- 消费明细清晰完整
- 审批流程规范透明
- 相关凭证齐全有效
✅ 符合透明度要求

**第六步:社会影响评估**
此次招待活动规模适中,消费水平合理,不会产生负面的社会影响.

**第七步:最终合规性判断**
整体符合合规要求,但建议:
1. 明确酒水消费政策
2. 加强招待活动的事前审批
3. 定期审查招待费用标准

**结论**:基本符合合规要求,有2个细节需要改进."""

    def _generate_attachment_response(self) -> str:
        """生成附件完整性检查的结果"""
        return json.dumps([
            {"rule_id": "规则1", "status": "PASS", "reason": "发票文件完整,格式规范"},
            {"rule_id": "规则2", "status": "PASS", "reason": "审批表包含完整审批流程"},
            {"rule_id": "规则3", "status": "PASS", "reason": "小票显示详细消费明细"},
            {"rule_id": "规则4", "status": "PASS", "reason": "银行支付记录与报销金额一致"},
            {"rule_id": "规则5", "status": "PASS", "reason": "所有必需附件均已提供"}
        ], ensure_ascii=False, indent=2)

    def _generate_consistency_response(self) -> str:
        """生成一致性检查的结果"""
        results = []
        for i in range(6, 25):  # 规则6-24
            if i == 12:  # 模拟一个警告
                results.append({"rule_id": f"规则{i}", "status": "WARNING", "reason": "酒水消费占比较高,需要进一步确认"})
            elif i == 18:  # 模拟另一个警告
                results.append({"rule_id": f"规则{i}", "status": "WARNING", "reason": "消费时间为工作日晚餐,建议确认业务必要性"})
            else:
                results.append({"rule_id": f"规则{i}", "status": "PASS", "reason": f"规则{i}检查通过"})
        
        return json.dumps(results, ensure_ascii=False, indent=2)

    def _generate_amount_response(self) -> str:
        """生成金额标准检查的结果"""
        return json.dumps([
            {"rule_id": "规则25", "status": "PASS", "reason": "总金额在预算范围内"},
            {"rule_id": "规则26", "status": "PASS", "reason": "人均消费符合标准"},
            {"rule_id": "规则27", "status": "PASS", "reason": "消费结构合理"},
            {"rule_id": "规则28", "status": "WARNING", "reason": "酒水消费占比偏高"},
            {"rule_id": "规则29", "status": "PASS", "reason": "服务费比例正常"},
            {"rule_id": "规则30", "status": "PASS", "reason": "消费地点符合标准"}
        ], ensure_ascii=False, indent=2)

    def _generate_compliance_response(self) -> str:
        """生成合规性检查的结果"""
        return json.dumps([
            {"rule_id": "规则31", "status": "PASS", "reason": "招待具有明确商业目的"},
            {"rule_id": "规则32", "status": "PASS", "reason": "消费标准总体合理"},
            {"rule_id": "规则33", "status": "WARNING", "reason": "建议明确酒水消费政策"},
            {"rule_id": "规则34", "status": "PASS", "reason": "审批流程规范透明"},
            {"rule_id": "规则35", "status": "PASS", "reason": "消费明细清晰完整"},
            {"rule_id": "规则36", "status": "PASS", "reason": "符合透明度要求"},
            {"rule_id": "规则37", "status": "PASS", "reason": "无负面社会影响"},
            {"rule_id": "规则38", "status": "PASS", "reason": "整体符合合规要求"}
        ], ensure_ascii=False, indent=2)

    def _generate_default_thinking(self) -> str:
        """生成默认思维链"""
        return """### 🔍 AI分析过程

**分析步骤1:理解审核要求**
我正在分析提供的业务招待费报销数据,需要根据相关规则进行全面审核.

**分析步骤2:数据解析**
从JSON数据中提取关键信息,包括金额, 日期, 人员, 消费明细等.

**分析步骤3:规则应用**
逐一应用审核规则,检查数据的完整性, 一致性和合规性.

**分析步骤4:风险识别**
识别潜在的风险点和需要关注的异常情况.

**分析步骤5:结论生成**
基于分析结果,生成具体的审核建议和处理意见."""

    def _generate_default_response(self) -> str:
        """生成默认响应"""
        return json.dumps([
            {"rule_id": "规则1", "status": "PASS", "reason": "检查通过"}
        ], ensure_ascii=False, indent=2)

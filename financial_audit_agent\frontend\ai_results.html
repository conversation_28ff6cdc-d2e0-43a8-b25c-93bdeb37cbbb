<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI审核结果 - 智能分析报告</title>
    <link rel="stylesheet" href="ai_results.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 动态背景 -->
    <div class="cosmic-background" id="cosmic-background"></div>
    
    <!-- 主容器 -->
    <div class="results-container">
        <!-- 顶部标题区域 -->
        <header class="results-header">
            <div class="header-glow"></div>
            <div class="ai-emblem">
                <div class="emblem-core"></div>
                <div class="emblem-rings">
                    <div class="ring ring-1"></div>
                    <div class="ring ring-2"></div>
                    <div class="ring ring-3"></div>
                </div>
            </div>
            <h1 class="main-title">AI智能审核报告</h1>
            <p class="subtitle">基于神经网络的深度分析结果</p>
            <div class="scan-line"></div>
        </header>

        <!-- AI思考过程展示区域 -->
        <section class="ai-thinking-chain" id="ai-thinking-chain">
            <div class="thinking-header">
                <h3>🤖 AI思考过程</h3>
                <div class="thinking-controls">
                    <button class="control-btn" id="expand-all-btn">
                        <span class="btn-icon">📖</span>
                        <span class="btn-text">展开全部</span>
                    </button>
                    <button class="control-btn" id="collapse-all-btn">
                        <span class="btn-icon">📚</span>
                        <span class="btn-text">收起全部</span>
                    </button>
                    <button class="control-btn" id="copy-thinking-btn">
                        <span class="btn-icon">📋</span>
                        <span class="btn-text">复制内容</span>
                    </button>
                </div>
            </div>

            <div class="thinking-search">
                <div class="search-container">
                    <input type="text" id="thinking-search-input" placeholder="搜索AI思考内容..." class="search-input">
                    <button class="search-btn" id="thinking-search-btn">🔍</button>
                </div>
                <div class="search-results" id="search-results"></div>
            </div>

            <div class="thinking-content" id="thinking-content">
                <div class="thinking-loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载AI思考过程...</p>
                </div>
            </div>

            <div class="thinking-metadata" id="thinking-metadata" style="display: none;">
                <div class="metadata-title">📊 思维链元数据</div>
                <div class="metadata-content" id="metadata-content"></div>
            </div>
        </section>

        <!-- 整体状态展示 -->
        <section class="overall-status" id="overall-status">
            <div class="status-hologram">
                <div class="hologram-content">
                    <div class="status-icon" id="status-icon">
                        <div class="icon-core"></div>
                        <div class="icon-pulse"></div>
                    </div>
                    <div class="status-text">
                        <h2 id="status-title">分析中...</h2>
                        <p id="status-description">AI正在处理审核数据</p>
                    </div>
                </div>
                <div class="hologram-grid"></div>
            </div>
        </section>

        <!-- 统计数据展示 -->
        <section class="stats-dashboard">
            <div class="dashboard-title">
                <h3>📊 智能分析统计</h3>
                <div class="title-line"></div>
            </div>
            <div class="stats-grid">
                <div class="stat-card total-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">🔍</div>
                    <div class="card-content">
                        <div class="card-number" id="total-count">--</div>
                        <div class="card-label">总规则数</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>
                
                <div class="stat-card passed-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">✅</div>
                    <div class="card-content">
                        <div class="card-number" id="passed-count">--</div>
                        <div class="card-label">通过规则</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>
                
                <div class="stat-card warning-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">⚠️</div>
                    <div class="card-content">
                        <div class="card-number" id="warning-count">--</div>
                        <div class="card-label">警告项目</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>
                
                <div class="stat-card failed-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">❌</div>
                    <div class="card-content">
                        <div class="card-number" id="failed-count">--</div>
                        <div class="card-label">失败项目</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>
            </div>
        </section>

        <!-- AI分析洞察 -->
        <section class="ai-insights" id="ai-insights">
            <div class="insights-header">
                <h3>🧠 AI智能洞察</h3>
                <div class="neural-indicator">
                    <div class="neural-dot"></div>
                    <span>神经网络分析</span>
                </div>
            </div>
            <div class="insights-content" id="insights-content">
                <div class="insight-loading">
                    <div class="loading-brain">
                        <div class="brain-wave"></div>
                        <div class="brain-wave"></div>
                        <div class="brain-wave"></div>
                    </div>
                    <p>AI正在生成智能洞察...</p>
                </div>
            </div>
        </section>


        <!-- 详细结果表格 -->
        <section class="detailed-results">
            <div class="results-header-section">
                <h3>📋 详细审核结果</h3>
            </div>
            
            <div class="results-table-container">
                <table class="results-table" id="results-table">
                    <thead>
                        <tr>
                            <th>状态</th>
                            <th>规则ID</th>
                            <th>审核结果</th>
                            <th>风险等级</th>
                        </tr>
                    </thead>
                    <tbody id="results-tbody">
                        <tr>
                            <td colspan="4" class="loading-row">
                                <div class="table-loading">
                                    <div class="loading-spinner"></div>
                                    <span>正在加载审核数据...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 操作按钮区域 -->
        <section class="action-panel">
            <div class="panel-glow"></div>
            <button class="action-btn primary" id="export-report">
                <span class="btn-icon">📄</span>
                <span class="btn-text">导出报告</span>
                <div class="btn-ripple"></div>
            </button>
            <button class="action-btn secondary" id="new-audit">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">新建审核</span>
                <div class="btn-ripple"></div>
            </button>
            <button class="action-btn secondary" id="view-console">
                <span class="btn-icon">🖥️</span>
                <span class="btn-text">返回控制台</span>
                <div class="btn-ripple"></div>
            </button>
        </section>

        <!-- 页脚信息 -->
        <footer class="results-footer">
            <div class="footer-content">
                <div class="ai-signature">
                    <div class="signature-icon">🤖</div>
                    <div class="signature-text">
                        <span class="signature-title">AI财务审核系统</span>
                        <span class="signature-version">Neural Engine v2.0</span>
                    </div>
                </div>
                <div class="generation-info">
                    <span>报告生成时间: <span id="generation-time">--</span></span>
                    <span>分析耗时: <span id="analysis-duration">--</span></span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 导出确认模态框 -->
    <div class="modal-overlay" id="export-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📄 导出审核报告</h3>
                <button class="modal-close" id="close-export-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>选择导出格式：</p>
                <div class="export-options">
                    <button class="export-option" data-format="pdf">
                        <span class="option-icon">📄</span>
                        <span class="option-text">PDF报告</span>
                    </button>
                    <button class="export-option" data-format="excel">
                        <span class="option-icon">📊</span>
                        <span class="option-text">Excel表格</span>
                    </button>
                    <button class="export-option" data-format="json">
                        <span class="option-icon">💾</span>
                        <span class="option-text">JSON数据</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 成功提示 -->
    <div class="success-toast" id="success-toast" style="display: none;">
        <div class="toast-content">
            <div class="toast-icon">✅</div>
            <div class="toast-message">操作成功完成！</div>
        </div>
    </div>

    <script src="ai_results.js"></script>
</body>
</html>

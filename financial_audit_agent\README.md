# AI财务审核系统

基于大语言模型的智能财务审核系统，支持业务招待费等费用的自动化审核。

## 🚀 快速开始

### 1. 环境要求
- Python 3.8+
- 千问大模型API密钥

### 2. 安装依赖
```bash
pip install -r backend/requirements.txt
```

### 3. 配置设置
1. 复制配置模板：
```bash
cp backend/config_example.json backend/config.json
```
2. 编辑 `backend/config.json`，填入您的API密钥

### 4. 启动系统
```bash
# 基本启动（自动打开浏览器）
python start.py --doc-num ZDBXD2025042900003

# 不自动打开浏览器
python start.py --doc-num ZDBXD2025042900003 --no-browser
```

## 📁 项目结构

```
financial_audit_agent/
├── start.py                # 🚀 主启动脚本（唯一入口）
├── reset_audit_status.py   # 🔄 状态重置脚本
├── backend/                # 后端代码
│   ├── auditor_v2/         # V2版本审核引擎
│   ├── api_server.py       # API服务器
│   └── config.json         # 配置文件
├── frontend/               # 前端代码
│   ├── ai_console.html     # AI控制台
│   └── ...
├── audit_reports/          # 审核报告输出
└── 业务招待费审核规则_V2.txt  # 审核规则文件
```

## 🎯 使用流程

1. **准备数据文件**：确保数据文件存在于指定路径
2. **启动系统**：运行 `python start.py --doc-num YOUR_DOC_NUM`
3. **查看进度**：系统会自动打开浏览器显示审核进度
4. **获取结果**：审核完成后在 `audit_reports/` 目录查看报告

## ✨ 功能特点

- 🤖 基于千问大模型的智能审核
- 📊 实时审核进度和AI思考过程显示
- 🔍 20+条详细审核规则检查
- 📈 可视化审核结果和报告
- 🌐 简洁的Web界面操作

## 🔧 故障排除

如遇问题，请检查：
1. 配置文件中的API密钥是否正确
2. 数据文件是否存在且路径正确
3. 网络连接是否正常
4. 端口8002是否被占用

## 📞 技术支持

系统采用简化的单一入口设计，如有问题请确保：
- 只使用 `start.py` 作为启动入口
- 数据文件格式符合系统要求
- Python环境和依赖包正确安装

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档编号支持功能测试脚本
验证V2系统的动态文档编号参数传递功能
"""

import os
import sys
import json
import subprocess
from pathlib import Path


def test_doc_num_support():
    """测试文档编号支持功能"""
    print("🧪 测试V2系统文档编号支持功能")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    test_doc_num = "ZDBXD2025042900003"
    
    # 1. 测试启动器参数解析
    print("\n1. 测试启动器参数解析...")
    
    try:
        # 测试V2启动器
        result = subprocess.run([
            sys.executable, "start_backend_v2.py", 
            "--doc-num", test_doc_num,
            "--create-sample"
        ], capture_output=True, text=True, timeout=10)
        
        if "文档编号: ZDBXD2025042900003" in result.stdout:
            print("✅ V2启动器正确解析文档编号参数")
        else:
            print("❌ V2启动器未正确解析文档编号参数")
            print(f"输出: {result.stdout}")
            
    except subprocess.TimeoutExpired:
        print("⚠️ V2启动器测试超时（正常，因为会尝试启动完整系统）")
    except Exception as e:
        print(f"❌ V2启动器测试失败: {e}")
    
    # 2. 测试版本选择器参数传递
    print("\n2. 测试版本选择器参数传递...")
    
    try:
        result = subprocess.run([
            sys.executable, "start_backend_selector.py",
            "--doc-num", test_doc_num
        ], input="q\n", capture_output=True, text=True, timeout=10)
        
        if "文档编号: ZDBXD2025042900003" in result.stdout:
            print("✅ 版本选择器正确解析文档编号参数")
        else:
            print("❌ 版本选择器未正确解析文档编号参数")
            
    except Exception as e:
        print(f"❌ 版本选择器测试失败: {e}")
    
    # 3. 测试配置文件路径构建
    print("\n3. 测试配置文件路径构建...")
    
    # 检查配置文件
    config_file = project_root / "backend" / "config.json"
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            form_path = config.get('FORM_EXTRACTION_PATH', '')
            attachment_path = config.get('ATTACHMENT_EXTRACTION_PATH', '')
            
            if 'C:\\Users\\<USER>\\Desktop\\测试附件\\测试记录' in form_path:
                print("✅ 配置文件包含正确的基础路径")
                
                # 测试路径替换逻辑
                if '{doc_num}' in form_path or 'ZDBXD2025042900003' in form_path:
                    print("✅ 配置文件支持动态路径替换")
                else:
                    print("⚠️ 配置文件可能需要更新以支持动态路径")
            else:
                print("⚠️ 配置文件路径可能不正确")
                
        except Exception as e:
            print(f"❌ 配置文件读取失败: {e}")
    else:
        print("❌ 配置文件不存在")
    
    # 4. 测试数据文件创建
    print("\n4. 测试数据文件创建...")
    
    test_data_dir = Path(r"C:\Users\<USER>\Desktop\测试附件\测试记录") / test_doc_num
    form_file = test_data_dir / "表单提取.json"
    attachment_file = test_data_dir / "附件提取.json"
    
    if test_data_dir.exists():
        print(f"✅ 测试数据目录存在: {test_data_dir}")
        
        if form_file.exists():
            print("✅ 表单提取文件存在")
        else:
            print("⚠️ 表单提取文件不存在")
            
        if attachment_file.exists():
            print("✅ 附件提取文件存在")
        else:
            print("⚠️ 附件提取文件不存在")
    else:
        print(f"⚠️ 测试数据目录不存在: {test_data_dir}")
    
    # 5. 测试报告文件命名
    print("\n5. 测试报告文件命名...")
    
    reports_dir = project_root / "audit_reports"
    if reports_dir.exists():
        # 查找文档特定的报告文件
        doc_report = reports_dir / f"audit_report_{test_doc_num}.json"
        if doc_report.exists():
            print(f"✅ 找到文档特定报告: audit_report_{test_doc_num}.json")
            
            # 检查报告内容
            try:
                with open(doc_report, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                    
                if 'summary' in report_data and 'details' in report_data:
                    print("✅ 报告文件格式正确")
                else:
                    print("⚠️ 报告文件格式可能不完整")
                    
            except Exception as e:
                print(f"❌ 报告文件读取失败: {e}")
        else:
            print(f"⚠️ 未找到文档特定报告: audit_report_{test_doc_num}.json")
            
        # 列出所有报告文件
        all_reports = list(reports_dir.glob("audit_report*.json"))
        print(f"📊 总共找到 {len(all_reports)} 个报告文件:")
        for report in all_reports:
            print(f"   - {report.name}")
    else:
        print("❌ 报告目录不存在")
    
    # 6. 测试前端URL参数支持
    print("\n6. 测试前端URL参数支持...")
    
    console_file = project_root / "frontend" / "ai_console_enhanced.js"
    if console_file.exists():
        try:
            with open(console_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'URLSearchParams' in content and 'doc_num' in content:
                print("✅ 前端支持URL参数解析")
            else:
                print("⚠️ 前端可能不支持URL参数解析")
                
            if 'audit_report_${docNum}.json' in content:
                print("✅ 前端支持动态报告文件加载")
            else:
                print("⚠️ 前端可能不支持动态报告文件加载")
                
        except Exception as e:
            print(f"❌ 前端文件检查失败: {e}")
    else:
        print("❌ 前端增强文件不存在")
    
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    print("✅ 表示功能正常工作")
    print("⚠️ 表示可能需要注意或改进")
    print("❌ 表示存在问题需要修复")
    
    print(f"\n💡 使用示例:")
    print(f"   python start_backend_v2.py --doc-num {test_doc_num}")
    print(f"   python start_backend_selector.py --doc-num {test_doc_num}")
    print(f"   访问: http://localhost:8002/frontend/ai_console.html?doc_num={test_doc_num}")


def create_test_data():
    """创建测试数据"""
    print("\n🔧 创建测试数据...")
    
    test_doc_num = "ZDBXD2025042900003"
    base_path = Path(r"C:\Users\<USER>\Desktop\测试附件\测试记录")
    test_dir = base_path / test_doc_num
    
    try:
        test_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建表单提取文件
        form_data = {
            "业务招待事前审批表": {
                "申请部门": "销售部",
                "招待事由": "客户商务洽谈",
                "招待日期": "2024-04-29",
                "招待地点": "北京饭店",
                "预算金额": "2000",
                "参与人数": "4",
                "申报事由": "与重要客户进行项目合作洽谈",
                "单据编号": test_doc_num
            },
            "招待明细": {
                "招待日期": "2024-04-29",
                "招待地点": "北京饭店",
                "招待对象": "ABC公司张总",
                "招待内容": "商务午餐",
                "金额": "1800",
                "参与人数": "4",
                "项目名称": "客户商务合作项目"
            },
            "发票": {
                "开票日期": "2024-04-29",
                "购买方": "销售部",
                "销售方名称": "北京饭店有限公司",
                "销售方地址": "北京市东城区王府井大街1号",
                "货物或应税劳务名称": "餐饮服务",
                "价税合计": "1800"
            }
        }
        
        # 创建附件提取文件
        attachment_data = {
            "附件列表": [
                "发票.pdf",
                "业务招待事前审批表.pdf",
                "招待明细.xlsx"
            ],
            "单据编号": test_doc_num
        }
        
        # 保存文件
        form_file = test_dir / "表单提取.json"
        attachment_file = test_dir / "附件提取.json"
        
        with open(form_file, 'w', encoding='utf-8') as f:
            json.dump(form_data, f, ensure_ascii=False, indent=4)
            
        with open(attachment_file, 'w', encoding='utf-8') as f:
            json.dump(attachment_data, f, ensure_ascii=False, indent=4)
        
        print(f"✅ 测试数据已创建:")
        print(f"   - {form_file}")
        print(f"   - {attachment_file}")
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--create-test-data":
        create_test_data()
    else:
        test_doc_num_support()


if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI财务审核控制台 - 动态效果演示</title>
    <link rel="stylesheet" href="ai_console.css">
    <style>
        body {
            background: var(--bg-primary);
            margin: 0;
            padding: 20px;
            font-family: var(--font-primary);
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        .demo-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .demo-title {
            color: var(--text-primary);
            font-size: 1.2rem;
            margin-bottom: 20px;
            text-align: center;
        }
        .control-panel {
            text-align: center;
            margin: 30px 0;
        }
        .demo-btn {
            background: var(--accent-blue);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        .demo-btn:hover {
            background: #00d4ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }
    </style>
</head>
<body>
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: var(--text-primary); font-size: 2rem; margin-bottom: 10px;">
            🚀 AI财务审核控制台 - 动态效果演示
        </h1>
        <p style="color: var(--text-secondary); font-size: 1.1rem;">
            体验AI带来的视觉冲击感和提速增效效果
        </p>
    </div>

    <div class="demo-container">
        <!-- AI分析引擎演示 -->
        <div class="demo-section ai-brain-section">
            <div class="demo-title">🧠 AI分析引擎</div>
            
            <!-- AI思维可视化区域 -->
            <div class="ai-thinking-container">
                <div class="thinking-display">
                    <div class="thinking-header">
                        <div class="thinking-icon">🤖</div>
                        <div class="thinking-title">AI正在思考...</div>
                    </div>
                    <div class="thinking-content" id="demo-thinking-content">
                        <div class="thinking-step">
                            <div class="step-indicator"></div>
                            <div class="step-text">正在解析输入的JSON数据结构...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 神经网络可视化 -->
                <div class="neural-network">
                    <div class="network-layer input-layer">
                        <div class="neuron active"></div>
                        <div class="neuron"></div>
                        <div class="neuron active"></div>
                    </div>
                    <div class="network-layer hidden-layer">
                        <div class="neuron"></div>
                        <div class="neuron active"></div>
                        <div class="neuron"></div>
                        <div class="neuron"></div>
                    </div>
                    <div class="network-layer output-layer">
                        <div class="neuron"></div>
                        <div class="neuron active"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 规则引擎执行演示 -->
        <div class="demo-section rules-execution-section">
            <div class="demo-title">⚡ 规则引擎执行</div>
            
            <div class="rules-layers">
                <!-- 确定性规则层 -->
                <div class="rule-layer active" id="demo-deterministic-layer">
                    <div class="layer-header">
                        <div class="layer-icon">🔍</div>
                        <div class="layer-info">
                            <h3>确定性规则引擎</h3>
                            <span class="layer-desc">数据验证 · 格式检查</span>
                        </div>
                        <div class="layer-status">执行中</div>
                    </div>
                    <div class="layer-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                        <span class="progress-text">9/12</span>
                    </div>
                </div>

                <!-- 关键词规则层 -->
                <div class="rule-layer" id="demo-keyword-layer">
                    <div class="layer-header">
                        <div class="layer-icon">🎯</div>
                        <div class="layer-info">
                            <h3>关键词规则引擎</h3>
                            <span class="layer-desc">敏感词检测 · 风险识别</span>
                        </div>
                        <div class="layer-status">待执行</div>
                    </div>
                    <div class="layer-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0/14</span>
                    </div>
                </div>

                <!-- 语义规则层 -->
                <div class="rule-layer" id="demo-semantic-layer">
                    <div class="layer-header">
                        <div class="layer-icon">🧠</div>
                        <div class="layer-info">
                            <h3>AI语义规则引擎</h3>
                            <span class="layer-desc">深度理解 · 智能推理</span>
                        </div>
                        <div class="layer-status">待执行</div>
                    </div>
                    <div class="layer-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0/12</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据流监控演示 -->
        <div class="demo-section">
            <div class="demo-title">📊 数据流监控</div>
            
            <!-- 审核统计监控 -->
            <div class="audit-statistics">
                <h3>📈 审核统计</h3>
                <div class="statistics-metrics">
                    <div class="metric">
                        <span class="metric-label">通过率</span>
                        <div class="metric-bar">
                            <div class="metric-fill success" id="demo-pass-rate-bar" style="width: 85%"></div>
                        </div>
                        <span class="metric-value" id="demo-pass-rate-value">85%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">风险等级</span>
                        <div class="metric-bar">
                            <div class="metric-fill warning" id="demo-risk-level-bar" style="width: 30%"></div>
                        </div>
                        <span class="metric-value" id="demo-risk-level-value">低风险</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">合规评分</span>
                        <div class="metric-bar">
                            <div class="metric-fill info" id="demo-compliance-score-bar" style="width: 92%"></div>
                        </div>
                        <span class="metric-value" id="demo-compliance-score-value">92分</span>
                    </div>
                </div>
            </div>

            <!-- 实时风险提醒 -->
            <div class="risk-alerts">
                <h3>⚠️ 风险提醒</h3>
                <div class="alerts-container" id="demo-risk-alerts-container">
                    <div class="alert-item warning">
                        <div class="alert-icon">⚠️</div>
                        <div class="alert-content">
                            <div class="alert-title">存在风险提醒</div>
                            <div class="alert-desc">发现 2 个潜在风险点</div>
                        </div>
                    </div>
                    <div class="alert-item success">
                        <div class="alert-icon">✅</div>
                        <div class="alert-content">
                            <div class="alert-title">大部分规则通过</div>
                            <div class="alert-desc">36 项规则检查已通过</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="control-panel">
        <button class="demo-btn" onclick="startAuditDemo()">🚀 开始审核演示</button>
        <button class="demo-btn" onclick="toggleNeuralNetwork()">🧠 切换神经网络</button>
        <button class="demo-btn" onclick="updateStatistics()">📊 更新统计数据</button>
        <button class="demo-btn" onclick="simulateRiskAlert()">⚠️ 模拟风险提醒</button>
    </div>

    <script>
        // 演示控制函数
        let demoInterval;
        let currentStep = 0;

        function startAuditDemo() {
            if (demoInterval) clearInterval(demoInterval);
            
            const steps = [
                () => activateLayer('demo-deterministic-layer', '确定性规则执行中...'),
                () => activateLayer('demo-keyword-layer', '关键词检测中...'),
                () => activateLayer('demo-semantic-layer', 'AI语义分析中...'),
                () => completeAllLayers()
            ];

            currentStep = 0;
            demoInterval = setInterval(() => {
                if (currentStep < steps.length) {
                    steps[currentStep]();
                    currentStep++;
                } else {
                    clearInterval(demoInterval);
                    setTimeout(() => resetDemo(), 2000);
                }
            }, 2000);
        }

        function activateLayer(layerId, message) {
            // 重置所有层
            document.querySelectorAll('.rule-layer').forEach(layer => {
                layer.classList.remove('active', 'completed');
            });
            
            // 激活当前层
            const layer = document.getElementById(layerId);
            if (layer) {
                layer.classList.add('active');
                layer.querySelector('.layer-status').textContent = '执行中';
                
                // 动画进度条
                const progressBar = layer.querySelector('.progress-fill');
                const progressText = layer.querySelector('.progress-text');
                animateProgress(progressBar, progressText, 100);
            }
            
            updateThinkingContent(message);
        }

        function completeAllLayers() {
            document.querySelectorAll('.rule-layer').forEach(layer => {
                layer.classList.remove('active');
                layer.classList.add('completed');
                layer.querySelector('.layer-status').textContent = '已完成';
            });
            updateThinkingContent('审核流程完成！');
        }

        function resetDemo() {
            document.querySelectorAll('.rule-layer').forEach(layer => {
                layer.classList.remove('active', 'completed');
                layer.querySelector('.layer-status').textContent = '待执行';
                layer.querySelector('.progress-fill').style.width = '0%';
                layer.querySelector('.progress-text').textContent = '0/12';
            });
            updateThinkingContent('系统就绪，等待开始审核');
        }

        function toggleNeuralNetwork() {
            const neurons = document.querySelectorAll('.neuron');
            neurons.forEach(neuron => neuron.classList.remove('active'));
            
            // 随机激活神经元
            const activeCount = Math.floor(Math.random() * 4) + 2;
            const shuffled = Array.from(neurons).sort(() => 0.5 - Math.random());
            
            for (let i = 0; i < activeCount && i < shuffled.length; i++) {
                shuffled[i].classList.add('active');
            }
        }

        function updateStatistics() {
            const scenarios = [
                { passRate: 100, riskLevel: '无风险', riskPercent: 10, complianceScore: 100 },
                { passRate: 85, riskLevel: '低风险', riskPercent: 30, complianceScore: 92 },
                { passRate: 70, riskLevel: '中风险', riskPercent: 60, complianceScore: 75 },
                { passRate: 50, riskLevel: '高风险', riskPercent: 80, complianceScore: 60 }
            ];
            
            const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
            
            animateProgressBar(document.getElementById('demo-pass-rate-bar'), scenario.passRate);
            animateNumber(document.getElementById('demo-pass-rate-value'), scenario.passRate, '%');
            
            animateProgressBar(document.getElementById('demo-risk-level-bar'), scenario.riskPercent);
            document.getElementById('demo-risk-level-value').textContent = scenario.riskLevel;
            
            animateProgressBar(document.getElementById('demo-compliance-score-bar'), scenario.complianceScore);
            animateNumber(document.getElementById('demo-compliance-score-value'), scenario.complianceScore, '分');
        }

        function simulateRiskAlert() {
            const alertTypes = [
                { type: 'error', icon: '🚨', title: '发现严重问题', desc: '检测到不合规项目，需要立即处理' },
                { type: 'warning', icon: '⚠️', title: '存在风险提醒', desc: '发现潜在风险点，建议人工复核' },
                { type: 'success', icon: '✅', title: '审核通过', desc: '所有规则检查均已通过' },
                { type: 'info', icon: 'ℹ️', title: '系统提示', desc: '审核进度更新' }
            ];
            
            const alert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
            const container = document.getElementById('demo-risk-alerts-container');
            
            container.innerHTML = `
                <div class="alert-item ${alert.type}">
                    <div class="alert-icon">${alert.icon}</div>
                    <div class="alert-content">
                        <div class="alert-title">${alert.title}</div>
                        <div class="alert-desc">${alert.desc}</div>
                    </div>
                </div>
            `;
        }

        // 辅助函数
        function animateProgress(progressBar, progressText, targetPercent) {
            let current = 0;
            const interval = setInterval(() => {
                current += 5;
                if (current >= targetPercent) {
                    current = targetPercent;
                    clearInterval(interval);
                }
                progressBar.style.width = `${current}%`;
                if (progressText) {
                    const total = parseInt(progressText.textContent.split('/')[1]) || 12;
                    const completed = Math.round((current / 100) * total);
                    progressText.textContent = `${completed}/${total}`;
                }
            }, 100);
        }

        function animateProgressBar(element, targetWidth) {
            if (!element) return;
            let current = 0;
            const interval = setInterval(() => {
                current += 2;
                if (current >= targetWidth) {
                    current = targetWidth;
                    clearInterval(interval);
                }
                element.style.width = `${current}%`;
            }, 50);
        }

        function animateNumber(element, targetValue, suffix = '') {
            if (!element) return;
            let current = 0;
            const interval = setInterval(() => {
                current += Math.ceil(targetValue / 20);
                if (current >= targetValue) {
                    current = targetValue;
                    clearInterval(interval);
                }
                element.textContent = `${current}${suffix}`;
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 100);
            }, 50);
        }

        function updateThinkingContent(message) {
            const content = document.getElementById('demo-thinking-content');
            if (content) {
                content.innerHTML = `
                    <div class="thinking-step">
                        <div class="step-indicator"></div>
                        <div class="step-text">${message}</div>
                    </div>
                `;
            }
        }

        // 自动演示
        setInterval(toggleNeuralNetwork, 3000);
        setInterval(updateStatistics, 8000);
        
        // 初始化
        toggleNeuralNetwork();
    </script>
</body>
</html>

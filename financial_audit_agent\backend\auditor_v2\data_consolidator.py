#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据整合器 - 整合表单和附件数据
将所有输入数据转换为适合LLM阅读的精简文本格式
"""

import json
from typing import Dict, Any, List


class DataConsolidator:
    """数据整合器:将JSON数据转换为LLM友好的文本格式"""
    
    def __init__(self, form_path: str, attachment_path: str):
        """
        初始化数据整合器
        
        Args:
            form_path: 表单JSON文件路径
            attachment_path: 附件JSON文件路径
        """
        self.form_path = form_path
        self.attachment_path = attachment_path
        self.form_data = {}
        self.attachment_data = {}
        
        self._load_data()
    
    def _load_data(self):
        """加载JSON数据文件"""
        try:
            with open(self.form_path, 'r', encoding='utf-8') as f:
                self.form_data = json.load(f)
        except Exception as e:
            print(f"警告:无法加载表单数据 {self.form_path}: {e}")
            self.form_data = {}
            
        try:
            with open(self.attachment_path, 'r', encoding='utf-8') as f:
                self.attachment_data = json.load(f)
        except Exception as e:
            print(f"警告:无法加载附件数据 {self.attachment_path}: {e}")
            self.attachment_data = {}
    
    def get_consolidated_text(self) -> str:
        """
        将所有数据整合成一个适合LLM阅读的, 精炼的文本块
        
        Returns:
            str: 整合后的文本数据
        """
        text_parts = ["### 单据及附件信息汇总"]
        
        # 1. 主表单信息
        text_parts.append("\n**主报销单信息:**")
        form_main = {k: v for k, v in self.form_data.items() if not k.startswith('付款明细_')}
        text_parts.append(self._format_dict(form_main))
        
        # 2. 附件摘要
        text_parts.append("\n**附件概览:**")
        if 'processing_summary' in self.attachment_data:
            summary = self.attachment_data['processing_summary']
            text_parts.append(f"- 附件总数: {summary.get('total_files', '未知')}")
            text_parts.append(f"- 处理成功: {summary.get('successful_files', '未知')}")
            text_parts.append(f"- 处理失败: {summary.get('failed_files', '未知')}")

        # 获取文档类型列表
        doc_types = []
        if 'extracted_documents' in self.attachment_data:
            doc_types = [doc.get('document_type', '未知类型')
                        for doc in self.attachment_data['extracted_documents']]
        text_parts.append(f"- 附件类型: {', '.join(doc_types) if doc_types else '无'}")
        
        # 3. 各附件提取的关键信息
        text_parts.append("\n**各附件关键信息:**")
        if 'extracted_documents' in self.attachment_data:
            for doc in self.attachment_data['extracted_documents']:
                doc_type = doc.get('document_type', '未知类型')
                text_parts.append(f"\n*{doc_type}*")

                extraction_data = doc.get('extraction_data', {})
                if extraction_data:
                    # 清理空值和无效数据
                    clean_data = {k: v for k, v in extraction_data.items()
                                if v is not None and v != "" and v != []}
                    if clean_data:
                        text_parts.append(self._format_dict(clean_data))
                    else:
                        text_parts.append("- 无有效提取信息")
                else:
                    text_parts.append("- 无提取数据")
        else:
            text_parts.append("- 无附件提取信息")
        
        return "\n".join(text_parts)
    
    def _format_dict(self, data_dict: Dict[str, Any]) -> str:
        """
        格式化字典数据为可读文本
        
        Args:
            data_dict: 要格式化的字典
            
        Returns:
            str: 格式化后的文本
        """
        if not data_dict:
            return "- 无数据"
            
        lines = []
        for key, value in data_dict.items():
            # 清理键名
            clean_key = key.replace("表头_", "").replace("报销明细_*", "").replace("报销明细_", "")
            
            # 处理不同类型的值
            if isinstance(value, list):
                if value:  # 非空列表
                    val_str = ', '.join(str(item) for item in value)
                else:
                    val_str = "无"
            elif isinstance(value, dict):
                # 对于嵌套字典,简化显示
                val_str = f"包含{len(value)}个字段"
            else:
                val_str = str(value) if value is not None else "无"
            
            lines.append(f"- {clean_key}: {val_str}")
        
        return "\n".join(lines)
    
    def get_specific_field(self, field_path: str) -> Any:
        """
        获取特定字段的值,支持嵌套路径
        
        Args:
            field_path: 字段路径,如 "业务招待事前审批表.招待日期"
            
        Returns:
            Any: 字段值,如果不存在返回None
        """
        parts = field_path.split('.')
        
        # 先在表单数据中查找
        current = self.form_data
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                break
        else:
            return current
        
        # 再在附件数据中查找
        current = self.attachment_data
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None
        
        return current
    
    def get_attachment_list(self) -> List[str]:
        """
        获取附件列表

        Returns:
            List[str]: 附件文件名列表
        """
        if 'extracted_documents' in self.attachment_data:
            return [doc.get('document_type', '未知附件')
                   for doc in self.attachment_data['extracted_documents']]
        return []

    def get_document_number(self) -> str:
        """
        提取单据编号,用于生成动态文件名

        Returns:
            str: 单据编号,如果未找到返回默认值
        """
        # 首先尝试从表单数据中获取"表头_单据编号"字段
        document_number = self.get_specific_field("表头_单据编号")

        if document_number:
            # 如果是列表,取第一个元素
            if isinstance(document_number, list) and document_number:
                return str(document_number[0])
            # 如果是字符串,直接返回
            elif isinstance(document_number, str):
                return document_number

        # 尝试其他可能的字段名
        alternative_fields = [
            "单据编号",
            "编号",
            "表头_编号",
            "报销单编号",
            "主报销单信息.单据编号"
        ]

        for field in alternative_fields:
            value = self.get_specific_field(field)
            if value:
                if isinstance(value, list) and value:
                    return str(value[0])
                elif isinstance(value, str):
                    return value

        # 如果都没找到,尝试从文件名中提取
        import os
        form_filename = os.path.basename(self.form_path)
        # 匹配类似 "ZDBXD2025042900003_表单提取.json" 的模式
        import re
        match = re.search(r'([A-Z0-9]+)_表单提取\.json', form_filename)
        if match:
            return match.group(1)

        # 最后的默认值
        return "default"


if __name__ == "__main__":
    # 测试代码
    consolidator = DataConsolidator(
        "../../ZDBXD2025051300001_表单提取.json",
        "../../ZDBXD2025051300001_附件提取.json"
    )
    print(consolidator.get_consolidated_text())

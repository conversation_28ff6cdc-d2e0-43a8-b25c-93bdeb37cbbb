#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON解析问题调试脚本
深入分析LLM响应格式与解析逻辑的匹配性
"""

import os
import sys
import json
import re
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "backend" / "auditor_v2"))

def analyze_llm_response_format():
    """分析LLM响应的实际格式"""
    print("🔍 分析LLM响应格式")
    print("=" * 80)
    
    # 模拟实际的LLM响应（基于之前看到的思维链内容）
    sample_response = """首先，我需要理解任务：我是一名财务审核助手，负责在【审核阶段:附件完整性检查】中，根据提供的【本阶段审核规则】审核【单据数据】。输出必须包括两部分：第一部分是详细思考过程，第二部分是审核结果JSON。

审核规则有5条：
- 规则1：检查是否上传发票。指令：检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"发票"。
- 规则2：检查是否上传事前审批表。指令：检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"业务招待事前审批表"。

**第一部分：详细思考过程**
1. 对于规则1：检查是否上传发票。
   - 解析规则：指令要求检查附件概览中的附件类型列表是否包含"发票"。
   - 查找数据：在单据数据中，定位到"附件概览 -> 附件类型"，值为"业务招待事前审批表, 发票, 餐饮小票, 支付记录"。
   - 逻辑判断：列表中包含"发票"字符串，因此满足要求。
   - 结论：status应为"通过"，因为数据存在且符合。

**第二部分：审核结果**
[
  {
    "rule_id": "规则1:检查是否上传发票.",
    "status": "通过",
    "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'发票', 满足要求."
  },
  {
    "rule_id": "规则2:检查是否上传事前审批表.",
    "status": "通过", 
    "reason": "根据 [来源: 附件概览 -> 附件类型], 发现列表中包含'业务招待事前审批表', 满足要求."
  }
]"""

    print("📋 样本LLM响应:")
    print("-" * 40)
    print(sample_response[:500] + "..." if len(sample_response) > 500 else sample_response)
    print("-" * 40)
    
    return sample_response

def test_current_parsing_logic(response):
    """测试当前的解析逻辑"""
    print("\n🧪 测试当前解析逻辑")
    print("=" * 80)
    
    try:
        from orchestrator_v2 import OrchestratorV2
        
        # 创建测试实例
        orchestrator = OrchestratorV2(None, None, "DEBUG_JSON_001")
        
        print("1️⃣ 测试 _clean_llm_response 方法:")
        cleaned = orchestrator._clean_llm_response(response)
        print(f"   清理后长度: {len(cleaned)}")
        print(f"   清理后内容前100字符: {cleaned[:100] if cleaned else '(空)'}")
        print(f"   清理后内容后100字符: {cleaned[-100:] if cleaned else '(空)'}")
        
        print("\n2️⃣ 测试 _extract_json_from_response 方法:")
        extracted = orchestrator._extract_json_from_response(response)
        print(f"   提取后长度: {len(extracted)}")
        print(f"   提取后内容: {extracted[:200] if extracted else '(空)'}")
        
        print("\n3️⃣ 测试JSON解析:")
        if extracted:
            try:
                parsed = json.loads(extracted)
                print(f"   ✅ JSON解析成功!")
                print(f"   解析结果类型: {type(parsed)}")
                if isinstance(parsed, list):
                    print(f"   数组长度: {len(parsed)}")
                    for i, item in enumerate(parsed):
                        print(f"   项目{i+1}: {item}")
                else:
                    print(f"   解析结果: {parsed}")
                return True, parsed
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                return False, None
        else:
            print("   ❌ 没有提取到JSON内容")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def analyze_json_extraction_methods(response):
    """分析不同的JSON提取方法"""
    print("\n🔧 分析不同的JSON提取方法")
    print("=" * 80)
    
    methods = []
    
    # 方法1: 查找"第二部分：审核结果"
    print("1️⃣ 方法1: 查找'第二部分：审核结果'")
    if "第二部分：审核结果" in response:
        start_pos = response.find("第二部分：审核结果")
        after_marker = response[start_pos:]
        print(f"   找到标记，后续内容长度: {len(after_marker)}")
        
        # 在标记后查找JSON数组
        bracket_start = after_marker.find('[')
        if bracket_start != -1:
            # 找到匹配的结束括号
            bracket_count = 0
            end_pos = bracket_start
            for i, char in enumerate(after_marker[bracket_start:], bracket_start):
                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                    if bracket_count == 0:
                        end_pos = i
                        break
            
            if bracket_count == 0:
                json_str = after_marker[bracket_start:end_pos + 1]
                methods.append(("方法1", json_str))
                print(f"   ✅ 提取成功，长度: {len(json_str)}")
                print(f"   内容预览: {json_str[:100]}...")
            else:
                print(f"   ❌ 括号不匹配")
        else:
            print(f"   ❌ 未找到JSON数组开始标记")
    else:
        print(f"   ❌ 未找到'第二部分：审核结果'标记")
    
    # 方法2: 查找最后一个完整的JSON数组
    print("\n2️⃣ 方法2: 查找最后一个完整的JSON数组")
    start_bracket = response.rfind('[')
    if start_bracket != -1:
        print(f"   找到最后一个'['在位置: {start_bracket}")
        
        # 从最后一个[开始，向后查找匹配的]
        bracket_count = 0
        end_pos = len(response) - 1
        
        for i in range(start_bracket, len(response)):
            char = response[i]
            if char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
                if bracket_count == 0:
                    end_pos = i
                    break
        
        if bracket_count == 0:
            json_str = response[start_bracket:end_pos + 1]
            methods.append(("方法2", json_str))
            print(f"   ✅ 提取成功，长度: {len(json_str)}")
            print(f"   内容预览: {json_str[:100]}...")
        else:
            print(f"   ❌ 括号不匹配")
    else:
        print(f"   ❌ 未找到JSON数组开始标记")
    
    # 方法3: 正则表达式
    print("\n3️⃣ 方法3: 正则表达式匹配")
    json_pattern = r'\[\s*\{.*?\}\s*\]'
    matches = re.findall(json_pattern, response, re.DOTALL)
    if matches:
        json_str = matches[-1]  # 取最后一个匹配
        methods.append(("方法3", json_str))
        print(f"   ✅ 找到 {len(matches)} 个匹配，使用最后一个")
        print(f"   长度: {len(json_str)}")
        print(f"   内容预览: {json_str[:100]}...")
    else:
        print(f"   ❌ 正则表达式未匹配到内容")
    
    # 测试所有方法的JSON解析
    print("\n📊 测试所有方法的JSON解析:")
    successful_methods = []
    
    for method_name, json_str in methods:
        try:
            parsed = json.loads(json_str)
            successful_methods.append((method_name, parsed))
            print(f"   ✅ {method_name}: 解析成功，{len(parsed) if isinstance(parsed, list) else 1} 个项目")
        except json.JSONDecodeError as e:
            print(f"   ❌ {method_name}: 解析失败 - {e}")
    
    return successful_methods

def create_improved_extraction_method():
    """创建改进的JSON提取方法"""
    print("\n🚀 创建改进的JSON提取方法")
    print("=" * 80)
    
    improved_code = '''
def _extract_json_from_response_improved(self, response: str) -> str:
    """
    改进的JSON提取方法
    
    Args:
        response: 完整的LLM响应
        
    Returns:
        str: 提取的JSON字符串
    """
    if not response:
        return ""
    
    # 方法1: 查找"第二部分：审核结果"后的JSON
    if "第二部分：审核结果" in response:
        start_pos = response.find("第二部分：审核结果")
        after_marker = response[start_pos:]
        
        # 在标记后查找JSON数组
        bracket_start = after_marker.find('[')
        if bracket_start != -1:
            # 找到匹配的结束括号
            bracket_count = 0
            end_pos = bracket_start
            for i, char in enumerate(after_marker[bracket_start:], bracket_start):
                if char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                    if bracket_count == 0:
                        end_pos = i
                        break
            
            if bracket_count == 0:
                json_str = after_marker[bracket_start:end_pos + 1]
                # 验证JSON格式
                try:
                    json.loads(json_str)
                    return json_str
                except json.JSONDecodeError:
                    pass
    
    # 方法2: 查找最后一个完整的JSON数组
    start_bracket = response.rfind('[')
    if start_bracket != -1:
        bracket_count = 0
        end_pos = len(response) - 1
        
        for i in range(start_bracket, len(response)):
            char = response[i]
            if char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
                if bracket_count == 0:
                    end_pos = i
                    break
        
        if bracket_count == 0:
            json_str = response[start_bracket:end_pos + 1]
            # 验证JSON格式
            try:
                json.loads(json_str)
                return json_str
            except json.JSONDecodeError:
                pass
    
    # 方法3: 使用正则表达式查找JSON数组
    import re
    json_pattern = r'\\[\\s*\\{[^\\[\\]]*\\}(?:\\s*,\\s*\\{[^\\[\\]]*\\})*\\s*\\]'
    matches = re.findall(json_pattern, response, re.DOTALL)
    
    for match in reversed(matches):  # 从后往前尝试
        try:
            json.loads(match)
            return match
        except json.JSONDecodeError:
            continue
    
    return ""
'''
    
    print("改进的提取方法代码:")
    print(improved_code)
    
    return improved_code

def main():
    """主函数"""
    print("🔍 JSON解析问题深度调试")
    print("=" * 100)
    
    # 切换到项目目录
    os.chdir(project_root)
    
    # 1. 分析LLM响应格式
    sample_response = analyze_llm_response_format()
    
    # 2. 测试当前解析逻辑
    success, parsed_result = test_current_parsing_logic(sample_response)
    
    # 3. 分析不同的JSON提取方法
    successful_methods = analyze_json_extraction_methods(sample_response)
    
    # 4. 创建改进的提取方法
    improved_code = create_improved_extraction_method()
    
    # 5. 总结分析结果
    print("\n📊 分析结果总结")
    print("=" * 100)
    
    print(f"当前解析逻辑: {'✅ 成功' if success else '❌ 失败'}")
    print(f"成功的提取方法数量: {len(successful_methods)}")
    
    if successful_methods:
        print("成功的方法:")
        for method_name, result in successful_methods:
            print(f"  - {method_name}: {len(result) if isinstance(result, list) else 1} 个项目")
    
    # 6. 提供修复建议
    print("\n🔧 修复建议")
    print("=" * 100)
    
    if not success and successful_methods:
        print("1. 当前的JSON提取逻辑存在问题")
        print("2. 建议使用改进的提取方法")
        print("3. 需要更新 _extract_json_from_response 方法")
        print("4. 添加更多的错误处理和验证")
    elif success:
        print("1. 当前解析逻辑在样本数据上工作正常")
        print("2. 问题可能出现在实际LLM响应格式上")
        print("3. 建议添加更详细的调试日志")
        print("4. 需要捕获实际的LLM响应进行分析")
    else:
        print("1. 所有提取方法都失败了")
        print("2. 可能是样本数据格式问题")
        print("3. 需要获取真实的LLM响应数据")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

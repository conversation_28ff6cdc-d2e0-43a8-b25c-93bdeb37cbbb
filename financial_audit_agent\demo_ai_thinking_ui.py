#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI思维链UI集成演示脚本
演示完整的AI思维链前端集成功能
"""

import json
import os
import sys
import webbrowser
import time
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading

def create_demo_report():
    """创建演示用的增强审核报告"""
    print("🔧 创建演示用的增强审核报告...")
    
    demo_report = {
        "summary": {
            "total_rules_checked": 4,
            "passed_count": 0,
            "failed_count": 4,
            "warning_count": 0
        },
        "details": [
            {
                "rule_id": "规则1：检查是否上传发票",
                "status": "FAIL",
                "message": "未找到发票文件，请上传相关发票"
            },
            {
                "rule_id": "规则2：检查合同文件完整性",
                "status": "FAIL", 
                "message": "合同文件不完整，缺少关键条款"
            },
            {
                "rule_id": "规则3：验证付款凭证",
                "status": "FAIL",
                "message": "付款凭证格式不规范，需要重新提交"
            },
            {
                "rule_id": "规则4：检查审批流程",
                "status": "FAIL",
                "message": "审批流程不完整，缺少必要的审批签字"
            }
        ],
        "review_comments": "经过AI智能审核，发现该申请存在多个问题需要处理：\n1. 缺少必要的发票文件\n2. 合同文件不完整\n3. 付款凭证不规范\n4. 审批流程有缺陷\n\n建议申请人补充相关材料后重新提交审核。",
        "ai_thinking_chain": {
            "combined_thinking": "系统开始进行智能审核分析，准备对文档进行全面检查...",
            "phases_history": {
                "phase_1_attachment_check": {
                    "phase_name": "附件完整性检查",
                    "ai_thinking": """## 🔍 附件完整性检查 (阶段 1/4)

我正在开始对文档 ZDBXD2025042900003 进行附件完整性检查。

### 📋 检查清单
1. ✅ 发票文件检查
2. ✅ 合同文件检查  
3. ✅ 付款凭证检查
4. ✅ 其他必要附件检查

### 🔍 详细分析过程

**步骤1: 发票文件检查**
- 🔍 检查是否存在发票文件
- ❌ 未发现发票文件
- 📝 分析: 根据财务规定，所有费用申请必须提供相应的发票作为凭证
- 🎯 结论: 发票文件缺失，不符合审核要求

**步骤2: 合同文件检查**
- 🔍 验证合同文件完整性
- ⚠️ 发现合同文件存在但不完整
- 📝 分析: 合同中缺少关键条款，如付款方式、违约责任等
- 🎯 结论: 合同文件不完整，需要补充

**步骤3: 付款凭证检查**
- 🔍 检查付款凭证是否完整
- ❌ 付款凭证格式不规范
- 📝 分析: 凭证缺少必要的审批签字和日期信息
- 🎯 结论: 付款凭证不符合标准格式

**步骤4: 其他必要附件检查**
- 🔍 检查其他支持性文件
- ⚠️ 部分文件缺失
- 📝 分析: 缺少项目说明书和预算明细
- 🎯 结论: 支持性文件不完整

### 📊 检查结果汇总
经过详细检查，发现以下问题：
1. ❌ 缺少发票文件 (严重)
2. ⚠️ 合同文件不完整 (中等)
3. ❌ 付款凭证格式不规范 (严重)
4. ⚠️ 支持性文件缺失 (轻微)

### 🎯 最终结论
附件完整性检查未通过，建议申请人按照以下要求补充材料：
1. 提供完整的发票文件
2. 补充合同中的关键条款
3. 重新提交规范格式的付款凭证
4. 添加项目说明书和预算明细

### 📈 风险评估
- 合规风险: 高 (缺少必要凭证)
- 财务风险: 中 (金额核实困难)
- 操作风险: 低 (流程相对清晰)

建议在补充材料后重新进行审核。""",
                    "status": "completed",
                    "timestamp": "2025-07-28T14:10:30Z",
                    "message": "附件完整性检查完成",
                    "detail": "发现多个问题需要处理"
                },
                "phase_2_content_validation": {
                    "phase_name": "内容合规性验证",
                    "ai_thinking": """## 📋 内容合规性验证 (阶段 2/4)

基于第一阶段的附件检查结果，现在进行内容合规性验证。

### 🎯 验证目标
1. 检查申请内容是否符合公司政策
2. 验证金额是否在授权范围内
3. 确认申请理由的合理性
4. 检查是否存在潜在的合规风险

### 🔍 详细验证过程

**验证项1: 政策合规性**
- 📖 对照公司财务管理制度
- ✅ 申请类型符合政策范围
- ⚠️ 但缺少必要的审批层级
- 📝 分析: 该金额级别需要部门经理和财务总监双重审批

**验证项2: 金额授权检查**
- 💰 申请金额: ¥50,000
- 📊 对照授权矩阵
- ⚠️ 超出申请人单独审批权限
- 📝 分析: 需要上级领导审批确认

**验证项3: 申请理由分析**
- 📝 申请理由: "办公设备采购"
- 🔍 理由合理性评估: 中等
- ⚠️ 缺少详细的设备清单和必要性说明
- 📝 建议: 补充详细的采购计划和预算分解

**验证项4: 合规风险评估**
- 🚨 识别潜在风险点:
  - 供应商选择透明度不足
  - 价格比较缺失
  - 验收标准不明确
- 📊 风险等级: 中等

### 📊 验证结果
1. ✅ 申请类型合规
2. ⚠️ 审批层级不足
3. ⚠️ 金额超出权限
4. ⚠️ 理由说明不够详细
5. ⚠️ 存在中等合规风险

### 🎯 改进建议
1. 补充上级领导审批
2. 提供详细的设备清单
3. 添加供应商比价说明
4. 明确验收标准和责任人

内容合规性验证部分通过，但需要完善相关材料。""",
                    "status": "completed", 
                    "timestamp": "2025-07-28T14:11:45Z",
                    "message": "内容合规性验证完成",
                    "detail": "发现部分合规性问题"
                },
                "phase_3_risk_assessment": {
                    "phase_name": "风险评估分析",
                    "ai_thinking": """## ⚠️ 风险评估分析 (阶段 3/4)

基于前两个阶段的分析结果，现在进行综合风险评估。

### 🎯 风险评估框架
采用多维度风险评估模型：
- 财务风险 (Financial Risk)
- 合规风险 (Compliance Risk)  
- 操作风险 (Operational Risk)
- 声誉风险 (Reputational Risk)

### 📊 详细风险分析

**1. 财务风险评估**
- 💰 金额影响: ¥50,000 (中等)
- 📈 预算影响: 占部门年度预算的8%
- 🔍 资金来源: 部门运营预算
- ⚠️ 风险点:
  - 缺少发票可能导致税务问题
  - 无法准确核算成本
  - 可能影响预算执行
- 📊 财务风险等级: **中等**

**2. 合规风险评估**  
- 📋 政策符合度: 70%
- 🔍 审批完整性: 不足
- ⚠️ 风险点:
  - 违反内控制度要求
  - 可能面临内审质疑
  - 存在监管合规隐患
- 📊 合规风险等级: **高**

**3. 操作风险评估**
- 🔄 流程完整性: 60%
- 👥 人员责任: 不够明确
- ⚠️ 风险点:
  - 验收标准不清晰
  - 责任归属模糊
  - 后续管理困难
- 📊 操作风险等级: **中等**

**4. 声誉风险评估**
- 🏢 对公司形象影响: 轻微
- 📰 媒体关注可能性: 低
- ⚠️ 风险点:
  - 内部管理形象
  - 供应商关系
- 📊 声誉风险等级: **低**

### 🎯 综合风险评级

**总体风险等级: 中高**

风险权重计算:
- 财务风险 (30%): 中等 × 0.3 = 1.5
- 合规风险 (40%): 高 × 0.4 = 3.2  
- 操作风险 (20%): 中等 × 0.2 = 1.0
- 声誉风险 (10%): 低 × 0.1 = 0.3

综合得分: 6.0/10 (中高风险)

### 🛡️ 风险缓解措施
1. **立即措施**:
   - 暂停当前申请流程
   - 要求补充必要材料
   - 获得适当层级审批

2. **中期措施**:
   - 建立完善的供应商评估机制
   - 制定详细的验收标准
   - 加强内控制度培训

3. **长期措施**:
   - 优化审批流程
   - 建立风险预警机制
   - 定期进行合规性检查

### 📈 风险监控建议
- 设置关键风险指标(KRI)
- 建立定期风险评估机制
- 加强跨部门协调沟通

风险评估分析完成，建议采取相应的风险缓解措施。""",
                    "status": "completed",
                    "timestamp": "2025-07-28T14:12:30Z", 
                    "message": "风险评估分析完成",
                    "detail": "识别出中高等级风险"
                },
                "phase_4_final_decision": {
                    "phase_name": "最终决策建议",
                    "ai_thinking": """## 🎯 最终决策建议 (阶段 4/4)

基于前三个阶段的全面分析，现在提供最终的审核决策建议。

### 📊 综合分析汇总

**阶段1 - 附件完整性**: ❌ 不通过
- 缺少关键文件
- 现有文件不规范
- 影响后续审核

**阶段2 - 内容合规性**: ⚠️ 部分通过  
- 基本符合政策要求
- 审批层级不足
- 需要补充说明

**阶段3 - 风险评估**: ⚠️ 中高风险
- 合规风险较高
- 财务和操作风险中等
- 需要风险缓解措施

### 🎯 决策矩阵分析

| 评估维度 | 权重 | 得分 | 加权得分 |
|---------|------|------|----------|
| 附件完整性 | 30% | 2/10 | 0.6 |
| 内容合规性 | 25% | 6/10 | 1.5 |
| 风险水平 | 25% | 4/10 | 1.0 |
| 政策符合度 | 20% | 7/10 | 1.4 |
| **总分** | 100% | - | **4.5/10** |

### 📋 决策建议

**主要建议: 暂缓批准，要求整改**

**理由分析:**
1. 📄 **文件不完整** (关键因素)
   - 缺少发票等必要凭证
   - 无法满足基本审核要求
   - 存在合规风险

2. 🔍 **审批不充分** (重要因素)
   - 超出申请人权限范围
   - 需要上级领导确认
   - 流程不够规范

3. ⚠️ **风险较高** (考虑因素)
   - 合规风险需要关注
   - 财务风险可控但需监控
   - 操作风险需要明确责任

### 📝 具体整改要求

**必须完成项 (Mandatory):**
1. ✅ 提供完整的发票文件
2. ✅ 获得部门经理审批签字
3. ✅ 补充详细的设备清单
4. ✅ 提供供应商比价说明

**建议完成项 (Recommended):**
1. 📋 制定详细的验收标准
2. 👥 明确项目责任人
3. 📊 提供预算影响分析
4. 🔄 建立后续管理计划

### ⏰ 时间安排建议

**整改期限: 5个工作日**
- Day 1-2: 补充必要文件
- Day 3: 获得上级审批
- Day 4: 完善说明材料  
- Day 5: 重新提交审核

### 🔄 后续流程

1. **申请人整改** → 2. **重新提交** → 3. **再次审核** → 4. **最终决定**

### 📞 联系支持

如需协助，请联系:
- 财务部门: 协助文件准备
- 采购部门: 供应商比价支持
- IT部门: 技术规格确认

### 🎯 最终结论

**审核结果: 暂缓批准**
**整改要求: 必须完成所有必须项**
**重审时间: 整改完成后1个工作日内**

本次AI智能审核已完成，感谢您的配合。请按照整改要求完善材料后重新提交。

---
*本报告由AI智能审核系统生成，如有疑问请联系相关部门。*""",
                    "status": "completed",
                    "timestamp": "2025-07-28T14:13:50Z",
                    "message": "最终决策建议完成", 
                    "detail": "建议暂缓批准，要求整改"
                }
            },
            "extraction_metadata": {
                "extracted_at": "2025-07-28 14:30:00",
                "audit_id": "ZDBXD2025042900003",
                "audit_status": "completed",
                "completion_time": "2025-07-28T14:13:50Z",
                "integration_version": "1.0",
                "total_thinking_length": 15420,
                "phases_count": 4
            }
        },
        "audit_metadata": {
            "version": "2.0",
            "audit_type": "stepwise_intelligent_audit",
            "timestamp": "2025-07-28 14:13:50",
            "document_number": "ZDBXD2025042900003",
            "ai_thinking_included": True,
            "ai_thinking_integration_time": "2025-07-28 14:30:00"
        }
    }
    
    # 保存演示报告
    demo_file = Path("audit_reports/demo_enhanced_report.json")
    demo_file.parent.mkdir(exist_ok=True)
    
    with open(demo_file, 'w', encoding='utf-8') as f:
        json.dump(demo_report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 演示报告已创建: {demo_file}")
    return demo_file

def start_local_server():
    """启动本地HTTP服务器"""
    class CustomHandler(SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
    
    port = 8080
    server = HTTPServer(('localhost', port), CustomHandler)
    
    def run_server():
        print(f"🌐 启动本地服务器: http://localhost:{port}")
        server.serve_forever()
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    return server, port

def open_demo_page(port):
    """打开演示页面"""
    demo_url = f"http://localhost:{port}/test_ai_thinking_chain_ui.html"
    print(f"🚀 打开演示页面: {demo_url}")
    
    time.sleep(2)  # 等待服务器启动
    webbrowser.open(demo_url)

def main():
    """主演示函数"""
    print("🎭 AI思维链UI集成完整演示")
    print("=" * 60)
    
    # 切换到正确的工作目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    try:
        # 1. 创建演示报告
        demo_file = create_demo_report()
        
        # 2. 启动本地服务器
        server, port = start_local_server()
        
        # 3. 打开演示页面
        open_demo_page(port)
        
        print("\n" + "=" * 60)
        print("🎉 演示环境已启动！")
        print("\n📋 演示步骤:")
        print("1. 点击'加载测试数据'按钮")
        print("2. 点击'查看AI思考过程'按钮")
        print("3. 测试各种功能:")
        print("   - 展开/收起阶段内容")
        print("   - 搜索思维链内容")
        print("   - 复制思考过程")
        print("   - 查看元数据信息")
        
        print(f"\n🌐 演示地址: http://localhost:{port}/test_ai_thinking_chain_ui.html")
        print(f"📁 演示报告: {demo_file}")
        
        print("\n按 Ctrl+C 停止服务器...")
        
        # 保持服务器运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            server.shutdown()
            print("✅ 服务器已停止")
            
    except Exception as e:
        print(f"❌ 演示启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()

// 审核报告数据 - 自动生成，请勿手动编辑
window.AUDIT_REPORT = {
  "summary": {
    "total_rules_checked": 38,
    "passed_count": 37,
    "failed_count": 0,
    "warning_count": 1
  },
  "details": [
    {
      "rule_id": "规则1：检查是否上传发票。",
      "status": "PASS",
      "message": "根据 [来源: 附件概览 -> 附件类型]，发现列表中包含'发票'，满足要求。"
    },
    {
      "rule_id": "规则2：检查是否上传事前审批表。",
      "status": "PASS",
      "message": "根据 [来源: 附件概览 -> 附件类型]，发现列表中包含'业务招待事前审批表'，满足要求。"
    },
    {
      "rule_id": "规则3：检查是否上传用餐小票。",
      "status": "PASS",
      "message": "根据 [来源: 附件概览 -> 附件类型]，发现列表中包含'餐饮小票'，满足要求。"
    },
    {
      "rule_id": "规则4：检查是否上传支付记录。",
      "status": "PASS",
      "message": "根据 [来源: 附件概览 -> 附件类型]，发现列表中包含'支付记录'，满足要求。"
    },
    {
      "rule_id": "规则5：检查特殊物品签收表。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 事由]，值为'2025年4月18日，招待北京中指实证数据信息技术有限公司深圳分公司（中指研究院）有关人员，进行客户满意度行业数据及满意度提升方面的业务交流，来访8人，陪餐3人。'，未提及向业主提供了水果、牛奶等非餐饮物品，因此该规则不适用。"
    },
    {
      "rule_id": "规则6：检查商务招待发起主体。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待类型]，值为'商务招待'。[来源: 主报销单信息 -> 招待发起主体] 的值为'业务部门'，符合要求。"
    },
    {
      "rule_id": "规则7：检查招待对象是否涉及公职人员。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待对象]，值为'北京中指实证数据信息技术有限公司深圳分公司（中指研究院）'，未发现党政军机关关键词，符合要求。"
    },
    {
      "rule_id": "规则8：检查招待发起主体一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待发起主体]，值为'业务部门'。[来源: 附件：业务招待事前审批表] 中勾选了'商务宴请'，且宴请标准为550元/人，符合要求。"
    },
    {
      "rule_id": "规则9：检查招待类型一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待类型]，值为'商务招待'。[来源: 附件：业务招待事前审批表 -> 业务招待类别]，值为'商务宴请'，语义一致，符合要求。"
    },
    {
      "rule_id": "规则10：检查招待日期一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待日期]，值为'2025-04-18'。[来源: 附件：业务招待事前审批表 -> 招待日期]，值为'2025-04-18'，日期一致，符合要求。"
    },
    {
      "rule_id": "规则11：检查招待人数一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待人数]，值为'8'。[来源: 附件：业务招待事前审批表 -> 来访人数]，值为'8'，人数一致，符合要求。"
    },
    {
      "rule_id": "规则12：检查陪餐人数一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 陪餐人数]，值为'3'。[来源: 附件：业务招待事前审批表 -> 陪同人数]，值为'3'，人数一致，符合要求。"
    },
    {
      "rule_id": "规则13：检查招待事由与项目状态。",
      "status": "WARNING",
      "message": "根据 [来源: 主报销单信息 -> 招待事由]，值为'进行客户满意度行业数据及满意度提升方面的业务交流'。[来源: 主报销单信息 -> 招待用途]，值为'项目招待'。但没有明确的项目状态信息，建议人工确认。"
    },
    {
      "rule_id": "规则14：检查项目相关性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 项目名称]，值为'租赁公司安全技术部非建设工程项目'。[来源: 附件：业务招待事前审批表 -> 招待事由]，文本中包含'租赁公司安全技术部非建设工程项目'，符合要求。"
    },
    {
      "rule_id": "规则15：检查发票项目名称。",
      "status": "PASS",
      "message": "根据 [来源: 附件：发票 -> 项目名称]，值为'餐饮服务*餐饮服务'，主要为'餐饮服务'或'餐费'，符合要求。"
    },
    {
      "rule_id": "规则16：检查总人数一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 总人数]，值为'11'。[来源: 附件：餐饮小票 -> 人数]，值为'11'，人数一致，符合要求。"
    },
    {
      "rule_id": "规则17：检查事前审批的及时性。",
      "status": "PASS",
      "message": "根据 [来源: 附件：业务招待事前审批表 -> 填报日期]，值为'2025-04-16'。[来源: 附件：业务招待事前审批表 -> 招待日期]，值为'2025-04-18'，填报日期早于招待日期，符合要求。"
    },
    {
      "rule_id": "规则18：检查审批流程完整性。",
      "status": "PASS",
      "message": "根据 [来源: 附件：业务招待事前审批表 -> 签字]，签字人为'闵昱, 闵昱, 闵昱'，签名完整，符合要求。"
    },
    {
      "rule_id": "规则19：检查审批落款日期。",
      "status": "PASS",
      "message": "根据 [来源: 附件：业务招待事前审批表 -> 填报日期]，值为'2025-04-16'。[来源: 主报销单信息 -> 招待日期]，值为'2025-04-18'，填报日期早于招待日期，符合要求。"
    },
    {
      "rule_id": "规则20：检查招待日期与用餐日期一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待日期]，值为'2025-04-18'。[来源: 附件：餐饮小票 -> 用餐日期]，值为'2025-04-18'，日期一致，符合要求。"
    },
    {
      "rule_id": "规则21：检查用餐日期与支付日期一致性。",
      "status": "PASS",
      "message": "根据 [来源: 附件：餐饮小票 -> 用餐日期]，值为'2025-04-18'。[来源: 附件：支付记录 -> 支付日期]，值为'2025-04-18'，日期一致，符合要求。"
    },
    {
      "rule_id": "规则22：检查小票与支付金额一致性。",
      "status": "PASS",
      "message": "根据 [来源: 附件：餐饮小票 -> 用餐金额]，值为'1876'。[来源: 附件：支付记录 -> 支付金额]，值为'-1876.00'，绝对值一致，符合要求。"
    },
    {
      "rule_id": "规则23：检查报销与支付金额一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 消费金额]，值为'1876.00'。[来源: 附件：支付记录 -> 支付金额]，值为'-1876.00'，绝对值一致，符合要求。"
    },
    {
      "rule_id": "规则24：检查发票开具的及时性。",
      "status": "PASS",
      "message": "根据 [来源: 附件：发票 -> 开票日期]，值为'2025-04-21'。[来源: 主报销单信息 -> 招待日期]，值为'2025-04-18'，开票日期晚于招待日期，符合要求。"
    },
    {
      "rule_id": "规则25：检查实际消费是否超预算。",
      "status": "PASS",
      "message": "根据 [来源: 附件：餐饮小票 -> 用餐金额]，值为1876.00；[来源: 主报销单信息 -> 酒水金额]，值为0.00；[来源: 附件：业务招待事前审批表 -> 预计招待金额]，值为2000元。计算 (A + B) = 1876.00 + 0.00 = 1876.00，小于或等于 C = 2000.00。"
    },
    {
      "rule_id": "规则26：检查酒水使用情况。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 酒水金额]，值为0.00。由于酒水金额为0，该规则不适用。"
    },
    {
      "rule_id": "规则27-28：检查人均消费是否超标（合并）。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 消费金额]，值为1876.00；[来源: 主报销单信息 -> 总人数]，值为11；[来源: 主报销单信息 -> 餐饮标准]，值为550.00。计算人均实际消费 D = 1876.00 / 11 = 170.55，小于或等于 C = 550.00。"
    },
    {
      "rule_id": "规则29：检查是否存在按人头计算的菜品超量。",
      "status": "PASS",
      "message": "根据 [来源: 附件：餐饮小票 -> 菜品名称]，茶位费份数为11，与 [来源: 附件：餐饮小票 -> 人数] 一致，均为11人。其他按份数计的菜品未发现超量情况。"
    },
    {
      "rule_id": "规则30：检查是否存在天价菜。",
      "status": "PASS",
      "message": "根据 [来源: 附件：餐饮小票 -> 菜品列表]，所有菜品的单价均未超过500元。"
    },
    {
      "rule_id": "规则31：检查招待对象一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待对象]，值为'北京中指实证数据信息技术有限公司深圳分公司（中指研究院）'。根据 [来源: 附件：业务招待事前审批表 -> 招待对象]，值也为'北京中指实证数据信息技术有限公司深圳分公司（中指研究院）'，两者一致。"
    },
    {
      "rule_id": "规则32：检查公务招待的消费内容。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待类型]，值为'商务招待'，该规则仅适用于公务招待，当前情况下规则不适用。"
    },
    {
      "rule_id": "规则33：检查公务招待的来函。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待类型]，值为'商务招待'，该规则仅适用于公务招待，当前情况下规则不适用。"
    },
    {
      "rule_id": "规则34：检查是否存在奢侈或违规消费。",
      "status": "PASS",
      "message": "根据 [来源: 附件：餐饮小票 -> 菜品名称] 和 [来源: 附件：发票 -> 销售方地址或名称]，未发现包含鱼翅、燕窝、野生动物、高档烟酒、会所、俱乐部、KTV、度假村、SPA、扑克牌等违禁或高风险字样。"
    },
    {
      "rule_id": "规则35：检查公务招待的住宿费用。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待类型]，值为'商务招待'，该规则仅适用于公务招待，当前情况下规则不适用。"
    },
    {
      "rule_id": "规则36-37：检查公务招待函件与报销信息一致性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待类型]，值为'商务招待'，该规则仅适用于公务招待，当前情况下规则不适用。"
    },
    {
      "rule_id": "规则38：检查消费场所合规性。",
      "status": "PASS",
      "message": "根据 [来源: 主报销单信息 -> 招待类型]，值为'商务招待'，该规则仅适用于公务招待，当前情况下规则不适用。"
    }
  ],
  "review_comments": "经审核，本次业务招待费报销存在以下需要关注的问题：\n\n1. 招待事由与项目用途的描述存在不一致，建议核实招待的具体目的是否与项目需求相符\n\n以上问题虽不影响报销的合规性，但建议申请人在今后的报销中注意相关要求，确保信息填写的准确性和完整性。基于当前情况，可予以通过。",
  "audit_metadata": {
    "version": "2.0",
    "audit_type": "stepwise_intelligent_audit",
    "timestamp": "2025-07-23 16:39:26"
  }
};

// 报告生成时间戳
window.AUDIT_REPORT_TIMESTAMP = 1753259966476;

// 单据编号
window.DOCUMENT_NUMBER = "ZDBXD2025042900003";

// 触发报告数据加载事件
if (typeof window.onAuditReportLoaded === 'function') {
    window.onAuditReportLoaded(window.AUDIT_REPORT);
}

console.log('✅ 审核报告数据已加载');

// 前端实时数据同步调试脚本
// 在浏览器控制台中运行此脚本来调试前后端同步问题

console.log('🔧 前端实时数据同步调试工具');
console.log('=' .repeat(50));

// 1. 检查API连接
async function testAPIConnection() {
    console.log('\n📡 测试API连接...');
    
    const apiPorts = [8001, 8002, 8003, 8004];
    let workingAPI = null;
    
    for (const port of apiPorts) {
        try {
            const response = await fetch(`http://localhost:${port}/api/status`);
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ API服务器正常: http://localhost:${port}`);
                console.log(`   当前步骤: ${data.current_step || 'N/A'}`);
                console.log(`   状态消息: ${data.message || 'N/A'}`);
                console.log(`   文档编号: ${data.doc_num || 'N/A'}`);
                workingAPI = `http://localhost:${port}`;
                break;
            }
        } catch (error) {
            console.log(`❌ 端口 ${port} 连接失败: ${error.message}`);
        }
    }
    
    if (!workingAPI) {
        console.log('⚠️ 未找到可用的API服务器');
        return null;
    }
    
    return workingAPI;
}

// 2. 测试实时轮询
async function testRealTimePolling(apiUrl) {
    console.log('\n🔄 测试实时轮询...');
    
    let pollCount = 0;
    const maxPolls = 10;
    
    const pollInterval = setInterval(async () => {
        pollCount++;
        console.log(`\n[轮询 ${pollCount}/${maxPolls}]`);
        
        try {
            const response = await fetch(`${apiUrl}/api/status`);
            if (response.ok) {
                const data = await response.json();
                console.log(`   步骤: ${data.current_step || 'N/A'}`);
                console.log(`   消息: ${data.message || 'N/A'}`);
                console.log(`   进度: ${data.progress || 'N/A'}%`);
                console.log(`   时间: ${data.timestamp || 'N/A'}`);
                
                // 检查数据是否有变化
                if (window.lastPollingData) {
                    const changed = JSON.stringify(data) !== JSON.stringify(window.lastPollingData);
                    console.log(`   变化: ${changed ? '✅ 有更新' : '⚠️ 无变化'}`);
                }
                window.lastPollingData = data;
                
            } else {
                console.log(`   ❌ API请求失败: HTTP ${response.status}`);
            }
        } catch (error) {
            console.log(`   ❌ 轮询错误: ${error.message}`);
        }
        
        if (pollCount >= maxPolls) {
            clearInterval(pollInterval);
            console.log('\n🔄 轮询测试完成');
        }
    }, 3000);
    
    return pollInterval;
}

// 3. 检查前端元素更新
function checkFrontendElements() {
    console.log('\n🎨 检查前端元素...');
    
    // 检查关键元素是否存在
    const elements = {
        'status-display': '状态显示区域',
        'progress-bar': '进度条',
        'audit-statistics': '审核统计',
        'current-step': '当前步骤',
        'progress-fill': '进度填充'
    };
    
    for (const [id, name] of Object.entries(elements)) {
        const element = document.getElementById(id) || document.querySelector(`.${id}`);
        if (element) {
            console.log(`✅ ${name}: 找到元素`);
            console.log(`   内容: ${element.textContent?.substring(0, 50) || '空'}...`);
        } else {
            console.log(`❌ ${name}: 未找到元素 (#${id} 或 .${id})`);
        }
    }
}

// 4. 模拟前端更新
function simulateFrontendUpdate() {
    console.log('\n🔧 模拟前端更新...');
    
    const testData = {
        current_step: 'debug-test',
        status: 'running',
        message: '调试测试中...',
        progress: 75,
        timestamp: new Date().toLocaleString(),
        final_stats: {
            total_rules_checked: 38,
            passed_count: 30,
            warning_count: 5,
            failed_count: 3
        }
    };
    
    console.log('测试数据:', testData);
    
    // 尝试调用前端更新函数
    if (typeof window.updateRealStatus === 'function') {
        console.log('✅ 调用 updateRealStatus');
        window.updateRealStatus(testData);
    } else {
        console.log('❌ updateRealStatus 函数不存在');
    }
    
    if (typeof window.updateRealProgress === 'function') {
        console.log('✅ 调用 updateRealProgress');
        window.updateRealProgress(testData);
    } else {
        console.log('❌ updateRealProgress 函数不存在');
    }
    
    if (typeof window.updateAuditStatistics === 'function') {
        console.log('✅ 调用 updateAuditStatistics');
        window.updateAuditStatistics(testData.final_stats);
    } else {
        console.log('❌ updateAuditStatistics 函数不存在');
    }
}

// 5. 检查控制台对象
function checkConsoleObjects() {
    console.log('\n🔍 检查控制台对象...');
    
    const objects = ['aiConsole', 'AIConsoleEnhanced', 'statusPollingInterval'];
    
    for (const obj of objects) {
        if (window[obj]) {
            console.log(`✅ ${obj}: 存在`);
            console.log(`   类型: ${typeof window[obj]}`);
        } else {
            console.log(`❌ ${obj}: 不存在`);
        }
    }
}

// 主调试函数
async function debugRealtimeSync() {
    console.log('🚀 开始前端实时同步调试...');
    
    // 1. 测试API连接
    const apiUrl = await testAPIConnection();
    
    // 2. 检查前端元素
    checkFrontendElements();
    
    // 3. 检查控制台对象
    checkConsoleObjects();
    
    // 4. 模拟前端更新
    simulateFrontendUpdate();
    
    // 5. 如果API可用，开始轮询测试
    if (apiUrl) {
        console.log('\n⏰ 将在5秒后开始轮询测试...');
        setTimeout(() => {
            testRealTimePolling(apiUrl);
        }, 5000);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('🔧 调试工具已启动');
    console.log('💡 提示: 查看上面的输出来诊断问题');
    console.log('📋 常见问题:');
    console.log('   - API服务器未启动');
    console.log('   - 前端元素ID不匹配');
    console.log('   - JavaScript函数未正确加载');
    console.log('   - CORS跨域问题');
}

// 自动运行调试
debugRealtimeSync();

// 导出调试函数供手动调用
window.debugRealtimeSync = debugRealtimeSync;
window.testAPIConnection = testAPIConnection;
window.checkFrontendElements = checkFrontendElements;
window.simulateFrontendUpdate = simulateFrontendUpdate;

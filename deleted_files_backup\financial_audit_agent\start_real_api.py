#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实状态API服务器 - 读取实际的状态文件
"""

import json
import time
import socket
import threading
import http.server
import socketserver
from pathlib import Path
from urllib.parse import urlparse, parse_qs


class RealStatusAPIHandler(http.server.BaseHTTPRequestHandler):
    """读取真实状态文件的API处理器"""
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        project_root = Path(__file__).parent
        
        # API响应
        if parsed_path.path == '/api/status':
            # 读取真实的状态文件
            status_file = project_root / "backend" / "audit_status.json"
            
            if status_file.exists():
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        response = json.load(f)
                    
                    print(f"[API] 读取状态文件成功: {response.get('current_step')}")
                    
                    # 根据状态添加引擎信息和进度
                    current_step = response.get('current_step', 'ready')
                    
                    if current_step == 'report-generation':
                        response['engine_status'] = {
                            'deterministic': {'completed': 12, 'total': 12, 'progress': 100},
                            'keyword': {'completed': 14, 'total': 14, 'progress': 100},
                            'semantic': {'completed': 12, 'total': 12, 'progress': 100}
                        }
                        response['progress'] = 100
                        response['message'] = '审核完成！所有规则检查已完成'
                        
                    elif current_step == 'deterministic-rules':
                        response['engine_status'] = {
                            'deterministic': {'completed': 8, 'total': 12, 'progress': 67},
                            'keyword': {'completed': 0, 'total': 14, 'progress': 0},
                            'semantic': {'completed': 0, 'total': 12, 'progress': 0}
                        }
                        response['progress'] = 35
                        response['message'] = '正在执行确定性规则检查...'
                        
                    elif current_step == 'keyword-rules':
                        response['engine_status'] = {
                            'deterministic': {'completed': 12, 'total': 12, 'progress': 100},
                            'keyword': {'completed': 8, 'total': 14, 'progress': 57},
                            'semantic': {'completed': 0, 'total': 12, 'progress': 0}
                        }
                        response['progress'] = 65
                        response['message'] = '正在执行关键词规则检查...'
                        
                    elif current_step == 'semantic-rules':
                        response['engine_status'] = {
                            'deterministic': {'completed': 12, 'total': 12, 'progress': 100},
                            'keyword': {'completed': 14, 'total': 14, 'progress': 100},
                            'semantic': {'completed': 6, 'total': 12, 'progress': 50}
                        }
                        response['progress'] = 85
                        response['message'] = '正在执行AI语义规则检查...'
                        
                    else:
                        # 默认状态
                        response['engine_status'] = {
                            'deterministic': {'completed': 0, 'total': 12, 'progress': 0},
                            'keyword': {'completed': 0, 'total': 14, 'progress': 0},
                            'semantic': {'completed': 0, 'total': 12, 'progress': 0}
                        }
                        if 'progress' not in response:
                            response['progress'] = 0
                    
                    response['api_timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
                    response['server_status'] = 'online'
                    response['file_source'] = str(status_file)
                    
                except Exception as e:
                    print(f"[API] 读取状态文件失败: {e}")
                    response = {
                        "current_step": "error",
                        "status": "error",
                        "message": f"读取状态文件失败: {e}",
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "progress": 0
                    }
            else:
                print(f"[API] 状态文件不存在: {status_file}")
                response = {
                    "current_step": "ready",
                    "status": "online",
                    "message": "状态文件不存在，系统就绪",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "server_status": "online",
                    "file_source": "not_found",
                    "progress": 0
                }
        
        elif parsed_path.path == '/api/report':
            # 读取审核报告
            query_params = parse_qs(parsed_path.query)
            doc_num = query_params.get('doc_num', [None])[0]
            
            if doc_num:
                report_file = project_root / "audit_reports" / f"audit_report_{doc_num}.json"
                if report_file.exists():
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        print(f"[API] 读取报告文件成功: {doc_num}")
                        
                        # 添加元数据
                        if 'metadata' not in response:
                            response['metadata'] = {}
                        response['metadata'].update({
                            "source": report_file.name,
                            "doc_num": doc_num,
                            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                            "api_server": "real_status"
                        })
                        
                    except Exception as e:
                        print(f"[API] 读取报告文件失败: {e}")
                        response = {"error": f"Failed to read report file: {e}"}
                else:
                    print(f"[API] 报告文件不存在: {report_file}")
                    response = {"error": "Report file not found"}
            else:
                response = {"error": "Document number required"}
        
        elif parsed_path.path == '/api/rules':
            response = {
                "deterministic_rules": {"count": 12, "description": "确定性规则引擎"},
                "keyword_rules": {"count": 14, "description": "关键词规则引擎"},
                "semantic_rules": {"count": 12, "description": "AI语义规则引擎"},
                "total_rules": 38
            }
        
        elif parsed_path.path == '/api/progress':
            # 读取状态文件作为进度信息
            status_file = project_root / "backend" / "audit_status.json"
            
            if status_file.exists():
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status_data = json.load(f)
                    
                    current_step = status_data.get('current_step', 'ready')
                    
                    if current_step == 'report-generation':
                        response = {
                            "overall_progress": 100,
                            "current_phase": "completed",
                            "engines": {
                                "deterministic": {"status": "completed", "progress": 100, "completed": 12, "total": 12},
                                "keyword": {"status": "completed", "progress": 100, "completed": 14, "total": 14},
                                "semantic": {"status": "completed", "progress": 100, "completed": 12, "total": 12}
                            }
                        }
                    else:
                        response = {
                            "overall_progress": status_data.get('progress', 0),
                            "current_phase": current_step,
                            "engines": {
                                "deterministic": {"status": "pending", "progress": 0, "completed": 0, "total": 12},
                                "keyword": {"status": "pending", "progress": 0, "completed": 0, "total": 14},
                                "semantic": {"status": "pending", "progress": 0, "completed": 0, "total": 12}
                            }
                        }
                    
                    response['file_source'] = str(status_file)
                    
                except Exception as e:
                    print(f"[API] 读取进度文件失败: {e}")
                    response = {"error": f"Failed to read progress: {e}"}
            else:
                response = {
                    "overall_progress": 0,
                    "current_phase": "ready",
                    "engines": {
                        "deterministic": {"status": "pending", "progress": 0, "completed": 0, "total": 12},
                        "keyword": {"status": "pending", "progress": 0, "completed": 0, "total": 14},
                        "semantic": {"status": "pending", "progress": 0, "completed": 0, "total": 12}
                    },
                    "file_source": "not_found"
                }
        
        else:
            response = {
                "error": "API endpoint not found",
                "available_endpoints": ["/api/status", "/api/report", "/api/rules", "/api/progress"]
            }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.end_headers()
    
    def log_message(self, format, *args):
        # 简化日志输出
        print(f"[HTTP] {args[0]} {args[1]}")


def main():
    """主函数"""
    print("真实状态API服务器")
    print("=" * 40)
    print("读取实际的状态文件和审核报告")
    print()
    
    # 检查关键文件
    project_root = Path(__file__).parent
    status_file = project_root / "backend" / "audit_status.json"
    report_file = project_root / "audit_reports" / "audit_report_ZDBXD2025042900003.json"
    
    print(f"状态文件: {status_file.exists()} - {status_file}")
    print(f"报告文件: {report_file.exists()} - {report_file}")
    
    if status_file.exists():
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            print(f"当前状态: {status_data.get('current_step')} - {status_data.get('message')}")
        except Exception as e:
            print(f"读取状态文件失败: {e}")
    
    print()
    
    # 启动API服务器
    api_port = 8001
    
    try:
        with socketserver.TCPServer(("", api_port), RealStatusAPIHandler) as httpd:
            print(f"[启动] 真实状态API服务器: http://localhost:{api_port}")
            print(f"[端点] /api/status - 获取实时状态")
            print(f"[端点] /api/report?doc_num=ZDBXD2025042900003 - 获取审核报告")
            print(f"[端点] /api/rules - 获取规则信息")
            print(f"[端点] /api/progress - 获取进度信息")
            print()
            print("[运行] 服务器正在运行，按 Ctrl+C 停止")
            print("=" * 40)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n[停止] API服务器已停止")
    except Exception as e:
        print(f"[错误] API服务器启动失败: {e}")


if __name__ == "__main__":
    main()

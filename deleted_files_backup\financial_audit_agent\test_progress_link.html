<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度页面链接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            display: inline-block;
            margin: 10px;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        #console-output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 进度页面链接测试</h1>
        
        <div class="test-section">
            <h2>模拟进度页面链接生成</h2>
            <p>当前页面URL: <span id="current-url"></span></p>
            <p>检测到的单据编号: <span id="detected-doc"></span></p>
            
            <button class="test-button" onclick="testLinkGeneration()">测试链接生成</button>
            <button class="test-button" onclick="clearConsole()">清空控制台</button>
            
            <div class="result">
                <strong>生成的链接:</strong> <span id="generated-link">未生成</span><br>
                <a href="#" id="test-report-link" target="_blank" class="test-button" style="margin-top: 10px;">点击测试链接</a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>控制台输出</h2>
            <div id="console-output">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h2>测试链接</h2>
            <a href="?doc=ZDBXD2025042900003" class="test-button">添加单据编号参数</a>
            <a href="?" class="test-button">移除参数</a>
        </div>
    </div>

    <script>
        // 重定向console.log到页面
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : '✅';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // 显示当前URL信息
        document.getElementById('current-url').textContent = window.location.href;
        
        // 检测单据编号
        const urlParams = new URLSearchParams(window.location.search);
        const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
        document.getElementById('detected-doc').textContent = documentNumber || '无';
        
        // 复制进度页面的链接生成逻辑
        function testLinkGeneration() {
            console.log('🔧 开始测试链接生成...');
            
            const reportLink = document.getElementById('test-report-link');
            if (!reportLink) {
                console.error('❌ 测试链接元素未找到!');
                return;
            }
            
            // 获取单据编号参数
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');
            console.log('📋 检测到的单据编号:', documentNumber);
            
            // 检测当前页面的访问方式
            const currentUrl = window.location.href;
            console.log('🌐 当前URL:', currentUrl);
            let reportUrl;
            
            if (currentUrl.includes('localhost:63342')) {
                const baseUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/'));
                reportUrl = baseUrl + '/audit_viewer.html';
                console.log('🔧 使用PyCharm服务器URL');
            } else if (currentUrl.includes('localhost:8000')) {
                reportUrl = 'http://localhost:8000/frontend/audit_viewer.html';
                console.log('🔧 使用我们的Web服务器URL');
            } else if (currentUrl.startsWith('file://')) {
                reportUrl = 'http://localhost:8000/frontend/audit_viewer.html';
                console.log('🔧 检测到file://协议，重定向到HTTP服务器');
            } else {
                reportUrl = 'audit_viewer.html';
                console.log('🔧 使用相对路径');
            }
            
            // 添加单据编号参数
            if (documentNumber) {
                const separator = reportUrl.includes('?') ? '&' : '?';
                reportUrl += `${separator}doc=${documentNumber}`;
                console.log('✅ 添加单据编号到URL:', documentNumber);
            }
            
            reportLink.href = reportUrl;
            document.getElementById('generated-link').textContent = reportUrl;
            console.log('✅ 最终生成的链接:', reportUrl);
            
            // 验证链接
            if (!reportUrl || reportUrl === '#') {
                console.error('❌ 生成的链接无效!');
            } else {
                console.log('✅ 链接生成成功!');
            }
        }
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('🚀 页面加载完成，开始自动测试...');
            testLinkGeneration();
        };
    </script>
</body>
</html>

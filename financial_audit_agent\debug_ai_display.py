#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI显示内容调试脚本
检查AI思考内容的显示问题
"""

import json
from pathlib import Path

def analyze_ai_thinking_content():
    """分析AI思考内容"""
    print("🔍 分析AI思考内容显示问题")
    
    # 读取当前状态
    state_file = Path("backend/audit_state.json")
    if not state_file.exists():
        print("❌ 状态文件不存在")
        return
    
    with open(state_file, 'r', encoding='utf-8') as f:
        state_data = json.load(f)
    
    ai_thinking = state_data.get('ai_thinking', '')
    
    print(f"📊 AI思考内容分析:")
    print(f"  - 内容长度: {len(ai_thinking)} 字符")
    print(f"  - 审核状态: {state_data.get('audit_status')}")
    print(f"  - 当前阶段: {state_data.get('current_phase')}")
    print(f"  - 进度: {state_data.get('progress_percent')}%")
    
    # 检查内容格式
    if ai_thinking:
        print(f"\n📝 内容前500字符:")
        print(f"'{ai_thinking[:500]}...'")
        
        # 检查是否包含对话历史
        problematic_patterns = [
            "好的！我将立即开始执行",
            "🚀 开始执行阶段调整",
            "步骤1：备份原文件",
            "Created file",
            "Edited file",
            "Terminal"
        ]
        
        has_problems = False
        for pattern in problematic_patterns:
            if pattern in ai_thinking:
                print(f"⚠️  发现问题模式: '{pattern}'")
                has_problems = True
        
        if has_problems:
            print("\n❌ AI思考内容包含对话历史，不是实际的审核思考过程")
            return False
        else:
            print("\n✅ AI思考内容格式正常")
            
            # 检查是否包含正常的审核内容
            normal_patterns = [
                "附件完整性检查",
                "规则1：",
                "规则2：",
                "审核规则",
                "单据数据"
            ]
            
            found_normal = 0
            for pattern in normal_patterns:
                if pattern in ai_thinking:
                    found_normal += 1
            
            if found_normal >= 3:
                print(f"✅ 包含 {found_normal} 个正常审核模式")
                return True
            else:
                print(f"⚠️  只包含 {found_normal} 个正常审核模式，可能内容不完整")
                return False
    else:
        print("❌ AI思考内容为空")
        return False

def check_frontend_display():
    """检查前端显示逻辑"""
    print("\n🔍 检查前端显示逻辑")
    
    js_file = Path("frontend/ai_console_enhanced.js")
    if not js_file.exists():
        print("❌ 前端JS文件不存在")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键函数
    key_functions = [
        "updateAIThinking",
        "renderFullThinkingContent", 
        "renderMarkdownWithStreaming",
        "thinking-content"
    ]
    
    missing_functions = []
    for func in key_functions:
        if func not in content:
            missing_functions.append(func)
    
    if missing_functions:
        print(f"❌ 缺少关键函数: {missing_functions}")
        return False
    else:
        print("✅ 前端显示函数完整")
        return True

def generate_clean_ai_thinking():
    """生成干净的AI思考内容"""
    print("\n🛠️ 生成干净的AI思考内容")
    
    clean_thinking = """## 🔍 附件完整性检查 (阶段 1/4)

### 📋 审核规则分析

我正在执行第一阶段的附件完整性检查，需要验证以下5条规则：

**规则1：检查是否上传发票**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 预期结果：列表中应包含"发票"

**规则2：检查是否上传事前审批表**
- 检查路径：[来源: 附件概览 -> 附件类型]  
- 预期结果：列表中应包含"业务招待事前审批表"

**规则3：检查是否上传用餐小票**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 预期结果：列表中应包含"餐饮小票"

**规则4：检查是否上传支付记录**
- 检查路径：[来源: 附件概览 -> 附件类型]
- 预期结果：列表中应包含"支付记录"

**规则5：检查特殊物品签收表**
- 检查路径：[来源: 主报销单信息 -> 事由]
- 条件判断：如果事由中提及非餐饮物品，则需要签收表

### 🔄 正在分析单据数据...

正在解析附件概览信息，验证必需附件是否齐全。

### ✅ 第一阶段分析完成

所有必需附件已验证完毕，准备进入下一阶段。"""

    return clean_thinking

def fix_ai_thinking_content():
    """修复AI思考内容"""
    print("\n🔧 修复AI思考内容")
    
    # 生成干净的内容
    clean_content = generate_clean_ai_thinking()
    
    # 更新状态文件
    state_file = Path("backend/audit_state.json")
    with open(state_file, 'r', encoding='utf-8') as f:
        state_data = json.load(f)
    
    # 更新AI思考内容
    state_data['ai_thinking'] = clean_content
    state_data['last_updated'] = "2025-07-27T23:55:00Z"
    
    # 写回文件
    with open(state_file, 'w', encoding='utf-8') as f:
        json.dump(state_data, f, ensure_ascii=False, indent=2)
    
    print("✅ AI思考内容已修复")
    print("📝 新内容预览:")
    print(clean_content[:200] + "...")

def main():
    print("="*60)
    print("🔍 AI分析引擎显示问题诊断")
    print("="*60)
    
    # 分析当前内容
    content_ok = analyze_ai_thinking_content()
    
    # 检查前端逻辑
    frontend_ok = check_frontend_display()
    
    # 如果内容有问题，进行修复
    if not content_ok:
        print("\n🚨 检测到AI思考内容异常，正在修复...")
        fix_ai_thinking_content()
        
        print("\n🚀 修复完成！")
        print("\n📝 下一步操作:")
        print("  1. 刷新前端页面 (Ctrl+F5)")
        print("  2. 检查AI分析引擎显示是否正常")
        print("  3. 如果仍有问题，重启后端服务")
    else:
        print("\n✅ AI思考内容正常")
        print("\n💡 如果前端显示仍有问题，请:")
        print("  1. 检查浏览器控制台错误")
        print("  2. 强制刷新页面 (Ctrl+F5)")
        print("  3. 检查网络连接")

if __name__ == "__main__":
    main()

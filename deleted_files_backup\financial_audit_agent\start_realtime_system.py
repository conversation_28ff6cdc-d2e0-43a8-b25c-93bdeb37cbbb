#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时同步的AI财务审核系统启动器
确保前后端实时数据同步
"""

import os
import sys
import json
import time
import socket
import threading
import webbrowser
import http.server
import socketserver
import argparse
import subprocess
from pathlib import Path
from urllib.parse import urlparse, parse_qs


class RealtimeAuditAPIHandler(http.server.BaseHTTPRequestHandler):
    """实时审核API请求处理器"""
    
    @property
    def server_doc_num(self):
        """获取服务器设置的文档编号"""
        return getattr(self.server, 'doc_num', None)
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        # API响应
        if parsed_path.path == '/api/status':
            # 实时读取状态文件
            status_files = [
                Path(__file__).parent / "backend" / "audit_status.json",
                Path(__file__).parent / "audit_status.json"
            ]
            
            response = None
            for status_file in status_files:
                if status_file.exists():
                    try:
                        with open(status_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        response['api_timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
                        response['server_status'] = 'online'
                        response['doc_num'] = self.server_doc_num
                        response['file_source'] = str(status_file)
                        break
                    except Exception as e:
                        continue
            
            if not response:
                response = {
                    "current_step": "ready",
                    "status": "online",
                    "message": "系统就绪，等待开始审核",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "server_status": "online",
                    "doc_num": self.server_doc_num,
                    "file_source": "default"
                }
        
        elif parsed_path.path == '/api/report':
            # 检查URL参数中的文档编号
            query_params = parse_qs(parsed_path.query)
            doc_num = query_params.get('doc_num', [None])[0] or self.server_doc_num
            
            report_files = []
            if doc_num:
                specific_report = Path(__file__).parent / "audit_reports" / f"audit_report_{doc_num}.json"
                report_files.append(specific_report)
            
            # 添加默认报告文件
            report_files.extend([
                Path(__file__).parent / "audit_reports" / "audit_report_v2.json",
                Path(__file__).parent / "audit_reports" / "audit_report_default.json"
            ])
            
            response = None
            used_file = None
            for report_file in report_files:
                if report_file.exists():
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        used_file = report_file.name
                        break
                    except Exception as e:
                        continue
            
            if not response:
                response = {
                    "summary": {
                        "total_rules_checked": 38,
                        "passed_count": 0,
                        "failed_count": 0,
                        "warning_count": 0
                    },
                    "details": [],
                    "metadata": {
                        "source": "default_data",
                        "doc_num": doc_num,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                }
            else:
                # 添加元数据
                if 'metadata' not in response:
                    response['metadata'] = {}
                response['metadata'].update({
                    "source": used_file,
                    "doc_num": doc_num,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "api_server": "realtime_system"
                })
        
        elif parsed_path.path == '/api/rules':
            response = {
                "deterministic_rules": {"count": 12, "description": "确定性规则引擎"},
                "keyword_rules": {"count": 14, "description": "关键词规则引擎"},
                "semantic_rules": {"count": 12, "description": "AI语义规则引擎"},
                "total_rules": 38,
                "doc_num": self.server_doc_num
            }
        
        elif parsed_path.path == '/api/progress':
            # 实时读取状态文件作为进度信息
            status_files = [
                Path(__file__).parent / "backend" / "audit_status.json",
                Path(__file__).parent / "audit_status.json"
            ]
            
            response = None
            for status_file in status_files:
                if status_file.exists():
                    try:
                        with open(status_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        response['doc_num'] = self.server_doc_num
                        response['file_source'] = str(status_file)
                        break
                    except Exception as e:
                        continue
            
            if not response:
                response = {
                    "overall_progress": 0,
                    "current_phase": "ready",
                    "phases": {
                        "deterministic_rules": {"status": "pending", "progress": 0},
                        "keyword_rules": {"status": "pending", "progress": 0},
                        "semantic_rules": {"status": "pending", "progress": 0}
                    },
                    "metadata": {
                        "source": "default_progress",
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    },
                    "doc_num": self.server_doc_num
                }
        
        else:
            response = {
                "error": "API endpoint not found",
                "available_endpoints": ["/api/status", "/api/report", "/api/rules", "/api/progress"],
                "doc_num": self.server_doc_num
            }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass  # 静默日志


def find_available_port(start_port):
    """查找可用端口"""
    for port in range(start_port, start_port + 20):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return port
            except OSError:
                continue
    return None


def start_backend_audit(doc_num=None):
    """启动后端审核引擎"""
    print("\n[启动] 启动后端审核引擎...")
    
    try:
        # 构建启动命令
        cmd = [sys.executable, "start_backend_v2.py"]
        if doc_num:
            cmd.extend(["--doc-num", doc_num])
        cmd.extend(["--no-browser", "--no-web"])  # 不启动浏览器和Web服务，我们在这里处理
        
        print(f"[命令] {' '.join(cmd)}")
        
        # 启动后端进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            cwd=Path(__file__).parent,
            bufsize=1,
            universal_newlines=True
        )
        
        # 创建线程来实时输出后端日志
        def output_reader():
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(f"[后端] {line.strip()}")
        
        output_thread = threading.Thread(target=output_reader, daemon=True)
        output_thread.start()
        
        print("[信息] 后端审核引擎已启动，正在后台运行...")
        return process
        
    except Exception as e:
        print(f"[错误] 启动后端审核引擎失败: {e}")
        return None


def start_services(doc_num=None, no_browser=False, no_audit=False):
    """启动所有服务"""
    print("AI财务审核系统 - 实时同步版启动器")
    print("=" * 60)
    print("确保前后端实时数据同步")
    if doc_num:
        print(f"文档编号: {doc_num}")
    if no_audit:
        print("模式: 仅Web服务（不启动审核）")
    else:
        print("模式: 完整审核 + 实时Web服务")
    print()
    
    # 启动API服务器
    print("启动实时API服务器...")
    api_port = find_available_port(8001)
    if not api_port:
        print("[错误] 无法找到可用的API端口")
        return False
    
    def run_api():
        try:
            with socketserver.TCPServer(("", api_port), RealtimeAuditAPIHandler) as httpd:
                httpd.doc_num = doc_num
                httpd.serve_forever()
        except Exception as e:
            print(f"[错误] API服务器异常: {e}")
    
    api_thread = threading.Thread(target=run_api, daemon=True)
    api_thread.start()
    time.sleep(1)
    print(f"[成功] 实时API服务器已启动: http://localhost:{api_port}")
    if doc_num:
        print(f"[信息] 使用文档编号: {doc_num}")
    
    # 启动Web服务器
    print("启动Web服务器...")
    web_port = find_available_port(8002)
    if not web_port:
        print("[错误] 无法找到可用的Web端口")
        return False
    
    class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def log_message(self, format, *args):
            pass
    
    def run_web():
        try:
            original_cwd = os.getcwd()
            project_root = Path(__file__).parent
            os.chdir(project_root)
            with socketserver.TCPServer(("", web_port), CORSRequestHandler) as httpd:
                httpd.serve_forever()
        except Exception as e:
            print(f"[错误] Web服务器异常: {e}")
        finally:
            try:
                os.chdir(original_cwd)
            except:
                pass
    
    web_thread = threading.Thread(target=run_web, daemon=True)
    web_thread.start()
    time.sleep(1)
    print(f"[成功] Web服务器已启动: http://localhost:{web_port}")
    
    # 启动后端审核引擎（如果需要）
    audit_process = None
    if not no_audit:
        audit_process = start_backend_audit(doc_num)
    
    # 构建控制台URL
    console_url = f"http://localhost:{web_port}/frontend/ai_console.html"
    if doc_num:
        console_url += f"?doc_num={doc_num}"
    
    print(f"\n[完成] 实时同步系统启动成功！")
    print(f"API服务: http://localhost:{api_port}")
    print(f"Web服务: http://localhost:{web_port}")
    print(f"AI控制台: {console_url}")
    
    # 自动打开浏览器
    if not no_browser:
        print("\n正在打开AI控制台...")
        try:
            webbrowser.open(console_url)
            print("[成功] 浏览器已打开")
        except Exception as e:
            print(f"[警告] 自动打开浏览器失败: {e}")
            print(f"请手动访问: {console_url}")
    else:
        print(f"\n请手动访问: {console_url}")
    
    print("\n" + "=" * 60)
    print("实时数据同步特性:")
    print("- 前端每3秒自动轮询最新状态")
    print("- API服务器实时读取状态文件")
    print("- 后端审核进度实时更新")
    print("- 支持文档编号参数传递")
    if not no_audit:
        print("- 后台审核引擎实时输出")
    print("\n按 Ctrl+C 停止所有服务")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n[停止] 正在停止所有服务...")
        if audit_process:
            try:
                audit_process.terminate()
                audit_process.wait(timeout=5)
                print("[停止] 后端审核引擎已停止")
            except:
                audit_process.kill()
                print("[停止] 强制停止后端审核引擎")
        print("[停止] 所有服务已停止，感谢使用！")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI财务审核系统实时同步版启动器')
    parser.add_argument('--doc-num', type=str, help='文档编号，用于动态路径生成')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    parser.add_argument('--no-audit', action='store_true', help='不启动后端审核引擎，仅启动Web服务')
    args = parser.parse_args()
    
    try:
        return 0 if start_services(args.doc_num, args.no_browser, args.no_audit) else 1
    except Exception as e:
        print(f"[错误] 启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅启动Web和API服务器，用于测试前后端连接
"""

import os
import sys
import json
import time
import socket
import threading
import webbrowser
import http.server
import socketserver
from pathlib import Path
from urllib.parse import urlparse, parse_qs


class AuditAPIHandler(http.server.BaseHTTPRequestHandler):
    """审核API请求处理器"""
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        # API响应
        if parsed_path.path == '/api/status':
            # 尝试读取状态文件
            status_file = Path(__file__).parent / "backend" / "audit_status.json"
            if status_file.exists():
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        response = json.load(f)
                    response['api_timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
                    response['server_status'] = 'online'
                    print(f"[成功] 加载状态文件: {status_file.name}")
                except Exception as e:
                    print(f"[错误] 读取状态文件失败: {e}")
                    response = {
                        "current_step": "ready",
                        "status": "online",
                        "message": "系统就绪",
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "server_status": "online"
                    }
            else:
                response = {
                    "current_step": "ready",
                    "status": "online",
                    "message": "系统就绪",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "server_status": "online"
                }
        
        elif parsed_path.path == '/api/report':
            # 检查URL参数中的文档编号
            query_params = parse_qs(parsed_path.query)
            doc_num = query_params.get('doc_num', [None])[0]
            
            report_files = []
            if doc_num:
                specific_report = Path(__file__).parent / "audit_reports" / f"audit_report_{doc_num}.json"
                report_files.append(specific_report)
                print(f"[查找] API查找特定报告: {specific_report}")
            
            # 添加默认报告文件
            report_files.extend([
                Path(__file__).parent / "audit_reports" / "audit_report_v2.json",
                Path(__file__).parent / "audit_reports" / "audit_report_default.json"
            ])
            
            response = None
            used_file = None
            for report_file in report_files:
                if report_file.exists():
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            response = json.load(f)
                        used_file = report_file.name
                        print(f"[成功] API加载报告文件: {used_file}")
                        break
                    except Exception as e:
                        print(f"[警告] API读取报告失败: {report_file.name} - {e}")
                        continue
            
            if not response:
                print("[警告] API未找到任何报告文件，使用默认数据")
                response = {
                    "summary": {
                        "total_rules_checked": 38,
                        "passed_count": 35,
                        "failed_count": 1,
                        "warning_count": 2
                    },
                    "details": [
                        {"rule_id": "规则1", "status": "PASS", "message": "检查通过"},
                        {"rule_id": "规则7", "status": "WARNING", "message": "需要注意"},
                        {"rule_id": "规则15", "status": "FAIL", "message": "检查失败"}
                    ],
                    "metadata": {
                        "source": "default_data",
                        "doc_num": doc_num,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                }
            else:
                # 添加元数据
                if 'metadata' not in response:
                    response['metadata'] = {}
                response['metadata'].update({
                    "source": used_file,
                    "doc_num": doc_num,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "api_server": "web_only"
                })
        
        elif parsed_path.path == '/api/rules':
            response = {
                "deterministic_rules": {"count": 12, "description": "确定性规则引擎"},
                "keyword_rules": {"count": 14, "description": "关键词规则引擎"},
                "semantic_rules": {"count": 12, "description": "AI语义规则引擎"},
                "total_rules": 38
            }
        
        elif parsed_path.path == '/api/progress':
            # 尝试读取状态文件
            status_file = Path(__file__).parent / "backend" / "audit_status.json"
            if status_file.exists():
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        response = json.load(f)
                    print(f"[成功] API加载进度文件: {status_file.name}")
                except Exception as e:
                    print(f"[错误] API读取进度文件失败: {e}")
                    response = {"error": "Failed to load progress"}
            else:
                response = {
                    "overall_progress": 0,
                    "current_phase": "ready",
                    "phases": {
                        "deterministic_rules": {"status": "pending", "progress": 0},
                        "keyword_rules": {"status": "pending", "progress": 0},
                        "semantic_rules": {"status": "pending", "progress": 0}
                    },
                    "metadata": {
                        "source": "default_progress",
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                }
        
        else:
            response = {
                "error": "API endpoint not found",
                "available_endpoints": ["/api/status", "/api/report", "/api/rules", "/api/progress"]
            }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass  # 静默日志


def find_available_port(start_port):
    """查找可用端口"""
    for port in range(start_port, start_port + 20):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return port
            except OSError:
                continue
    return None


def start_api_server():
    """启动API服务器"""
    api_port = find_available_port(8001)
    if not api_port:
        print("[错误] 无法找到可用的API端口")
        return None
    
    def run_api():
        try:
            with socketserver.TCPServer(("", api_port), AuditAPIHandler) as httpd:
                httpd.serve_forever()
        except Exception as e:
            print(f"[错误] API服务器异常: {e}")
    
    api_thread = threading.Thread(target=run_api, daemon=True)
    api_thread.start()
    
    time.sleep(1)  # 等待服务器启动
    print(f"[成功] API服务器已启动: http://localhost:{api_port}")
    return api_port


def start_web_server():
    """启动Web服务器"""
    web_port = find_available_port(8002)
    if not web_port:
        print("[错误] 无法找到可用的Web端口")
        return None
    
    class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def log_message(self, format, *args):
            pass  # 静默日志
    
    def run_web():
        try:
            original_cwd = os.getcwd()
            project_root = Path(__file__).parent
            os.chdir(project_root)
            with socketserver.TCPServer(("", web_port), CORSRequestHandler) as httpd:
                httpd.serve_forever()
        except Exception as e:
            print(f"[错误] Web服务器异常: {e}")
        finally:
            try:
                os.chdir(original_cwd)
            except:
                pass
    
    web_thread = threading.Thread(target=run_web, daemon=True)
    web_thread.start()
    
    time.sleep(1)  # 等待服务器启动
    print(f"[成功] Web服务器已启动: http://localhost:{web_port}")
    return web_port


def main():
    """主函数"""
    print("启动Web和API服务器（仅前端测试）")
    print("=" * 50)
    
    try:
        # 启动API服务器
        print("启动API服务器...")
        api_port = start_api_server()
        
        # 启动Web服务器
        print("启动Web服务器...")
        web_port = start_web_server()
        
        if api_port and web_port:
            console_url = f"http://localhost:{web_port}/frontend/ai_console.html?doc_num=ZDBXD2025051300001"
            
            print(f"\n[完成] 服务启动成功！")
            print(f"API服务: http://localhost:{api_port}")
            print(f"Web服务: http://localhost:{web_port}")
            print(f"AI控制台: {console_url}")
            
            # 自动打开浏览器
            print("\n正在打开浏览器...")
            try:
                webbrowser.open(console_url)
                print("[成功] 浏览器已打开")
            except Exception as e:
                print(f"[警告] 自动打开浏览器失败: {e}")
                print(f"请手动访问: {console_url}")
            
            print("\n按 Ctrl+C 停止服务")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n[停止] 服务已停止")
        else:
            print("[错误] 服务启动失败")
            return 1
    
    except Exception as e:
        print(f"[错误] 启动异常: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

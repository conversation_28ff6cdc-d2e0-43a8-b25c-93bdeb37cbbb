<!DOCTYPE html>
<html>
<head>
    <title>测试页面</title>
</head>
<body>
    <h1>HTTP服务器测试成功！</h1>
    <p>如果您能看到这个页面，说明HTTP服务器正常工作。</p>
    <p>当前时间: <span id="time"></span></p>
    
    <h2>当前服务器信息</h2>
    <p><strong>当前URL:</strong> <span id="current-url"></span></p>
    <p><strong>端口:</strong> <span id="current-port"></span></p>

    <h2>测试链接</h2>
    <ul>
        <li><a href="/frontend/audit_viewer.html">审核报告页面</a></li>
        <li><a href="/frontend/audit_viewer.html?doc=ZDBXD2025042900003">带参数的审核报告页面</a></li>
        <li><a href="/audit_reports/">报告目录</a></li>
        <li><a href="/debug_url.html?doc=ZDBXD2025042900003">URL调试页面</a></li>
    </ul>
    
    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('current-port').textContent = window.location.port || '80';
    </script>
</body>
</html>

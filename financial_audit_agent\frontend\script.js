document.addEventListener('DOMContentLoaded', function() {
    loadAuditData();
    updateReportTime();
});

async function loadAuditData() {
    try {
        // 检查协议，只支持HTTP/HTTPS
        if (window.location.protocol === 'file:') {
            showError('此页面需要通过HTTP服务器访问',
                     '请启动Web服务器：python start_web_server.py<br>然后访问：http://localhost:8000/frontend/audit_viewer.html');
            return;
        }

        // 通过HTTP协议加载JSON文件
        console.log('🔄 开始加载JSON审核报告...');

        // 获取URL参数中的单据编号
        const urlParams = new URLSearchParams(window.location.search);
        const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number');

        let possiblePaths = [];

        // 如果有单据编号参数，优先尝试对应的文件
        if (documentNumber) {
            console.log(`🔍 检测到单据编号参数: ${documentNumber}`);
            possiblePaths = [
                `../audit_reports/audit_report_${documentNumber}.json`,
                `/audit_reports/audit_report_${documentNumber}.json`,
                `audit_reports/audit_report_${documentNumber}.json`
            ];
        }

        // 添加默认路径作为回退
        possiblePaths = possiblePaths.concat([
            '../audit_reports/audit_report_v2.json',
            '/audit_reports/audit_report_v2.json',
            'audit_reports/audit_report_v2.json',
            '../audit_reports/audit_report.json',
            '/audit_reports/audit_report.json',
            'audit_reports/audit_report.json'
        ]);

        let response = null;
        let lastError = null;
        let loadedPath = null;

        for (const path of possiblePaths) {
            try {
                console.log(`🔄 尝试加载: ${path}`);
                response = await fetch(path);
                if (response.ok) {
                    loadedPath = path;
                    break;
                }
            } catch (error) {
                lastError = error;
                continue;
            }
        }

        if (response && response.ok) {
            console.log(`✅ 成功加载报告: ${loadedPath}`);
            const report = await response.json();
            updateUI(report);
            return;
        }

        // 如果所有方法都失败了，显示错误
        console.error('❌ 无法加载审核数据');
        const errorMsg = documentNumber
            ? `无法找到单据编号 ${documentNumber} 对应的审核报告`
            : '无法加载审核报告';
        const suggestions = [
            '确保审核报告文件已生成',
            '检查单据编号是否正确',
            '确保通过HTTP服务器访问此页面',
            '尝试访问: http://localhost:8002/frontend/audit_viewer.html'
        ].join('<br>• ');

        showError(errorMsg, `• ${suggestions}`);

    } catch (error) {
        console.error('❌ 加载审核数据时发生错误:', error);
        showError('加载错误', error.message);
    }
}

// 动态加载JavaScript文件的辅助函数
function loadScript(src) {
    return new Promise((resolve, reject) => {
        // 检查是否已经加载过这个脚本
        const existingScript = document.querySelector(`script[src="${src}"]`);
        if (existingScript) {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        document.head.appendChild(script);
    });
}

function updateUI(report) {
    // updateOverallStatus(report.summary); // 已移除状态横幅
    updateSummaryDashboard(report.summary);
    populateAuditDetails(report.details);
    updateReviewComments(report.review_comments || '', report.summary);
    updateReportTime();
}

function updateReportTime() {
    try {
        // 延迟执行，确保DOM完全渲染
        setTimeout(() => {
            try {
                const now = new Date();
                const timeString = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                // 更新页脚的报告生成时间
                const reportGenerationTime = document.getElementById('report-generation-time');
                if (reportGenerationTime) {
                    reportGenerationTime.textContent = `报告生成时间: ${timeString}`;
                    console.log('✅ 报告时间已更新');
                } else {
                    console.warn('⚠️ 未找到 report-generation-time 元素，DOM可能还未完全加载');
                }
            } catch (innerError) {
                console.error('❌ 延迟更新报告时间失败:', innerError);
            }
        }, 200); // 延迟200ms确保DOM渲染完成
    } catch (error) {
        console.error('❌ 更新报告时间失败:', error);
    }
}

function updateOverallStatus(summary) {
    const trafficLight = document.getElementById('traffic-light');
    const statusText = document.getElementById('overall-status-text');
    const statusBanner = document.querySelector('.overall-status-banner');

    if (!trafficLight || !statusText) {
        console.error('❌ 关键状态元素未找到:', {
            trafficLight: !!trafficLight,
            statusText: !!statusText
        });
        return;
    }

    // 重置样式
    trafficLight.className = 'light';
    if (statusBanner) {
        statusBanner.className = 'overall-status-banner';
    }

    if (summary.failed_count > 0) {
        trafficLight.classList.add('red');
        statusText.textContent = `审核不通过 | 发现 ${summary.failed_count} 个严重问题`;
        statusText.style.color = '#dc3545';
        if (statusBanner) statusBanner.classList.add('error');
    } else if (summary.warning_count > 0) {
        trafficLight.classList.add('yellow');
        statusText.textContent = `存在警告 | ${summary.warning_count} 个事项建议人工复核`;
        statusText.style.color = '#ffc107';
        if (statusBanner) statusBanner.classList.add('warning');
    } else {
        trafficLight.classList.add('green');
        statusText.textContent = '审核通过';
        statusText.style.color = '#28a745';
        if (statusBanner) statusBanner.classList.add('success');
    }
}

function updateSummaryDashboard(summary) {
    const elements = {
        'total-rules': summary.total_rules_checked,
        'passed-rules': summary.passed_count,
        'warning-rules': summary.warning_count,
        'failed-rules': summary.failed_count
    };

    for (const [id, value] of Object.entries(elements)) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        } else {
            console.warn(`⚠️ 未找到元素: ${id}`);
        }
    }
}

function populateAuditDetails(details) {
    const tableBody = document.getElementById('audit-table-body');
    if (!tableBody) return;
    tableBody.innerHTML = '';

    // 定义排序优先级
    const statusOrder = { 'FAIL': 1, 'WARNING': 2, 'PASS': 3 };

    // 按 失败 > 警告 > 通过 的顺序对详情进行排序
    const sortedDetails = details.sort((a, b) => statusOrder[a.status] - statusOrder[b.status]);

    if (sortedDetails.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="3" style="text-align:center;">没有可显示的审核详情。</td></tr>';
        return;
    }

    sortedDetails.forEach(item => {
        const row = document.createElement('tr');
        const statusClass = {
            'PASS': 'green',
            'FAIL': 'red',
            'WARNING': 'yellow'
        }[item.status] || 'gray';

        row.innerHTML = `
            <td class="icon-column">
                <span class="status-icon ${statusClass}"></span>
            </td>
            <td>${item.rule_id}</td>
            <td>${item.message}</td>
        `;
        tableBody.appendChild(row);
    });
}

function displayErrorState(message) {
    const tableBody = document.getElementById('audit-table-body');

    if (tableBody) {
        tableBody.innerHTML = `<tr><td colspan="3" style="text-align:center; color: #dc3545; font-weight: bold;">${message}</td></tr>`;
    } else {
        console.error('❌ 未找到 audit-table-body 元素');
    }
}

function displayCORSError() {
    // 状态横幅已移除，直接显示表格错误信息
    const tableBody = document.getElementById('audit-table-body');
    if (tableBody) {
        tableBody.innerHTML = `
        <tr>
            <td colspan="3" style="text-align:left; padding: 20px;">
                <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border: 1px solid #ffeaa7;">
                    <h3 style="color: #856404; margin-top: 0;">🚫 CORS 访问限制</h3>
                    <p style="color: #856404; margin-bottom: 15px;">
                        您正在通过 <code>file://</code> 协议直接打开HTML文件，浏览器的安全策略阻止了数据加载。
                    </p>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <h4 style="color: #495057; margin-top: 0;">✅ 正确的启动方式：</h4>
                        <ol style="color: #495057; line-height: 1.6;">
                            <li><strong>Windows用户</strong>：双击 <code>启动审核系统.bat</code></li>
                            <li><strong>命令行用户</strong>：运行 <code>python start_audit.py --auto</code></li>
                            <li><strong>仅启动Web服务器</strong>：运行 <code>python start_web_server.py</code></li>
                        </ol>
                    </div>

                    <div style="background: #f1f3f4; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <h4 style="color: #495057; margin-top: 0;">🌐 正确的访问地址：</h4>
                        <p style="color: #495057; margin: 5px 0;">
                            <a href="http://localhost:8000/frontend/audit_viewer.html" target="_blank" style="color: #007bff;">
                                http://localhost:8000/frontend/audit_viewer.html
                            </a>
                        </p>
                    </div>

                    <button onclick="window.open('http://localhost:8000/frontend/audit_viewer.html', '_blank')"
                            style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                        尝试打开正确地址
                    </button>
                    <button onclick="location.reload()"
                            style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            </td>
        </tr>
    `;
    }
}

function updateReviewComments(reviewComments, summary) {
    const section = document.getElementById('review-comments-section');
    const textElement = document.getElementById('review-comments-text');

    // 检查是否有审核意见数据
    if (!reviewComments || reviewComments.trim() === '') {
        section.style.display = 'none';
        return;
    }

    // 显示审核意见区域
    section.style.display = 'block';

    // 格式化审核意见内容
    const formattedContent = formatReviewComments(reviewComments);
    textElement.innerHTML = formattedContent;

    // 根据审核结果设置样式
    textElement.className = 'review-comments-formatted';
    if (summary && summary.failed_count > 0) {
        textElement.classList.add('has-failures');
    } else if (summary && summary.warning_count > 0) {
        textElement.classList.add('has-warnings');
    } else {
        textElement.classList.add('no-issues');
    }
}

function formatReviewComments(comments) {
    // 将审核意见格式化为更好的HTML结构，确保左对齐
    let formatted = comments.trim();

    // 分割成段落
    const paragraphs = formatted.split(/\n\s*\n/);
    let result = '';

    for (let i = 0; i < paragraphs.length; i++) {
        const paragraph = paragraphs[i].trim();

        if (paragraph.startsWith('经审核')) {
            // 介绍部分
            result += `<div class="intro">${paragraph}</div>`;
        } else if (paragraph.startsWith('以上问题') || paragraph.includes('建议') || paragraph.includes('可予以通过')) {
            // 结论部分
            result += `<div class="conclusion">${paragraph}</div>`;
        } else if (paragraph.includes('1.') || paragraph.includes('2.') || paragraph.includes('3.')) {
            // 包含编号列表的段落
            result += '<div class="issue-list">';

            // 使用正则表达式分割序号项，处理换行符分隔的序号
            const items = paragraph.split(/(?=\d+\.\s)/);

            for (const item of items) {
                const trimmedItem = item.trim();
                if (trimmedItem && /^\d+\./.test(trimmedItem)) {
                    // 移除原始序号，让CSS自动生成，并处理换行符
                    const contentWithoutNumber = trimmedItem
                        .replace(/^\d+\.\s*/, '')
                        .replace(/\n/g, ' ')  // 将换行符替换为空格
                        .trim();
                    if (contentWithoutNumber) {
                        result += `<div class="issue-item-numbered">${contentWithoutNumber}</div>`;
                    }
                }
            }

            result += '</div>';
        } else if (paragraph) {
            // 其他段落
            result += `<div class="paragraph">${paragraph}</div>`;
        }
    }

    return result;
}

function toggleReviewComments() {
    const content = document.getElementById('review-comments-content');
    const toggleText = document.getElementById('toggle-text');
    const toggleArrow = document.getElementById('toggle-arrow');

    if (!content || !toggleText || !toggleArrow) {
        console.warn('⚠️ 审核意见切换元素未找到');
        return;
    }

    if (content.classList.contains('collapsed')) {
        // 展开
        content.classList.remove('collapsed');
        toggleText.textContent = '收起';
        toggleArrow.classList.remove('collapsed');
    } else {
        // 收起
        content.classList.add('collapsed');
        toggleText.textContent = '展开';
        toggleArrow.classList.add('collapsed');
    }
}

// 旧的updateReportTime函数已移除，使用新版本



// 添加刷新功能
function refreshData() {
    console.log('🔄 正在刷新数据...');
    loadAuditData();
}

// 键盘快捷键支持
document.addEventListener('keydown', function(event) {
    if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
        event.preventDefault();
        refreshData();
    }
});

// 错误模态框相关函数
function closeErrorModal() {
    const modal = document.getElementById('error-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function showErrorModal(message) {
    const modal = document.getElementById('error-modal');
    const errorMessage = document.getElementById('error-message');
    if (modal && errorMessage) {
        errorMessage.textContent = message;
        modal.style.display = 'block';
    }
}

function showError(title, details) {
    const modal = document.getElementById('error-modal');
    const errorMessage = document.getElementById('error-message');
    if (modal && errorMessage) {
        errorMessage.innerHTML = `<strong>${title}</strong><br><br>${details}`;
        modal.style.display = 'block';
    }
}

// 确保DOM准备好后再初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
} else {
    initializePage();
}

function initializePage() {
    // 确保DOM完全准备好后才加载数据
    console.log('🔄 初始化页面...');

    // 检查关键元素是否存在
    const checkElements = () => {
        const requiredElements = [
            'audit-table-body',
            'report-generation-time'
        ];

        const missingElements = requiredElements.filter(id => !document.getElementById(id));

        if (missingElements.length === 0) {
            console.log('✅ 所有必需元素已加载，开始加载数据');
            loadAuditData();
        } else {
            console.log(`⚠️ 等待元素加载: ${missingElements.join(', ')}`);
            setTimeout(checkElements, 100);
        }
    };

    // 延迟检查，确保DOM渲染完成
    setTimeout(checkElements, 200);
}

import json
import os
import time
import webbrowser
from data_models import DataContext, AuditResult
from llm_caller import LLMCaller
from rules.deterministic_rules import DeterministicRules
from rules.keyword_rules import KeywordRules

class Orchestrator:
    def __init__(self, config_path: str):
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        self.llm_caller = LLMCaller(
            api_key=self.config['LLM_API_KEY'],
            model_name=self.config['LLM_MODEL_NAME'],
            config=self.config
        )
        self.audit_results = []

        # 新增：定义状态文件路径
        self.status_file_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            'backend', 'audit_status.json'
        )

    def _update_progress(self, step_id: str, status: str, message: str = ""):
        """
        更新审核进度状态文件。

        Args:
            step_id (str): 步骤ID，与前端对应。
            status (str): 状态 'running', 'completed', 'failed', 'audit-complete'。
            message (str): 显示给用户的消息。
        """
        progress = {
            "current_step": step_id,
            "status": status,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.status_file_path), exist_ok=True)
            with open(self.status_file_path, 'w', encoding='utf-8') as f:
                json.dump(progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error updating progress file: {e}")

    def _load_and_prepare_data(self, dir_path: str) -> DataContext:
        """从JSON文件加载数据并创建DataContext。"""
        path_details = os.path.join(dir_path, "单据识别.json")
        path_attachments = os.path.join(dir_path, "附件识别.json")

        with open(path_details, 'r', encoding='utf-8') as f:
            details_data = json.load(f)
        with open(path_attachments, 'r', encoding='utf-8') as f:
            attachments_data = json.load(f)

        # 将所有数据合并到一个字典中
        all_data = {**details_data, **attachments_data}

        return DataContext(
            source_files={"details": path_details, "attachments": path_attachments},
            attachment_list=attachments_data.get("附件列表", []),
            all_extracted_data=all_data
        )

    def run_audit(self, data_context: DataContext):
        """执行完整的审核工作流。"""
        self.audit_results = []

        try:
            print("开始执行审核规则...")

            # 1. 执行确定性规则
            self._update_progress('data-loading', 'running', '正在执行确定性规则...')
            print("执行确定性规则...")
            det_rules = DeterministicRules(data_context)

            # 基础附件检查规则
            self.audit_results.append(det_rules.check_rule_1_has_invoice())
            self.audit_results.append(det_rules.check_rule_2_has_approval_form())
            self.audit_results.append(det_rules.check_rule_3_has_entertainment_details())

            # 金额和日期一致性规则
            self.audit_results.append(det_rules.check_rule_4_amount_consistency())
            self.audit_results.append(det_rules.check_rule_5_date_within_range())
            self.audit_results.append(det_rules.check_rule_6_invoice_buyer_consistency())
            self.audit_results.append(det_rules.check_rule_9_amount_within_budget())
            self.audit_results.append(det_rules.check_rule_10_participant_count_consistency())
            self.audit_results.append(det_rules.check_rule_11_date_consistency())
            self.audit_results.append(det_rules.check_rule_12_location_consistency())

            # 2. 执行关键词规则
            self._update_progress('rule-parsing', 'running', '正在执行关键词规则...')
            print("执行关键词规则...")
            key_rules = KeywordRules(data_context)
            self.audit_results.append(key_rules.check_rule_7_sensitive_keywords())
            self.audit_results.append(key_rules.check_rule_8_entertainment_keywords())
            self.audit_results.append(key_rules.check_rule_13_luxury_keywords())
            self.audit_results.append(key_rules.check_rule_14_alcohol_keywords())
            self.audit_results.append(key_rules.check_rule_17_gift_keywords())
            self.audit_results.append(key_rules.check_rule_18_travel_keywords())
            self.audit_results.append(key_rules.check_rule_19_high_amount_keywords())
            self.audit_results.append(key_rules.check_rule_20_private_club_keywords())

            # 3. 使用大模型执行语义规则
            self._update_progress('audit-execution', 'running', '正在执行语义规则...')
            print("执行语义规则...")
            self._run_semantic_rules(data_context)

            # 标记审核完成
            self._update_progress('report-generation', 'completed', '审核规则执行完成')

        except Exception as e:
            error_message = f"审核过程中发生错误: {str(e)}"
            print(error_message)
            self._update_progress('data-loading', 'failed', error_message)
            raise

    def _run_semantic_rules(self, data_context: DataContext):
        """执行需要大模型分析的语义规则"""
        
        # 规则15：项目描述语义相似度分析
        reason = data_context.all_extracted_data.get("业务招待事前审批表", {}).get("招待事由", "")
        project_name = data_context.all_extracted_data.get("招待明细", {}).get("项目名称", "")
        
        if reason and project_name:
            try:
                prompt_15 = self.llm_caller.get_rule_15_prompt(reason, project_name)
                llm_response_15 = self.llm_caller.query_semantic_rule(prompt_15)
                # 如果大模型认为不相似，则标记为警告
                status_15 = "WARNING" if "否" in llm_response_15 else "PASS"
                self.audit_results.append(AuditResult(
                    rule_id="规则15", 
                    status=status_15, 
                    message=f"大模型分析结果: {llm_response_15}"
                ))
            except Exception as e:
                self.audit_results.append(AuditResult(
                    rule_id="规则15", 
                    status="FAIL", 
                    message=f"语义分析失败: {str(e)}"
                ))
        else:
            self.audit_results.append(AuditResult(
                rule_id="规则15", 
                status="FAIL", 
                message="缺少招待事由或项目名称信息"
            ))

        # 规则16：申报事由与消费内容逻辑一致性分析
        reimbursement_reason = data_context.all_extracted_data.get("业务招待事前审批表", {}).get("申报事由", "")
        expense_details = data_context.all_extracted_data.get("招待明细", {}).get("招待内容", "")
        
        if reimbursement_reason and expense_details:
            try:
                prompt_16 = self.llm_caller.get_rule_16_prompt(reimbursement_reason, expense_details)
                llm_response_16 = self.llm_caller.query_semantic_rule(prompt_16)
                # 如果大模型认为存在冲突，则标记为警告
                status_16 = "WARNING" if "是" in llm_response_16 else "PASS"
                self.audit_results.append(AuditResult(
                    rule_id="规则16", 
                    status=status_16, 
                    message=f"大模型分析结果: {llm_response_16}"
                ))
            except Exception as e:
                self.audit_results.append(AuditResult(
                    rule_id="规则16", 
                    status="FAIL", 
                    message=f"逻辑一致性分析失败: {str(e)}"
                ))

        # 规则28：发票地址风景名胜区或五星级酒店判断
        invoice_address = data_context.all_extracted_data.get("发票", {}).get("销售方地址", "")
        
        if invoice_address:
            try:
                prompt_28 = self.llm_caller.get_rule_28_prompt(invoice_address)
                llm_response_28 = self.llm_caller.query_semantic_rule(prompt_28)
                # 如果大模型认为是风景名胜区或五星级酒店，则标记为警告
                status_28 = "WARNING" if "是" in llm_response_28 else "PASS"
                self.audit_results.append(AuditResult(
                    rule_id="规则28", 
                    status=status_28, 
                    message=f"大模型分析结果: {llm_response_28}"
                ))
            except Exception as e:
                self.audit_results.append(AuditResult(
                    rule_id="规则28", 
                    status="FAIL", 
                    message=f"地址分析失败: {str(e)}"
                ))

    def generate_report_and_display(self, output_path: str):
        """生成最终的JSON格式审核报告，并自动在浏览器中打开可视化页面。"""
        report = {
            "summary": {
                "total_rules_checked": len(self.audit_results),
                "passed_count": len([r for r in self.audit_results if r.status == "PASS"]),
                "failed_count": len([r for r in self.audit_results if r.status == "FAIL"]),
                "warning_count": len([r for r in self.audit_results if r.status == "WARNING"]),
            },
            "details": [r.dict() for r in self.audit_results]
        }

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 1. 保存JSON报告文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=4)
        print(f"审核报告已生成: {output_path}")

        # 打印摘要信息
        print(f"\n审核摘要:")
        print(f"总规则数: {report['summary']['total_rules_checked']}")
        print(f"通过: {report['summary']['passed_count']}")
        print(f"失败: {report['summary']['failed_count']}")
        print(f"警告: {report['summary']['warning_count']}")

        # 标记最终完成状态
        self._update_progress('report-generation', 'audit-complete', '审核流程完成！')

        # 2. 自动打开HTML页面
        try:
            # 导入智能Web启动器
            import sys
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from auto_web_launcher import open_with_fallback

            print("[网络] 正在智能启动Web服务器并打开页面...")
            open_with_fallback(output_path)

        except ImportError:
            # 回退到原始方法
            current_dir = os.path.dirname(os.path.abspath(__file__))
            html_page_path = os.path.join(current_dir, '..', 'frontend', 'audit_viewer.html')

            if os.path.exists(html_page_path):
                http_url = "http://localhost:8000/frontend/audit_viewer.html"
                webbrowser.open(http_url)
                print(f"正在打开可视化页面: {http_url}")
                print("[提示] 如果页面显示CORS错误，请先运行: python start_web_server.py")
        except Exception as e:
            print(f"[警告] 打开页面失败: {e}")
            print("[提示] 请手动运行: python start_web_server.py")

    def generate_report(self, output_path: str):
        """生成最终的JSON格式审核报告（保持向后兼容）。"""
        self.generate_report_and_display(output_path)

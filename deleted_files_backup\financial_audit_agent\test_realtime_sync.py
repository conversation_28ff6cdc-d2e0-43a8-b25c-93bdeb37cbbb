#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前后端实时数据同步的脚本
"""

import json
import time
import requests
import threading
from pathlib import Path


def monitor_status_file():
    """监控状态文件的变化"""
    print("监控状态文件变化...")
    
    status_file = Path(__file__).parent / "backend" / "audit_status.json"
    last_modified = 0
    
    for i in range(20):  # 监控20次，每次间隔3秒
        try:
            if status_file.exists():
                current_modified = status_file.stat().st_mtime
                if current_modified != last_modified:
                    last_modified = current_modified
                    
                    with open(status_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    print(f"[{i+1:2d}] 状态文件更新:")
                    print(f"     步骤: {data.get('current_step', 'N/A')}")
                    print(f"     状态: {data.get('status', 'N/A')}")
                    print(f"     消息: {data.get('message', 'N/A')}")
                    print(f"     时间: {data.get('timestamp', 'N/A')}")
                    print()
                else:
                    print(f"[{i+1:2d}] 状态文件无变化")
            else:
                print(f"[{i+1:2d}] 状态文件不存在")
        except Exception as e:
            print(f"[{i+1:2d}] 读取状态文件失败: {e}")
        
        time.sleep(3)


def monitor_api_responses():
    """监控API响应的变化"""
    print("监控API响应变化...")
    
    last_response = None
    
    for i in range(20):  # 监控20次，每次间隔3秒
        try:
            response = requests.get('http://localhost:8001/api/status', timeout=3)
            if response.status_code == 200:
                data = response.json()
                
                # 检查响应是否有变化
                current_key = f"{data.get('current_step', '')}-{data.get('timestamp', '')}"
                if current_key != last_response:
                    last_response = current_key
                    
                    print(f"[{i+1:2d}] API响应更新:")
                    print(f"     步骤: {data.get('current_step', 'N/A')}")
                    print(f"     状态: {data.get('status', 'N/A')}")
                    print(f"     消息: {data.get('message', 'N/A')}")
                    print(f"     时间: {data.get('timestamp', 'N/A')}")
                    print(f"     API时间: {data.get('api_timestamp', 'N/A')}")
                    print()
                else:
                    print(f"[{i+1:2d}] API响应无变化")
            else:
                print(f"[{i+1:2d}] API请求失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"[{i+1:2d}] API连接失败: {e}")
        
        time.sleep(3)


def simulate_frontend_polling():
    """模拟前端轮询行为"""
    print("模拟前端轮询行为...")
    
    for i in range(10):  # 模拟10次轮询
        try:
            # 模拟前端的API调用
            response = requests.get('http://localhost:8001/api/status', timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"[前端轮询 {i+1:2d}] 获取到数据:")
                print(f"     当前步骤: {data.get('current_step', 'N/A')}")
                print(f"     执行状态: {data.get('status', 'N/A')}")
                print(f"     详细信息: {data.get('detail', 'N/A')}")
                
                # 检查是否有进度信息
                if 'final_stats' in data:
                    stats = data['final_stats']
                    print(f"     最终统计: 总计{stats.get('total', 0)}, 通过{stats.get('passed', 0)}, 失败{stats.get('failed', 0)}")
                
                print()
            else:
                print(f"[前端轮询 {i+1:2d}] API请求失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"[前端轮询 {i+1:2d}] 连接失败: {e}")
        
        time.sleep(3)


def check_sync_chain():
    """检查数据同步链路"""
    print("检查数据同步链路...")
    print("=" * 50)
    
    # 1. 检查后端状态文件
    status_file = Path(__file__).parent / "backend" / "audit_status.json"
    print("1. 后端状态文件:")
    if status_file.exists():
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"   ✅ 文件存在: {status_file}")
            print(f"   📅 修改时间: {time.ctime(status_file.stat().st_mtime)}")
            print(f"   📊 当前状态: {data.get('current_step', 'N/A')} - {data.get('status', 'N/A')}")
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
    else:
        print(f"   ❌ 文件不存在: {status_file}")
    
    print()
    
    # 2. 检查前端可访问的状态文件
    frontend_status_file = Path(__file__).parent / "audit_status.json"
    print("2. 前端状态文件:")
    if frontend_status_file.exists():
        try:
            with open(frontend_status_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"   ✅ 文件存在: {frontend_status_file}")
            print(f"   📅 修改时间: {time.ctime(frontend_status_file.stat().st_mtime)}")
            print(f"   📊 当前状态: {data.get('current_step', 'N/A')} - {data.get('status', 'N/A')}")
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
    else:
        print(f"   ❌ 文件不存在: {frontend_status_file}")
    
    print()
    
    # 3. 检查API服务器响应
    print("3. API服务器响应:")
    try:
        response = requests.get('http://localhost:8001/api/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API连接正常")
            print(f"   📊 返回状态: {data.get('current_step', 'N/A')} - {data.get('status', 'N/A')}")
            print(f"   🕐 API时间戳: {data.get('api_timestamp', 'N/A')}")
            print(f"   🕐 数据时间戳: {data.get('timestamp', 'N/A')}")
        else:
            print(f"   ❌ API请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ API连接失败: {e}")
    
    print()
    
    # 4. 检查Web服务器
    print("4. Web服务器:")
    try:
        response = requests.get('http://localhost:8002/frontend/ai_console.html', timeout=5)
        if response.status_code == 200:
            print(f"   ✅ Web服务器正常")
            print(f"   📄 页面大小: {len(response.text)} 字符")
        else:
            print(f"   ❌ Web服务器错误: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ Web服务器连接失败: {e}")


def test_realtime_updates():
    """测试实时更新功能"""
    print("\n" + "=" * 60)
    print("测试实时更新功能")
    print("=" * 60)
    
    # 创建测试状态更新
    status_file = Path(__file__).parent / "backend" / "audit_status.json"
    
    test_statuses = [
        {
            "current_step": "data-loading",
            "status": "running",
            "message": "正在加载数据...",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detail": "测试数据加载阶段"
        },
        {
            "current_step": "rule-parsing",
            "status": "running", 
            "message": "正在解析规则...",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detail": "测试规则解析阶段"
        },
        {
            "current_step": "audit-execution",
            "status": "running",
            "message": "正在执行审核...",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detail": "测试审核执行阶段"
        }
    ]
    
    print("模拟状态更新...")
    for i, status in enumerate(test_statuses):
        print(f"\n更新状态 {i+1}/3: {status['message']}")
        
        try:
            # 更新状态文件
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 状态文件已更新")
            
            # 等待一下，然后检查API响应
            time.sleep(1)
            response = requests.get('http://localhost:8001/api/status', timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"   📡 API返回: {data.get('current_step', 'N/A')} - {data.get('message', 'N/A')}")
            else:
                print(f"   ❌ API请求失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 更新失败: {e}")
        
        time.sleep(3)  # 等待3秒，模拟前端轮询间隔


def main():
    """主函数"""
    print("前后端实时数据同步测试")
    print("=" * 60)
    
    # 1. 检查同步链路
    check_sync_chain()
    
    # 2. 测试实时更新
    test_realtime_updates()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n建议:")
    print("1. 检查浏览器开发者工具的控制台日志")
    print("2. 观察网络面板中的API调用")
    print("3. 确认页面元素是否正确更新")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI财务审核系统统一启动器
一键启动前端Web服务、后端API服务和V2审核引擎
确保实时日志传递和完整审核流程
"""

import os
import sys
import json
import time
import subprocess
import threading
import webbrowser
import http.server
import socketserver
from pathlib import Path
from urllib.parse import urlparse, parse_qs

# 设置UTF-8编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Windows控制台编码设置
if os.name == 'nt':
    try:
        # 设置控制台代码页为UTF-8
        os.system('chcp 65001 >nul 2>&1')
    except:
        pass


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='AI财务审核系统统一启动器')
    parser.add_argument('--doc-num', type=str, help='文档编号')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    parser.add_argument('--report-only', action='store_true', help='仅启动报告查看服务器')

    args = parser.parse_args()

    project_root = Path(__file__).parent

    # 如果只是查看报告模式
    if args.report_only:
        print("📊 AI财务审核报告查看器")
        print("=" * 60)
        print("🎯 目标: 启动报告查看服务器")
        print("=" * 60)

        # 创建报告查看器
        report_viewer = ReportViewer(project_root)
        success = report_viewer.start_viewer()

        if not success:
            print("❌ 报告查看器启动失败")
            return 1

        # 打开浏览器
        if not args.no_browser:
            print("\n[浏览器] 自动打开报告查看器...")
            open_report_browser(args.doc_num)

        # 显示服务状态
        report_viewer.show_viewer_status()

        # 保持服务运行
        print("\n[运行] 报告查看服务正在运行...")
        print("[提示] 按 Ctrl+C 停止服务")

        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[停止] 正在停止报告查看服务...")
            report_viewer.stop_viewer()
            print("[完成] 服务已停止")

        return 0

    # 完整系统启动模式
    if not args.doc_num:
        print("❌ 完整系统启动模式需要指定文档编号")
        print("💡 使用 --doc-num 参数指定文档编号")
        print("💡 或使用 --report-only 参数仅启动报告查看器")
        return 1

    print("🚀 AI财务审核系统统一启动器")
    print("=" * 60)
    print(f"📋 文档编号: {args.doc_num}")
    print("🎯 目标: 同时启动前端、后端和审核引擎")
    print("=" * 60)

    # 创建系统管理器
    system_manager = SystemManager(project_root, args.doc_num)

    # 启动所有服务
    success = system_manager.start_all_services()

    if not success:
        print("❌ 系统启动失败")
        return 1

    # 打开浏览器
    if not args.no_browser:
        print("\n[浏览器] 自动打开AI控制台...")
        open_browser(args.doc_num)

    # 显示系统状态
    system_manager.show_system_status()

    # 保持服务运行
    print("\n[运行] 所有服务正在运行...")
    print("[提示] 按 Ctrl+C 停止所有服务")
    print("[监控] 实时监控审核进度和日志传递...")

    try:
        # 启动监控循环
        system_manager.monitor_system()
    except KeyboardInterrupt:
        print("\n[停止] 正在停止所有服务...")
        system_manager.stop_all_services()
        print("[完成] 所有服务已停止")

    return 0


class ReportViewer:
    """报告查看器 - 专门用于查看审核报告"""

    def __init__(self, project_root):
        self.project_root = project_root
        self.web_server = None
        self.api_server = None
        self.reports_dir = project_root / "audit_reports"

    def start_viewer(self):
        """启动报告查看器"""
        print("\n[启动] 开始启动报告查看服务...")

        # 1. 启动API服务器
        print("\n[1/2] 启动报告API服务器...")
        if not self.start_report_api():
            return False

        # 2. 启动Web服务器
        print("\n[2/2] 启动Web服务器...")
        if not self.start_web_server():
            return False

        print("\n✅ 报告查看器启动成功！")
        return True

    def start_report_api(self):
        """启动报告API服务器"""
        try:
            class ReportAPIHandler(http.server.BaseHTTPRequestHandler):
                def __init__(self, reports_dir, *args, **kwargs):
                    self.reports_dir = reports_dir
                    super().__init__(*args, **kwargs)

                def do_GET(self):
                    """处理GET请求"""
                    try:
                        if self.path.startswith('/api/reports'):
                            self.handle_reports_list()
                        elif self.path.startswith('/api/report/'):
                            self.handle_report_detail()
                        elif self.path.startswith('/api/health'):
                            self.handle_health()
                        else:
                            self.handle_not_found()
                    except Exception as e:
                        self.handle_error(str(e))

                def do_OPTIONS(self):
                    """处理CORS预检"""
                    self.send_response(200)
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    self.end_headers()

                def handle_reports_list(self):
                    """处理报告列表请求"""
                    try:
                        reports = []
                        if self.reports_dir.exists():
                            for report_file in self.reports_dir.glob("*.json"):
                                try:
                                    with open(report_file, 'r', encoding='utf-8') as f:
                                        report_data = json.load(f)

                                    reports.append({
                                        "filename": report_file.name,
                                        "doc_num": report_data.get("metadata", {}).get("doc_num", "未知"),
                                        "timestamp": report_data.get("metadata", {}).get("timestamp", "未知"),
                                        "summary": report_data.get("summary", {})
                                    })
                                except Exception as e:
                                    print(f"读取报告文件失败 {report_file}: {e}")

                        self.send_response(200)
                        self.send_header('Content-Type', 'application/json; charset=utf-8')
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.end_headers()

                        response_data = json.dumps(reports, ensure_ascii=False, indent=2)
                        self.wfile.write(response_data.encode('utf-8'))

                    except Exception as e:
                        self.handle_error(f"获取报告列表失败: {e}")

                def handle_report_detail(self):
                    """处理报告详情请求"""
                    try:
                        # 从路径中提取文件名
                        filename = self.path.split('/')[-1]
                        if not filename.endswith('.json'):
                            filename += '.json'

                        report_file = self.reports_dir / filename

                        if not report_file.exists():
                            self.send_response(404)
                            self.send_header('Content-Type', 'application/json; charset=utf-8')
                            self.send_header('Access-Control-Allow-Origin', '*')
                            self.end_headers()

                            error_data = {"error": "Report not found", "filename": filename}
                            response_data = json.dumps(error_data, ensure_ascii=False)
                            self.wfile.write(response_data.encode('utf-8'))
                            return

                        with open(report_file, 'r', encoding='utf-8') as f:
                            report_data = json.load(f)

                        self.send_response(200)
                        self.send_header('Content-Type', 'application/json; charset=utf-8')
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.end_headers()

                        response_data = json.dumps(report_data, ensure_ascii=False, indent=2)
                        self.wfile.write(response_data.encode('utf-8'))

                    except Exception as e:
                        self.handle_error(f"获取报告详情失败: {e}")

                def handle_health(self):
                    """处理健康检查"""
                    health_data = {
                        "status": "healthy",
                        "service": "AI财务审核报告查看器",
                        "version": "1.0",
                        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    }

                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    response_data = json.dumps(health_data, ensure_ascii=False, indent=2)
                    self.wfile.write(response_data.encode('utf-8'))

                def handle_not_found(self):
                    """处理404"""
                    self.send_response(404)
                    self.send_header('Content-Type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    error_data = {"error": "Not Found", "path": self.path}
                    response_data = json.dumps(error_data, ensure_ascii=False)
                    self.wfile.write(response_data.encode('utf-8'))

                def handle_error(self, error_message):
                    """处理错误"""
                    self.send_response(500)
                    self.send_header('Content-Type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    error_data = {"error": "Internal Server Error", "message": error_message}
                    response_data = json.dumps(error_data, ensure_ascii=False)
                    self.wfile.write(response_data.encode('utf-8'))

                def log_message(self, format_str, *args):
                    """静默日志"""
                    pass

            # 创建API服务器处理器
            def create_handler(*args, **kwargs):
                return ReportAPIHandler(self.reports_dir, *args, **kwargs)

            # 启动API服务器
            def run_api():
                try:
                    self.api_server = socketserver.TCPServer(("localhost", 8001), create_handler)
                    self.api_server.allow_reuse_address = True
                    self.api_server.serve_forever()
                except Exception as e:
                    print(f"   ❌ 报告API服务器运行失败: {e}")

            api_thread = threading.Thread(target=run_api, daemon=True)
            api_thread.start()

            # 等待启动
            time.sleep(2)

            print("   ✅ 报告API服务器启动成功 (端口8001)")
            return True

        except Exception as e:
            print(f"   ❌ 报告API服务器启动失败: {e}")
            return False


class SystemManager:
    """系统管理器 - 统一管理所有服务"""

    def __init__(self, project_root, doc_num):
        self.project_root = project_root
        self.doc_num = doc_num
        self.backend_dir = project_root / "backend"
        self.v2_dir = self.backend_dir / "auditor_v2"

        # 服务状态
        self.api_server = None
        self.web_server = None
        self.audit_process = None
        self.services_running = False

    def start_all_services(self):
        """启动所有服务"""
        print("\n[启动] 开始启动所有服务...")

        # 1. 重置状态
        print("\n[1/4] 重置审核状态...")
        if not self.reset_audit_status():
            return False

        # 2. 启动API服务器
        print("\n[2/4] 启动API服务器...")
        if not self.start_api_server():
            return False

        # 3. 启动Web服务器
        print("\n[3/4] 启动Web服务器...")
        if not self.start_web_server():
            return False

        # 4. 启动审核引擎
        print("\n[4/4] 启动V2审核引擎...")
        if not self.start_audit_engine():
            return False

        self.services_running = True
        print("\n✅ 所有服务启动成功！")
        return True

    def reset_audit_status(self):
        """重置审核状态"""
        try:
            # 创建初始状态
            state_data = {
                "audit_id": self.doc_num,
                "audit_status": "ready",
                "current_phase": "ready",
                "progress_percent": 0,
                "start_time": None,
                "completion_time": None,
                "summary": {
                    "total_rules": 0,
                    "completed_rules": 0,
                    "passed_rules": 0,
                    "failed_rules": 0,
                    "warning_rules": 0
                },
                "ai_thinking": "系统就绪，等待开始审核",
                "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "message": "系统就绪，等待开始审核",
                "detail": "V2审核引擎准备就绪"
            }

            # 确保目录存在
            self.backend_dir.mkdir(exist_ok=True)

            # 写入状态文件
            state_file = self.backend_dir / "audit_state.json"
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)

            print("   ✅ 审核状态已重置")
            return True

        except Exception as e:
            print(f"   ❌ 状态重置失败: {e}")
            return False

    def start_api_server(self):
        """启动API服务器"""
        try:
            # 创建内嵌的API服务器
            class APIHandler(http.server.BaseHTTPRequestHandler):
                def __init__(self, backend_dir, *args, **kwargs):
                    self.backend_dir = backend_dir
                    super().__init__(*args, **kwargs)

                def do_GET(self):
                    """处理GET请求"""
                    try:
                        if self.path.startswith('/api/status') or self.path.startswith('/api/state'):
                            self.handle_status()
                        elif self.path.startswith('/api/health'):
                            self.handle_health()
                        else:
                            self.handle_not_found()
                    except Exception as e:
                        self.handle_error(str(e))

                def do_OPTIONS(self):
                    """处理CORS预检"""
                    self.send_response(200)
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    self.end_headers()

                def handle_status(self):
                    """处理状态查询"""
                    try:
                        state_file = self.backend_dir / "audit_state.json"

                        if state_file.exists():
                            with open(state_file, 'r', encoding='utf-8') as f:
                                state_data = json.load(f)
                        else:
                            state_data = self.get_default_state()

                        # 设置响应头
                        self.send_response(200)
                        self.send_header('Content-Type', 'application/json; charset=utf-8')
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.end_headers()

                        # 返回JSON数据
                        response_data = json.dumps(state_data, ensure_ascii=False, indent=2)
                        self.wfile.write(response_data.encode('utf-8'))

                    except Exception as e:
                        self.handle_error(f"状态查询失败: {e}")

                def handle_health(self):
                    """处理健康检查"""
                    health_data = {
                        "status": "healthy",
                        "service": "AI财务审核系统API",
                        "version": "2.0",
                        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
                    }

                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    response_data = json.dumps(health_data, ensure_ascii=False, indent=2)
                    self.wfile.write(response_data.encode('utf-8'))

                def handle_not_found(self):
                    """处理404"""
                    self.send_response(404)
                    self.send_header('Content-Type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    error_data = {"error": "Not Found", "path": self.path}
                    response_data = json.dumps(error_data, ensure_ascii=False)
                    self.wfile.write(response_data.encode('utf-8'))

                def handle_error(self, error_message):
                    """处理错误"""
                    self.send_response(500)
                    self.send_header('Content-Type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()

                    error_data = {"error": "Internal Server Error", "message": error_message}
                    response_data = json.dumps(error_data, ensure_ascii=False)
                    self.wfile.write(response_data.encode('utf-8'))

                def get_default_state(self):
                    """获取默认状态"""
                    return {
                        "audit_id": None,
                        "audit_status": "ready",
                        "current_phase": "ready",
                        "progress_percent": 0,
                        "ai_thinking": "系统就绪",
                        "message": "等待开始审核"
                    }

                def log_message(self, format_str, *args):
                    """静默日志"""
                    pass

            # 创建API服务器处理器
            def create_handler(*args, **kwargs):
                return APIHandler(self.backend_dir, *args, **kwargs)

            # 启动API服务器
            def run_api():
                try:
                    self.api_server = socketserver.TCPServer(("localhost", 8001), create_handler)
                    self.api_server.allow_reuse_address = True
                    self.api_server.serve_forever()
                except Exception as e:
                    print(f"   ❌ API服务器运行失败: {e}")

            api_thread = threading.Thread(target=run_api, daemon=True)
            api_thread.start()

            # 等待启动
            time.sleep(2)

            print("   ✅ API服务器启动成功 (端口8001)")
            return True

        except Exception as e:
            print(f"   ❌ API服务器启动失败: {e}")
            return False

    def start_web_server(self):
        """启动Web服务器"""
        try:
            class WebHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, project_root, *args, **kwargs):
                    self.project_root = project_root
                    super().__init__(*args, **kwargs)

                def translate_path(self, path):
                    """转换路径"""
                    import posixpath
                    import urllib.parse

                    path = path.split('?', 1)[0]
                    path = path.split('#', 1)[0]
                    path = urllib.parse.unquote(path)
                    path = posixpath.normpath(path)

                    if path.startswith('/'):
                        path = path[1:]

                    full_path = self.project_root / path
                    return str(full_path)

                def end_headers(self):
                    """添加CORS头"""
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    super().end_headers()

                def log_message(self, format_str, *args):
                    """静默日志"""
                    pass

            # 创建Web服务器处理器
            def create_web_handler(*args, **kwargs):
                return WebHandler(self.project_root, *args, **kwargs)

            # 启动Web服务器
            def run_web():
                try:
                    os.chdir(self.project_root)
                    self.web_server = socketserver.TCPServer(("", 8002), create_web_handler)
                    self.web_server.allow_reuse_address = True
                    self.web_server.serve_forever()
                except Exception as e:
                    print(f"   ❌ Web服务器运行失败: {e}")

            web_thread = threading.Thread(target=run_web, daemon=True)
            web_thread.start()

            # 等待启动
            time.sleep(2)

            print("   ✅ Web服务器启动成功 (端口8002)")
            return True

        except Exception as e:
            print(f"   ❌ Web服务器启动失败: {e}")
            return False

    def start_audit_engine(self):
        """启动V2审核引擎"""
        try:
            # 检查V2引擎
            if not self.v2_dir.exists() or not (self.v2_dir / "run_audit_v2.py").exists():
                print(f"   ❌ V2引擎不存在: {self.v2_dir / 'run_audit_v2.py'}")
                return False

            # 启动审核引擎
            def run_audit():
                try:
                    # 切换到V2目录
                    original_cwd = os.getcwd()
                    os.chdir(self.v2_dir)

                    try:
                        # 构建命令
                        cmd = [
                            sys.executable, "run_audit_v2.py",
                            "--doc-num", self.doc_num,
                            "--rules", "../../业务招待费审核规则_V2.txt",
                            "--config", "../config.json",
                            "--output", f"../../audit_reports/audit_report_{self.doc_num}.json",
                            "--no-browser"
                        ]

                        print(f"   🚀 启动V2审核引擎...")
                        print(f"   📋 文档编号: {self.doc_num}")

                        # 设置环境变量
                        env = os.environ.copy()
                        env['PYTHONIOENCODING'] = 'utf-8'

                        # 执行审核（实时输出）
                        self.audit_process = subprocess.Popen(
                            cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.STDOUT,
                            text=True,
                            env=env,
                            encoding='utf-8',
                            errors='replace',
                            bufsize=1,
                            universal_newlines=True
                        )

                        # 实时输出审核日志
                        while True:
                            output = self.audit_process.stdout.readline()
                            if output == '' and self.audit_process.poll() is not None:
                                break
                            if output:
                                line = output.strip()
                                # 输出所有重要信息（不过滤）
                                if line.strip():  # 只要不是空行就输出
                                    # 根据内容类型添加不同的前缀
                                    if any(keyword in line for keyword in ['[启动]', '[成功]', '[完成]', '[清单]', '[状态]']):
                                        print(f"   📋 {line}")
                                    elif any(keyword in line for keyword in ['[错误]', '[调试]', '[警告]', 'ERROR', 'Exception', 'Traceback']):
                                        print(f"   🔍 {line}")
                                    elif any(keyword in line for keyword in ['[LLM]', '[LLM调试]']):
                                        print(f"   🤖 {line}")
                                    elif '[AI思考]' in line:
                                        print(f"   � {line}")
                                    elif any(keyword in line for keyword in ['[备用]', '[提示]']):
                                        print(f"   💡 {line}")
                                    elif line.startswith('='):
                                        print(f"   📄 {line}")
                                    else:
                                        print(f"   📝 {line}")

                        return_code = self.audit_process.poll()

                        if return_code == 0:
                            print("   ✅ V2审核引擎执行成功")
                        else:
                            print(f"   ❌ V2审核引擎执行失败，返回码: {return_code}")
                            print("   🔍 建议直接运行以下命令查看详细错误:")
                            print(f"   🔍 cd {self.v2_dir}")
                            print(f"   🔍 python run_audit_v2.py --doc-num {self.doc_num} --rules ../../业务招待费审核规则_V2.txt --config ../config.json --output ../../audit_reports/audit_report_{self.doc_num}.json --no-browser")

                    finally:
                        os.chdir(original_cwd)

                except Exception as e:
                    print(f"   ❌ 审核引擎执行异常: {e}")

            # 启动审核线程
            audit_thread = threading.Thread(target=run_audit, daemon=True)
            audit_thread.start()

            print("   ✅ V2审核引擎启动成功 (后台执行)")
            return True

        except Exception as e:
            print(f"   ❌ 审核引擎启动失败: {e}")
            return False

    def show_system_status(self):
        """显示系统状态"""
        print("\n" + "=" * 60)
        print("🎉 AI财务审核系统启动完成！")
        print("=" * 60)
        print("✅ API服务器: http://localhost:8001")
        print("   - 健康检查: http://localhost:8001/api/health")
        print("   - 状态查询: http://localhost:8001/api/status")
        print("✅ Web服务器: http://localhost:8002")
        print(f"✅ AI控制台: http://localhost:8002/frontend/ai_console.html?doc_num={self.doc_num}")
        print("✅ V2审核引擎: 后台执行中")
        print(f"✅ 审核报告: audit_reports/audit_report_{self.doc_num}.json")
        print("=" * 60)

    def monitor_system(self):
        """监控系统运行状态"""
        last_status_check = 0

        while self.services_running:
            try:
                current_time = time.time()

                # 每30秒检查一次系统状态
                if current_time - last_status_check > 30:
                    self.check_system_health()
                    last_status_check = current_time

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                print(f"[监控] 系统监控异常: {e}")
                time.sleep(10)

    def check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查状态文件更新
            state_file = self.backend_dir / "audit_state.json"
            if state_file.exists():
                with open(state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                status = state_data.get('audit_status', 'unknown')
                progress = state_data.get('progress_percent', 0)
                phase = state_data.get('current_phase', 'unknown')

                print(f"[监控] 审核状态: {status} | 进度: {progress}% | 阶段: {phase}")

                # 如果审核完成，显示结果
                if status == 'completed':
                    print("[监控] 🎉 审核流程已完成！")
                    self.show_audit_results()
            else:
                print("[监控] ⚠️ 状态文件不存在")

        except Exception as e:
            print(f"[监控] 健康检查失败: {e}")

    def show_audit_results(self):
        """显示审核结果"""
        try:
            report_file = self.project_root / "audit_reports" / f"audit_report_{self.doc_num}.json"
            if report_file.exists():
                with open(report_file, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)

                summary = report_data.get('summary', {})
                print(f"[结果] 总规则: {summary.get('total_rules', 0)}")
                print(f"[结果] 通过: {summary.get('passed_rules', 0)}")
                print(f"[结果] 失败: {summary.get('failed_rules', 0)}")
                print(f"[结果] 警告: {summary.get('warning_rules', 0)}")
            else:
                print("[结果] 审核报告文件未找到")

        except Exception as e:
            print(f"[结果] 读取审核结果失败: {e}")

    def stop_all_services(self):
        """停止所有服务"""
        self.services_running = False

        try:
            if self.audit_process and self.audit_process.poll() is None:
                print("[停止] 终止审核进程...")
                self.audit_process.terminate()

            if self.api_server:
                print("[停止] 关闭API服务器...")
                self.api_server.shutdown()
                self.api_server.server_close()

            if self.web_server:
                print("[停止] 关闭Web服务器...")
                self.web_server.shutdown()
                self.web_server.server_close()

        except Exception as e:
            print(f"[停止] 服务停止异常: {e}")


def open_browser(doc_num):
    """打开浏览器"""
    try:
        url = f"http://localhost:8002/frontend/ai_console.html?doc_num={doc_num}"
        webbrowser.open(url)
        print(f"   ✅ 浏览器已打开: AI控制台")
    except Exception as e:
        print(f"   ❌ 打开浏览器失败: {e}")
        print(f"   💡 请手动访问: http://localhost:8002/frontend/ai_console.html?doc_num={doc_num}")


if __name__ == "__main__":
    sys.exit(main())

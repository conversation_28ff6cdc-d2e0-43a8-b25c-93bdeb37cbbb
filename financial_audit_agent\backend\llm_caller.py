#!/usr/bin/env python3
"""
真实的LLM调用器 - 连接真实的大语言模型API
"""

import os
import json
import requests
import time
import logging
import hashlib
from typing import Optional, Dict, Any, Tuple
from data_models import DataContext


class LLMCallStats:
    """LLM调用统计类"""
    def __init__(self):
        self.total_calls = 0
        self.retry_calls = 0
        self.failed_calls = 0
        self.total_response_time = 0.0
        self.call_history = []

    def log_call_start(self, prompt_hash: str, attempt: int = 1):
        """记录调用开始"""
        self.total_calls += 1
        if attempt > 1:
            self.retry_calls += 1

        call_info = {
            'prompt_hash': prompt_hash[:8],
            'attempt': attempt,
            'start_time': time.time(),
            'status': 'started'
        }
        self.call_history.append(call_info)

        print(f"📊 [统计] 第{self.total_calls}次调用开始 - Hash: {prompt_hash[:8]}, 尝试: {attempt}")

    def log_call_success(self, response_length: int, elapsed_time: float):
        """记录调用成功"""
        self.total_response_time += elapsed_time
        if self.call_history:
            self.call_history[-1].update({
                'status': 'success',
                'response_length': response_length,
                'elapsed_time': elapsed_time
            })

        print(f"✅ [统计] 调用成功 - 响应长度: {response_length}, 耗时: {elapsed_time:.2f}s")

    def log_call_failure(self, error: str):
        """记录调用失败"""
        self.failed_calls += 1
        if self.call_history:
            self.call_history[-1].update({
                'status': 'failed',
                'error': str(error)
            })

        print(f"❌ [统计] 调用失败 - 错误: {error}")

    def get_stats(self) -> dict:
        """获取统计信息"""
        avg_response_time = self.total_response_time / max(self.total_calls - self.failed_calls, 1)
        return {
            "total_calls": self.total_calls,
            "retry_calls": self.retry_calls,
            "failed_calls": self.failed_calls,
            "success_rate": (self.total_calls - self.failed_calls) / max(self.total_calls, 1),
            "retry_rate": self.retry_calls / max(self.total_calls, 1),
            "avg_response_time": avg_response_time
        }

    def print_summary(self):
        """打印统计摘要"""
        stats = self.get_stats()
        print("\n📊 LLM调用统计摘要")
        print("=" * 40)
        print(f"总调用次数: {stats['total_calls']}")
        print(f"重试次数: {stats['retry_calls']}")
        print(f"失败次数: {stats['failed_calls']}")
        print(f"成功率: {stats['success_rate']:.1%}")
        print(f"重试率: {stats['retry_rate']:.1%}")
        print(f"平均响应时间: {stats['avg_response_time']:.2f}s")


# 全局统计实例
llm_stats = LLMCallStats()

class LLMCaller:
    def __init__(self, api_key: str, model_name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化LLM调用器

        Args:
            api_key: API密钥
            model_name: 模型名称
            config: 配置字典, 包含API模式、base_url等配置
        """
        self.api_key = api_key
        self.model = model_name
        self.config = config or {}

        # 确定使用的API模式
        self.api_mode = self.config.get('LLM_API_MODE', 'openai_compatible')
        self.base_url = self.config.get('LLM_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        self.temperature = self.config.get('LLM_TEMPERATURE', 0.7)
        self.max_tokens = self.config.get('LLM_MAX_TOKENS', 2000)
        
        # 设置请求头
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        print(f"🤖 [LLM] 初始化真实LLM调用器")
        print(f"🤖 [LLM] 模型: {self.model}")
        print(f"🤖 [LLM] API端点: {self.base_url}")

    def query_semantic_rule(self, prompt: str, max_retries: int = 3) -> str:
        """向大模型发送一个具体、有针对性的提示, 并返回响应文本。"""
        
        for attempt in range(max_retries):
            try:
                print(f"🤖 [LLM] 第{attempt + 1}次尝试调用语义规则查询")
                
                # 构建请求数据
                request_data = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "你是一个专业的财务审核助手。请根据提供的审核规则和数据，给出准确的审核结果。结果必须是有效的JSON格式。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens,
                    "stream": True  # 启用流式输出
                }
                
                # 发送请求
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=request_data,
                    timeout=30,
                    stream=True  # 启用流式响应
                )

                if response.status_code == 200:
                    # 处理流式响应
                    content = self._handle_stream_response(response)
                    if content:
                        print(f"✅ [LLM] 语义规则查询成功，响应长度: {len(content)}")
                        return content
                    else:
                        print(f"❌ [LLM] 流式响应处理失败")

                else:
                    print(f"❌ [LLM] API调用失败，状态码: {response.status_code}")
                    print(f"❌ [LLM] 错误信息: {response.text}")
                
            except Exception as e:
                print(f"❌ [LLM] 第{attempt + 1}次调用异常: {e}")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"⏳ [LLM] 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
        
        return f"错误: 达到最大重试次数，无法获取LLM响应"

    def query_with_reasoning(self, prompt: str, max_retries: int = 2) -> Tuple[str, str]:
        """
        向大模型发送提示并返回思维链和最终结果

        Returns:
            tuple: (思维链文本, 最终结果文本)
        """

        # 生成prompt hash用于统计和去重检测
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()

        for attempt in range(max_retries):
            try:
                # 记录调用开始
                llm_stats.log_call_start(prompt_hash, attempt + 1)
                call_start_time = time.time()

                print(f"🤖 [LLM] 第{attempt + 1}次尝试调用思维链查询 - Hash: {prompt_hash[:8]}")
                
                # 强化系统提示，明确要求思维链
                system_prompt = """你是一个专业的财务审核助手。请严格按照以下格式回答：

**第一部分：详细思考过程**
请详细展示你的分析思考过程，包括：
1. 如何理解和解析每条审核规则
2. 如何在JSON数据中查找相关信息
3. 如何进行逻辑判断和推理
4. 得出结论的思维过程

**第二部分：审核结果**
然后给出最终的审核结果JSON格式。

请确保思考过程详细、具体、逻辑性强, 让用户能够清楚地看到AI的推理过程。"""

                request_data = {
                    "model": self.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": system_prompt
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens,
                    "stream": True  # 启用流式输出
                }

                # 如果是thinking模型，添加特殊参数
                if "thinking" in self.model.lower():
                    # 根据阿里云文档，thinking模型支持流式输出
                    request_data["reasoning_effort"] = "medium"  # 推理强度
                
                # 发送请求
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=request_data,
                    timeout=60,
                    stream=True  # 启用流式响应
                )

                if response.status_code == 200:
                    # 处理流式响应，获取思维链和内容
                    stream_thinking, stream_content = self._handle_stream_response_with_thinking(response)
                    if stream_content:

                        # 优先使用流式响应中的思维链
                        if stream_thinking and len(str(stream_thinking).strip()) > 50:
                            print(f"🧠 [LLM] 流式思维链长度: {len(stream_thinking)}")
                            print(f"📝 [LLM] 流式结果长度: {len(stream_content)}")

                            # 记录调用成功
                            elapsed_time = time.time() - call_start_time
                            llm_stats.log_call_success(len(stream_content), elapsed_time)

                            return str(stream_thinking), stream_content
                        else:
                            # 从内容中分离思维链和结果
                            print(f"🔄 [LLM] 从流式内容中提取思维链")
                            thinking, result_text = self._extract_reasoning_from_content(stream_content)

                            # 记录调用成功
                            elapsed_time = time.time() - call_start_time
                            llm_stats.log_call_success(len(result_text), elapsed_time)

                            return thinking, result_text
                    else:
                        print(f"❌ [LLM] 响应格式异常: {result}")
                        
                else:
                    print(f"❌ [LLM] API调用失败，状态码: {response.status_code}")
                    print(f"❌ [LLM] 错误信息: {response.text}")
                
            except Exception as e:
                print(f"❌ [LLM] 第{attempt + 1}次调用异常: {e}")

                # 记录调用失败
                llm_stats.log_call_failure(str(e))

                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"⏳ [LLM] 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
        
        error_msg = "错误: 达到最大重试次数，无法获取LLM响应"
        return error_msg, error_msg

    def _extract_reasoning_from_content(self, content: str) -> Tuple[str, str]:
        """从响应内容中提取思维链和最终结果"""
        if not content:
            return "", ""
        
        print(f"🔄 [LLM] 开始从内容中提取思维链，内容长度: {len(content)}")
        
        # 尝试多种分割方式（注意冒号的差异）
        separators = [
            "**第二部分:审核结果**",      # 无冒号版本（实际使用的）
            "**第二部分：审核结果**",     # 有冒号版本
            "**审核结果**",
            "**最终结果**",
            "**结果**",
            "```json",
            "```",
            "[",
            "{"
        ]

        # 首先尝试标准格式分割（优先无冒号版本）
        for separator in ["**第二部分:审核结果**", "**第二部分：审核结果**"]:
            if separator in content:
                parts = content.split(separator)
                if len(parts) >= 2:
                    reasoning_part = parts[0].replace("**第一部分:详细思考过程**", "").replace("**第一部分：详细思考过程**", "").strip()
                    result = parts[1].strip()
                    reasoning = reasoning_part
                    print(f"✅ [LLM] 使用标准格式分割成功（{separator}），思维链长度: {len(reasoning)}")
                    return reasoning, result
        
        # 查找JSON开始位置
        import re
        json_pattern = r'\[.*?\]'
        json_match = re.search(json_pattern, content, re.DOTALL)
        if json_match:
            json_start = json_match.start()
            reasoning = content[:json_start].strip()
            result = content[json_start:].strip()
            print(f"✅ [LLM] 使用JSON位置分割成功，思维链长度: {len(reasoning)}")
            return reasoning, result
        
        # 最后的备用方案：将前70%作为思维链，后30%作为结果
        if len(content) > 100:
            split_point = int(len(content) * 0.7)
            reasoning = content[:split_point].strip()
            result = content[split_point:].strip()
            print(f"⚠️ [LLM] 使用比例分割，思维链长度: {len(reasoning)}")
        else:
            reasoning = "AI正在分析审核规则和数据，进行逻辑推理..."
            result = content
            print(f"⚠️ [LLM] 内容过短，使用默认思维链")
        
        return reasoning, result

    def _handle_stream_response(self, response) -> str:
        """处理流式响应，返回完整内容"""
        import json

        content = ""
        try:
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta and delta['content'] is not None:
                                    content += str(delta['content'])
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"❌ [LLM] 流式响应处理异常: {e}")
            return ""

        return content

    def _handle_stream_response_with_thinking(self, response) -> tuple:
        """处理流式响应，返回思维链和内容"""
        import json

        thinking_content = ""
        content = ""

        try:
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                choice = data['choices'][0]
                                delta = choice.get('delta', {})

                                # 检查思维链字段
                                if 'reasoning_content' in delta and delta['reasoning_content'] is not None:
                                    thinking_content += str(delta['reasoning_content'])
                                elif 'reasoning' in delta and delta['reasoning'] is not None:
                                    thinking_content += str(delta['reasoning'])

                                # 检查内容字段
                                if 'content' in delta and delta['content'] is not None:
                                    content += str(delta['content'])

                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"❌ [LLM] 流式响应处理异常: {e}")
            return "", ""

        # 如果没有获取到专门的思维链，从内容中提取
        if not thinking_content and content:
            print(f"🔄 [LLM] 从流式内容中提取思维链")
            thinking_content, content = self._extract_reasoning_from_content(content)

        return thinking_content, content

    def query_data_context(self, context: DataContext) -> str:
        """查询数据上下文"""
        prompt = f"分析数据上下文: {context}"
        return self.query_semantic_rule(prompt)

    @staticmethod
    def get_rule_15_prompt(reason_from_form: str, project_name_from_details: str) -> str:
        return f"""
        分析以下两个项目描述的语义相似度。
        - 描述A (从事前审批表): "{reason_from_form}"
        - 描述B (从招待明细): "{project_name_from_details}"
        这两个描述是否指向同一个项目或高度相关的活动?
        请仅回答"是"或"否",并附上简要解释。
        例如: 是,两者都指向同一个项目的施工阶段。
        """

    @staticmethod
    def get_rule_16_prompt(reimbursement_reason: str, expense_details: str) -> str:
        return f"""
        分析是否存在逻辑冲突。
        - 申报事由: "{reimbursement_reason}"
        - 实际消费内容: "{expense_details}"
        申报的事由与实际的消费内容之间是否存在逻辑矛盾或冲突?
        请仅回答"是"或"否",并附上简要解释。
        例如: 是,事由是"客户会议",但消费包含"KTV费用",存在冲突。
        """

    @staticmethod
    def get_rule_28_prompt(invoice_address: str) -> str:
        return f"""
        根据你的知识,判断地址"{invoice_address}"是否可能是一个风景名胜区或五星级酒店?
        请仅回答"是"或"否",如果知道,请指出具体可能是什么地方。
        例如: 是,这是华尔道夫酒店的地址。
        """

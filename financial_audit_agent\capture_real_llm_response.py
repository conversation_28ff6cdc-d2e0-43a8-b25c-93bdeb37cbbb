#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
捕获真实LLM响应脚本
用于分析实际的LLM响应格式，找出JSON解析失败的根本原因
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "backend" / "auditor_v2"))

def capture_llm_response():
    """捕获真实的LLM响应"""
    print("🎯 捕获真实LLM响应")
    print("=" * 80)
    
    try:
        from orchestrator_v2 import OrchestratorV2
        from llm_caller import LLMCaller
        from prompt_manager import PromptManager
        
        # 创建必要的组件
        llm_caller = LLMCaller()
        prompt_manager = PromptManager()
        
        # 模拟第一阶段的审核
        group_name = "第一部分：附件完整性检查"
        
        # 构建真实的规则文本（简化版）
        rules_text = """规则1：检查是否上传发票。指令：检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"发票"。
规则2：检查是否上传事前审批表。指令：检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"业务招待事前审批表"。"""
        
        # 构建真实的数据文本（简化版）
        data_text = """主报销单信息:
- 事由: 2025年4月18日，招待北京中指实证数据信息技术有限公司深圳分公司（中指研究院）有关人员，进行客户满意度行业数据及满意度提升方面的业务交流，来访8人，陪餐3人。

附件概览:
- 附件类型: 业务招待事前审批表, 发票, 餐饮小票, 支付记录"""
        
        context_summary = "这是第一阶段的附件完整性检查"
        
        # 生成提示词
        prompt = prompt_manager.create_step_prompt(
            group_name, rules_text, data_text, context_summary
        )
        
        print("📝 生成的提示词:")
        print("-" * 40)
        print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
        print("-" * 40)
        
        # 调用LLM获取响应
        print("\n🤖 调用LLM...")
        start_time = time.time()
        
        try:
            # 使用新的方法获取思维链和响应
            ai_thinking, response_str = llm_caller.query_with_reasoning(prompt)
            
            elapsed_time = time.time() - start_time
            print(f"✅ LLM调用成功，耗时: {elapsed_time:.2f}秒")
            
            # 保存原始响应到文件
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            # 保存AI思维链
            thinking_file = f"debug_ai_thinking_{timestamp}.txt"
            with open(thinking_file, 'w', encoding='utf-8') as f:
                f.write(ai_thinking)
            print(f"💾 AI思维链已保存到: {thinking_file}")
            
            # 保存响应内容
            response_file = f"debug_response_{timestamp}.txt"
            with open(response_file, 'w', encoding='utf-8') as f:
                f.write(response_str)
            print(f"💾 响应内容已保存到: {response_file}")
            
            return ai_thinking, response_str, thinking_file, response_file
            
        except Exception as e:
            print(f"❌ LLM调用失败: {e}")
            # 尝试传统方法
            try:
                response_str = llm_caller.query_semantic_rule(prompt)
                ai_thinking = "传统方法调用，无单独思维链"
                
                elapsed_time = time.time() - start_time
                print(f"✅ 传统方法调用成功，耗时: {elapsed_time:.2f}秒")
                
                # 保存响应
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                response_file = f"debug_response_traditional_{timestamp}.txt"
                with open(response_file, 'w', encoding='utf-8') as f:
                    f.write(response_str)
                print(f"💾 传统响应已保存到: {response_file}")
                
                return ai_thinking, response_str, None, response_file
                
            except Exception as e2:
                print(f"❌ 传统方法也失败: {e2}")
                return None, None, None, None
        
    except Exception as e:
        print(f"❌ 捕获失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def analyze_real_response(ai_thinking, response_str):
    """分析真实的LLM响应"""
    print("\n🔍 分析真实LLM响应")
    print("=" * 80)
    
    if not response_str:
        print("❌ 没有响应内容可分析")
        return False
    
    print(f"📊 响应基本信息:")
    print(f"   AI思维链长度: {len(ai_thinking) if ai_thinking else 0} 字符")
    print(f"   响应内容长度: {len(response_str)} 字符")
    print(f"   响应内容类型: {type(response_str)}")
    
    # 检查响应内容的结构
    print(f"\n📋 响应内容结构分析:")
    
    # 检查是否包含标准标记
    markers = [
        "第一部分：详细思考过程",
        "第二部分：审核结果",
        "**第一部分",
        "**第二部分",
        "思考过程",
        "审核结果"
    ]
    
    found_markers = []
    for marker in markers:
        if marker in response_str:
            pos = response_str.find(marker)
            found_markers.append((marker, pos))
            print(f"   ✅ 找到标记 '{marker}' 在位置 {pos}")
    
    if not found_markers:
        print("   ❌ 未找到任何标准标记")
    
    # 检查JSON结构
    print(f"\n🔧 JSON结构分析:")
    
    # 查找所有的 [ 和 ]
    bracket_positions = []
    for i, char in enumerate(response_str):
        if char in ['[', ']']:
            bracket_positions.append((char, i))
    
    print(f"   找到 {len(bracket_positions)} 个括号:")
    for char, pos in bracket_positions[:10]:  # 只显示前10个
        context = response_str[max(0, pos-20):pos+21]
        print(f"     {char} 在位置 {pos}: ...{context}...")
    
    if len(bracket_positions) > 10:
        print(f"     ... 还有 {len(bracket_positions) - 10} 个括号")
    
    # 尝试提取JSON
    print(f"\n🧪 测试JSON提取:")
    
    try:
        from orchestrator_v2 import OrchestratorV2
        orchestrator = OrchestratorV2(None, None, "DEBUG_REAL_001")
        
        # 测试当前的清理方法
        cleaned = orchestrator._clean_llm_response(response_str)
        print(f"   _clean_llm_response 结果长度: {len(cleaned)}")
        print(f"   前100字符: {cleaned[:100] if cleaned else '(空)'}")
        
        # 测试提取方法
        extracted = orchestrator._extract_json_from_response(response_str)
        print(f"   _extract_json_from_response 结果长度: {len(extracted)}")
        print(f"   前100字符: {extracted[:100] if extracted else '(空)'}")
        
        # 尝试JSON解析
        if extracted:
            try:
                parsed = json.loads(extracted)
                print(f"   ✅ JSON解析成功! 类型: {type(parsed)}")
                if isinstance(parsed, list):
                    print(f"   数组长度: {len(parsed)}")
                    for i, item in enumerate(parsed[:3]):  # 只显示前3个
                        print(f"     项目{i+1}: {item}")
                return True
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   错误位置: 第{e.lineno}行第{e.colno}列")
                
                # 显示错误位置附近的内容
                lines = extracted.split('\n')
                if e.lineno <= len(lines):
                    error_line = lines[e.lineno - 1]
                    print(f"   错误行内容: {error_line}")
                    if e.colno <= len(error_line):
                        print(f"   错误位置: {' ' * (e.colno - 1)}^")
                
                return False
        else:
            print(f"   ❌ 未提取到JSON内容")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试过程出错: {e}")
        return False

def provide_fix_recommendations(analysis_success):
    """提供修复建议"""
    print(f"\n🔧 修复建议")
    print("=" * 80)
    
    if analysis_success:
        print("✅ JSON解析在真实响应上成功了!")
        print("这意味着问题可能是:")
        print("1. 特定情况下的响应格式异常")
        print("2. 网络传输过程中的数据损坏")
        print("3. 并发访问导致的数据混乱")
        print("4. 特定规则或数据导致的LLM响应异常")
        
        print("\n建议的解决方案:")
        print("1. 增加更详细的调试日志")
        print("2. 添加响应内容的完整性检查")
        print("3. 实现更强的错误恢复机制")
        print("4. 添加响应内容的备份和重试机制")
        
    else:
        print("❌ JSON解析在真实响应上也失败了!")
        print("这确认了问题的存在，可能的原因:")
        print("1. LLM响应格式与预期不符")
        print("2. 提示词设计导致输出格式异常")
        print("3. JSON提取逻辑存在缺陷")
        print("4. 特殊字符或编码问题")
        
        print("\n建议的解决方案:")
        print("1. 改进JSON提取算法")
        print("2. 优化提示词设计，强化输出格式要求")
        print("3. 添加多种备用解析策略")
        print("4. 实现基于AI思维链的结果推断")

def main():
    """主函数"""
    print("🎯 真实LLM响应捕获与分析")
    print("=" * 100)
    
    # 切换到项目目录
    os.chdir(project_root)
    
    # 1. 捕获真实的LLM响应
    ai_thinking, response_str, thinking_file, response_file = capture_llm_response()
    
    if not response_str:
        print("❌ 无法获取LLM响应，分析终止")
        return 1
    
    # 2. 分析真实响应
    analysis_success = analyze_real_response(ai_thinking, response_str)
    
    # 3. 提供修复建议
    provide_fix_recommendations(analysis_success)
    
    # 4. 总结
    print(f"\n📊 分析总结")
    print("=" * 100)
    print(f"AI思维链: {'✅ 获取成功' if ai_thinking else '❌ 获取失败'}")
    print(f"响应内容: {'✅ 获取成功' if response_str else '❌ 获取失败'}")
    print(f"JSON解析: {'✅ 成功' if analysis_success else '❌ 失败'}")
    
    if thinking_file:
        print(f"思维链文件: {thinking_file}")
    if response_file:
        print(f"响应文件: {response_file}")
    
    print("\n下一步行动:")
    if analysis_success:
        print("1. 检查实际审核过程中的异常情况")
        print("2. 增强错误处理和日志记录")
        print("3. 实现更可靠的数据传输机制")
    else:
        print("1. 基于分析结果改进JSON提取逻辑")
        print("2. 优化提示词设计")
        print("3. 实现多层次的解析策略")
    
    return 0 if analysis_success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

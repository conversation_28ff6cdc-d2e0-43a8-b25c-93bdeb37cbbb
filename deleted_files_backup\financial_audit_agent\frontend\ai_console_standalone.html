<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI财务审核控制台 - 独立版</title>
    <link rel="stylesheet" href="ai_console.css">
    <style>
        /* 状态指示器 */
        .data-source {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
        }
        .success-indicator {
            background: rgba(0, 255, 0, 0.2);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- 数据源指示器 -->
    <div class="data-source success-indicator" id="data-source">
        ✅ 数据已加载 - 来源: audit_report_ZDBXD2025042900003.json
    </div>

    <!-- 动态粒子背景 -->
    <div id="particles-background"></div>
    
    <!-- 主控制台界面 -->
    <div class="console-container">
        <!-- 顶部状态栏 -->
        <header class="console-header">
            <div class="header-left">
                <div class="logo-section">
                    <div class="ai-logo">
                        <div class="logo-core"></div>
                        <div class="logo-ring"></div>
                        <div class="logo-pulse"></div>
                    </div>
                    <div class="system-title">
                        <h1>AI财务审核系统</h1>
                        <span class="version">v2.0 Neural Engine - 独立版</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="status-indicators">
                    <div class="status-item">
                        <span class="status-label">系统状态</span>
                        <span class="status-value online" id="system-status">已完成</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">文档编号</span>
                        <span class="status-value online" id="doc-number">ZDBXD2025042900003</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="console-main">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- AI思维过程 -->
                <div class="thinking-panel">
                    <div class="panel-header">
                        <h3>🧠 AI思维过程</h3>
                        <div class="neural-activity">
                            <div class="neuron active"></div>
                            <div class="neuron active"></div>
                            <div class="neuron active"></div>
                        </div>
                    </div>
                    <div class="thinking-content" id="thinking-content">
                        <div class="thinking-step">
                            <div class="step-indicator"></div>
                            <div class="step-text">✅ 数据加载完成 - 38条规则已分析</div>
                        </div>
                        <div class="thinking-step">
                            <div class="step-indicator"></div>
                            <div class="step-text">✅ 确定性规则检查完成 - 12条规则</div>
                        </div>
                        <div class="thinking-step">
                            <div class="step-indicator"></div>
                            <div class="step-text">✅ 关键词规则检查完成 - 14条规则</div>
                        </div>
                        <div class="thinking-step">
                            <div class="step-indicator"></div>
                            <div class="step-text">✅ AI语义规则检查完成 - 12条规则</div>
                        </div>
                        <div class="thinking-step">
                            <div class="step-indicator"></div>
                            <div class="step-text">🎯 审核完成 - 发现1个失败项，2个警告项</div>
                        </div>
                    </div>
                </div>

                <!-- 审核统计 -->
                <div class="stats-panel">
                    <div class="panel-header">
                        <h3>📊 审核统计</h3>
                    </div>
                    <div class="audit-statistics" id="audit-statistics">
                        <div class="stat-item">
                            <div class="stat-label">总规则数</div>
                            <div class="stat-value" id="total-rules">38</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">通过数</div>
                            <div class="stat-value success" id="passed-rules">35</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">失败数</div>
                            <div class="stat-value error" id="failed-rules">1</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">警告数</div>
                            <div class="stat-value warning" id="warning-rules">2</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">通过率</div>
                            <div class="stat-value success" id="pass-rate">92%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">风险率</div>
                            <div class="stat-value warning" id="risk-rate">8%</div>
                        </div>
                    </div>
                </div>

                <!-- 风险提醒 -->
                <div class="risk-panel">
                    <div class="panel-header">
                        <h3>⚠️ 风险提醒</h3>
                    </div>
                    <div class="risk-alerts">
                        <div class="risk-item high">
                            <div class="risk-icon">🚨</div>
                            <div class="risk-content">
                                <div class="risk-title">招待发起主体不一致</div>
                                <div class="risk-desc">规则8检查失败</div>
                            </div>
                        </div>
                        <div class="risk-item medium">
                            <div class="risk-icon">⚠️</div>
                            <div class="risk-content">
                                <div class="risk-title">需要关注的项目</div>
                                <div class="risk-desc">2个警告项需要人工复核</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 实时进度 -->
                <div class="progress-panel">
                    <div class="panel-header">
                        <h3>⚡ 审核进度</h3>
                    </div>
                    <div class="progress-content">
                        <div class="progress-bar" id="progress-bar">
                            <div class="progress-fill" style="width: 100%; background: linear-gradient(90deg, #00ff00, #00cc00);"></div>
                            <div class="progress-text">38/38</div>
                        </div>
                        <div class="current-status" id="current-status">
                            🎉 审核完成！所有规则检查已完成
                        </div>
                    </div>
                </div>

                <!-- 规则引擎状态 -->
                <div class="engines-panel">
                    <div class="panel-header">
                        <h3>🔧 规则引擎</h3>
                    </div>
                    <div class="engines-grid">
                        <div class="engine-card completed">
                            <div class="engine-icon">🎯</div>
                            <div class="engine-name">确定性规则</div>
                            <div class="engine-status">已完成</div>
                            <div class="engine-count">12/12</div>
                        </div>
                        <div class="engine-card completed">
                            <div class="engine-icon">🔍</div>
                            <div class="engine-name">关键词规则</div>
                            <div class="engine-status">已完成</div>
                            <div class="engine-count">14/14</div>
                        </div>
                        <div class="engine-card completed">
                            <div class="engine-icon">🧠</div>
                            <div class="engine-name">AI语义规则</div>
                            <div class="engine-status">已完成</div>
                            <div class="engine-count">12/12</div>
                        </div>
                    </div>
                </div>

                <!-- 详细结果 -->
                <div class="results-panel">
                    <div class="panel-header">
                        <h3>📋 详细结果</h3>
                    </div>
                    <div class="results-summary">
                        <div class="result-item">
                            <span class="result-label">审核时间:</span>
                            <span class="result-value" id="audit-time">2025-07-25 08:30:00</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">处理速度:</span>
                            <span class="result-value">38规则/分钟</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">数据源:</span>
                            <span class="result-value">audit_report_ZDBXD2025042900003.json</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script>
        // 嵌入的审核数据 - 来自 audit_report_ZDBXD2025042900003.json
        const auditData = {
            "summary": {
                "total_rules_checked": 38,
                "passed_count": 35,
                "failed_count": 1,
                "warning_count": 2
            },
            "metadata": {
                "doc_num": "ZDBXD2025042900003",
                "timestamp": "2025-07-25 08:30:00",
                "source": "embedded_data"
            }
        };

        // 初始化函数
        function initializeConsole() {
            console.log('🚀 AI财务审核控制台 - 独立版');
            console.log('📊 审核数据已嵌入，无需API连接');
            console.log('📋 数据摘要:', auditData.summary);
            
            // 更新时间戳
            const now = new Date();
            document.getElementById('audit-time').textContent = now.toLocaleString('zh-CN');
            
            // 添加动态效果
            addDynamicEffects();
            
            // 显示成功消息
            setTimeout(() => {
                console.log('✅ 控制台初始化完成');
                console.log('📈 通过率: 92% (35/38)');
                console.log('⚠️ 风险率: 8% (3/38)');
            }, 1000);
        }

        // 添加动态效果
        function addDynamicEffects() {
            // 数字动画效果
            const numbers = document.querySelectorAll('.stat-value');
            numbers.forEach(num => {
                if (num.textContent && !isNaN(parseInt(num.textContent))) {
                    animateNumber(num, parseInt(num.textContent));
                }
            });
            
            // 进度条动画
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = '0%';
                setTimeout(() => {
                    progressFill.style.transition = 'width 2s ease-in-out';
                    progressFill.style.width = '100%';
                }, 500);
            }
            
            // 神经元活动
            setInterval(() => {
                const neurons = document.querySelectorAll('.neuron');
                neurons.forEach(neuron => {
                    neuron.classList.toggle('active');
                });
            }, 2000);
        }

        // 数字动画
        function animateNumber(element, target) {
            let current = 0;
            const increment = target / 30;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 50);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeConsole, 500);
        });

        // 添加交互功能
        document.addEventListener('click', function(e) {
            if (e.target.closest('.stat-item')) {
                const item = e.target.closest('.stat-item');
                item.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    item.style.transform = 'scale(1)';
                }, 200);
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'r' || e.key === 'R') {
                location.reload();
            }
            if (e.key === 'd' || e.key === 'D') {
                console.log('🔧 调试信息:');
                console.log('数据:', auditData);
                console.log('DOM元素:', {
                    totalRules: document.getElementById('total-rules').textContent,
                    passedRules: document.getElementById('passed-rules').textContent,
                    failedRules: document.getElementById('failed-rules').textContent,
                    warningRules: document.getElementById('warning-rules').textContent
                });
            }
        });

        console.log('💡 提示: 按 R 刷新页面，按 D 显示调试信息');
    </script>
</body>
</html>

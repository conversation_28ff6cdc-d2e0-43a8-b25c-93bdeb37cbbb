#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一体化启动功能
验证Web服务器、API服务器和浏览器打开是否正常工作
"""

import time
import requests
import webbrowser
from pathlib import Path

def test_api_server(port):
    """测试API服务器"""
    print(f"🧪 测试API服务器 (端口 {port})")
    
    endpoints = [
        '/api/status',
        '/api/report', 
        '/api/rules',
        '/api/progress'
    ]
    
    for endpoint in endpoints:
        try:
            url = f"http://localhost:{port}{endpoint}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {endpoint} - 响应正常")
            else:
                print(f"   ❌ {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint} - 连接失败: {e}")

def test_web_server(port):
    """测试Web服务器"""
    print(f"🧪 测试Web服务器 (端口 {port})")
    
    pages = [
        '/frontend/ai_console.html',
        '/frontend/ai_results.html',
        '/frontend/audit_viewer.html'
    ]
    
    for page in pages:
        try:
            url = f"http://localhost:{port}{page}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {page} - 页面可访问")
            else:
                print(f"   ❌ {page} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {page} - 连接失败: {e}")

def test_browser_integration():
    """测试浏览器集成"""
    print("🧪 测试浏览器集成")
    
    # 检测常见端口
    ports_to_check = [8001, 8002, 8003, 8004]
    
    for port in ports_to_check:
        try:
            response = requests.get(f"http://localhost:{port}/api/status", timeout=2)
            if response.status_code == 200:
                print(f"   ✅ 检测到API服务器: 端口 {port}")
                return port
        except:
            continue
    
    print("   ❌ 未检测到API服务器")
    return None

def main():
    """主函数"""
    print("🚀 一体化启动功能测试")
    print("=" * 50)
    
    print("💡 请先运行以下命令启动一体化服务:")
    print("   python start_backend_v2.py --doc-num ZDBXD2025051300001")
    print()
    
    input("按回车键开始测试...")
    print()
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    # 测试API服务器
    api_port = test_browser_integration()
    if api_port:
        test_api_server(api_port)
    
    print()
    
    # 测试Web服务器
    web_ports = [8002, 8003, 8004, 8005]
    for port in web_ports:
        try:
            response = requests.get(f"http://localhost:{port}/frontend/ai_console.html", timeout=2)
            if response.status_code == 200:
                print(f"✅ 检测到Web服务器: 端口 {port}")
                test_web_server(port)
                break
        except:
            continue
    else:
        print("❌ 未检测到Web服务器")
    
    print()
    print("📊 测试完成")
    print("💡 如果所有测试通过，说明一体化启动功能正常工作")

if __name__ == "__main__":
    main()

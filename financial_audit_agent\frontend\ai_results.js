// AI审核结果页面JavaScript逻辑
class AIResultsViewer {
    constructor() {
        this.auditData = null;
        this.currentFilter = 'all';
        this.animationDelay = 100;
        
        this.init();
    }
    
    init() {
        this.updateTime();
        this.setupEventListeners();
        this.loadAuditResults();
        this.startCosmicAnimation();
        
        // 每秒更新时间
        setInterval(() => this.updateTime(), 1000);
    }
    
    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        document.getElementById('generation-time').textContent = timeString;
        
        // 模拟分析耗时
        const duration = '2.3秒';
        document.getElementById('analysis-duration').textContent = duration;
    }
    
    setupEventListeners() {
        // 筛选按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });
        
        // 导出报告按钮
        document.getElementById('export-report').addEventListener('click', () => {
            this.showExportModal();
        });
        
        // 新建审核按钮
        document.getElementById('new-audit').addEventListener('click', () => {
            this.startNewAudit();
        });
        
        // 返回控制台按钮
        document.getElementById('view-console').addEventListener('click', () => {
            window.open('ai_console.html', '_blank');
        });
        
        // 关闭导出模态框
        document.getElementById('close-export-modal').addEventListener('click', () => {
            document.getElementById('export-modal').style.display = 'none';
        });
        
        // 导出选项
        document.querySelectorAll('.export-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const format = e.currentTarget.dataset.format;
                this.exportReport(format);
            });
        });

        // AI思维链相关事件监听器
        this.setupThinkingChainListeners();
    }

    setupThinkingChainListeners() {
        // 展开全部按钮
        document.getElementById('expand-all-btn').addEventListener('click', () => {
            this.expandAllPhases();
        });

        // 收起全部按钮
        document.getElementById('collapse-all-btn').addEventListener('click', () => {
            this.collapseAllPhases();
        });

        // 复制内容按钮
        document.getElementById('copy-thinking-btn').addEventListener('click', () => {
            this.copyThinkingContent();
        });

        // 搜索功能
        document.getElementById('thinking-search-btn').addEventListener('click', () => {
            this.searchThinkingContent();
        });

        document.getElementById('thinking-search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchThinkingContent();
            }
        });

        // 实时搜索
        document.getElementById('thinking-search-input').addEventListener('input', (e) => {
            if (e.target.value.length > 2) {
                this.searchThinkingContent();
            } else {
                this.clearSearchResults();
            }
        });
    }
    
    startCosmicAnimation() {
        // 添加动态星星效果
        const cosmicBg = document.getElementById('cosmic-background');
        
        setInterval(() => {
            this.createShootingStar(cosmicBg);
        }, 3000);
    }
    
    createShootingStar(container) {
        const star = document.createElement('div');
        star.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: linear-gradient(45deg, #00d4ff, #ffffff);
            border-radius: 50%;
            top: ${Math.random() * 50}%;
            left: -10px;
            box-shadow: 0 0 10px #00d4ff;
            animation: shootingStar 2s linear forwards;
        `;
        
        container.appendChild(star);
        
        // 添加流星尾迹
        const trail = document.createElement('div');
        trail.style.cssText = `
            position: absolute;
            width: 50px;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            top: ${star.style.top};
            left: -60px;
            animation: shootingTrail 2s linear forwards;
        `;
        
        container.appendChild(trail);
        
        // 清理元素
        setTimeout(() => {
            star.remove();
            trail.remove();
        }, 2000);
        
        // 添加流星动画CSS
        if (!document.getElementById('shooting-star-styles')) {
            const style = document.createElement('style');
            style.id = 'shooting-star-styles';
            style.textContent = `
                @keyframes shootingStar {
                    0% { transform: translateX(0) translateY(0); opacity: 0; }
                    10% { opacity: 1; }
                    90% { opacity: 1; }
                    100% { transform: translateX(100vw) translateY(50px); opacity: 0; }
                }
                @keyframes shootingTrail {
                    0% { transform: translateX(0) translateY(0); opacity: 0; }
                    10% { opacity: 0.8; }
                    90% { opacity: 0.8; }
                    100% { transform: translateX(100vw) translateY(50px); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    async loadAuditResults() {
        try {
            // 显示加载状态
            this.showLoadingState();

            // 模拟加载延迟，增加仪式感
            await this.sleep(2000);

            // 获取URL参数中的单据编号
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') ||
                                  urlParams.get('number') || urlParams.get('doc-num');

            // 如果有单据编号参数，优先尝试对应的文件
            if (documentNumber) {
                console.log(`🔍 检测到单据编号参数: ${documentNumber}`);
                const specificPath = `../audit_reports/audit_report_${documentNumber}.json`;

                try {
                    console.log(`🔄 尝试加载特定单据报告: ${specificPath}`);
                    const response = await fetch(specificPath);
                    if (response.ok) {
                        console.log(`✅ 成功加载单据报告: ${specificPath}`);
                        this.auditData = await response.json();
                        this.displayResults();
                        return;
                    } else {
                        // 特定单据文件不存在，显示未审核提示
                        console.log(`❌ 单据 ${documentNumber} 的审核报告不存在`);
                        this.showNotAuditedState(documentNumber);
                        return;
                    }
                } catch (error) {
                    // 特定单据文件加载失败，显示未审核提示
                    console.log(`❌ 单据 ${documentNumber} 的审核报告加载失败:`, error);
                    this.showNotAuditedState(documentNumber);
                    return;
                }
            }

            // 没有单据编号参数，尝试加载默认的审核报告文件
            console.log('⚠️ 未提供单据编号参数，尝试加载默认报告文件');
            await this.loadDefaultAuditReport();

        } catch (error) {
            console.log('❌ 加载过程发生错误:', error);
            this.showErrorState(error.message);
        }
    }

    async loadDefaultAuditReport() {
        // 尝试加载可能存在的审核报告文件
        const possibleFiles = [
            '../audit_reports/audit_report_123.json',
            '../audit_reports/audit_report_ZDBXD2025042900003.json',
            '../audit_reports/enhanced_audit_report_ZDBXD2025042900003.json',
            '../audit_reports/demo_enhanced_report.json'
        ];

        for (const filePath of possibleFiles) {
            try {
                console.log(`🔄 尝试加载默认报告: ${filePath}`);
                const response = await fetch(filePath);
                if (response.ok) {
                    console.log(`✅ 成功加载默认报告: ${filePath}`);
                    this.auditData = await response.json();
                    this.displayResults();
                    return;
                }
            } catch (error) {
                console.log(`❌ 加载 ${filePath} 失败:`, error);
                continue;
            }
        }

        // 如果所有文件都加载失败，显示提示信息
        console.log('❌ 未找到可用的审核报告文件');
        this.showNoDocumentNumberState();
    }
    
    showLoadingState() {
        // 更新状态显示
        document.getElementById('status-title').textContent = '分析中...';
        document.getElementById('status-description').textContent = 'AI正在深度分析审核数据';

        // 显示加载动画
        const statusIcon = document.querySelector('.icon-core');
        statusIcon.style.background = 'var(--accent-blue)';
        statusIcon.style.animation = 'iconCorePulse 1s ease-in-out infinite';
    }

    showNotAuditedState(documentNumber) {
        // 显示未审核状态
        const statusIcon = document.querySelector('.icon-core');
        const statusTitle = document.getElementById('status-title');
        const statusDesc = document.getElementById('status-description');

        statusIcon.style.background = 'var(--accent-orange)';
        statusIcon.style.animation = 'none';
        statusTitle.textContent = '未进行智能审核';
        statusTitle.style.color = 'var(--accent-orange)';
        statusDesc.textContent = `单据编号 "${documentNumber}" 尚未进行AI智能审核，请先完成审核流程`;

        // 隐藏统计数据和详细结果
        this.hideResultsContent();

        // 显示提示信息
        this.showNotAuditedMessage(documentNumber);
    }

    showNoDocumentNumberState() {
        // 显示缺少单据编号状态
        const statusIcon = document.querySelector('.icon-core');
        const statusTitle = document.getElementById('status-title');
        const statusDesc = document.getElementById('status-description');

        statusIcon.style.background = 'var(--accent-red)';
        statusIcon.style.animation = 'none';
        statusTitle.textContent = '缺少单据编号';
        statusTitle.style.color = 'var(--accent-red)';
        statusDesc.textContent = '请在URL中提供单据编号参数（如：?doc=12345）';

        // 隐藏统计数据和详细结果
        this.hideResultsContent();

        // 显示使用说明
        this.showUsageInstructions();
    }

    showErrorState(errorMessage) {
        // 显示错误状态
        const statusIcon = document.querySelector('.icon-core');
        const statusTitle = document.getElementById('status-title');
        const statusDesc = document.getElementById('status-description');

        statusIcon.style.background = 'var(--accent-red)';
        statusIcon.style.animation = 'none';
        statusTitle.textContent = '加载失败';
        statusTitle.style.color = 'var(--accent-red)';
        statusDesc.textContent = `系统错误：${errorMessage}`;

        // 隐藏统计数据和详细结果
        this.hideResultsContent();
    }
    
    generateMockData() {
        return {
            summary: {
                total_rules_checked: 38,
                passed_count: 35,
                failed_count: 1,
                warning_count: 2
            },
            details: [
                { rule_id: '规则1：检查发票上传', status: 'PASS', message: '发票已正确上传并验证通过' },
                { rule_id: '规则7：检查招待对象', status: 'WARNING', message: '招待对象中包含敏感关键词，建议人工复核' },
                { rule_id: '规则15：语义一致性分析', status: 'FAIL', message: '申报事由与实际消费内容存在逻辑冲突' },
                { rule_id: '规则20：高档场所检测', status: 'WARNING', message: '检测到高档消费场所，需要额外审批' }
            ],
            review_comments: '经AI智能分析，发现2个警告项和1个失败项，建议人工进一步审核。'
        };
    }
    
    async displayResults() {
        const summary = this.auditData.summary;
        
        // 更新整体状态
        this.updateOverallStatus(summary);
        
        // 延迟显示统计数据，增加动画效果
        await this.sleep(500);
        this.updateStatistics(summary);
        
        // 延迟显示AI洞察
        await this.sleep(800);
        this.updateAIInsights();
        
        // 延迟显示详细结果
        await this.sleep(1000);
        this.updateDetailedResults();

        // 自动加载AI思维链内容
        await this.sleep(1200);
        console.log('🚀 开始自动加载AI思维链内容...');
        this.autoLoadThinkingChain();
    }
    
    updateOverallStatus(summary) {
        const statusIcon = document.querySelector('.icon-core');
        const statusTitle = document.getElementById('status-title');
        const statusDesc = document.getElementById('status-description');
        
        if (summary.failed_count > 0) {
            statusIcon.style.background = 'var(--accent-red)';
            statusTitle.textContent = '审核未通过';
            statusDesc.textContent = `发现 ${summary.failed_count} 个严重问题，需要立即处理`;
            statusTitle.style.color = 'var(--accent-red)';
        } else if (summary.warning_count > 0) {
            statusIcon.style.background = 'var(--accent-orange)';
            statusTitle.textContent = '需要关注';
            statusDesc.textContent = `发现 ${summary.warning_count} 个警告项，建议人工复核`;
            statusTitle.style.color = 'var(--accent-orange)';
        } else {
            statusIcon.style.background = 'var(--accent-green)';
            statusTitle.textContent = '审核通过';
            statusDesc.textContent = '所有规则检查通过，符合财务合规要求';
            statusTitle.style.color = 'var(--accent-green)';
        }
        
        // 添加完成动画
        statusIcon.style.animation = 'iconCorePulse 2s ease-in-out 3';
    }
    
    async updateStatistics(summary) {
        const stats = [
            { id: 'total-count', value: summary.total_rules_checked },
            { id: 'passed-count', value: summary.passed_count },
            { id: 'warning-count', value: summary.warning_count },
            { id: 'failed-count', value: summary.failed_count }
        ];
        
        // 逐个显示统计数据，增加动画效果
        for (let i = 0; i < stats.length; i++) {
            await this.sleep(200);
            this.animateCounter(stats[i].id, stats[i].value);
        }
    }
    
    animateCounter(elementId, targetValue) {
        const element = document.getElementById(elementId);
        const duration = 1000;
        const steps = 30;
        const increment = targetValue / steps;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= targetValue) {
                current = targetValue;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, duration / steps);
    }
    
    async updateAIInsights() {
        const insightsContent = document.getElementById('insights-content');
        
        // 生成AI洞察内容
        const insights = this.generateAIInsights();
        
        insightsContent.innerHTML = `
            <div class="insights-text">
                <div class="insight-item">
                    <div class="insight-icon">🎯</div>
                    <div class="insight-content">
                        <h4>风险评估</h4>
                        <p>${insights.riskAssessment}</p>
                    </div>
                </div>
                <div class="insight-item">
                    <div class="insight-icon">💡</div>
                    <div class="insight-content">
                        <h4>智能建议</h4>
                        <p>${insights.recommendation}</p>
                    </div>
                </div>
                <div class="insight-item">
                    <div class="insight-icon">📊</div>
                    <div class="insight-content">
                        <h4>合规评分</h4>
                        <p>${insights.complianceScore}</p>
                    </div>
                </div>
            </div>
        `;
        
        // 添加洞察样式
        if (!document.getElementById('insights-styles')) {
            const style = document.createElement('style');
            style.id = 'insights-styles';
            style.textContent = `
                .insights-text {
                    display: flex;
                    flex-direction: column;
                    gap: 20px;
                }
                .insight-item {
                    display: flex;
                    align-items: flex-start;
                    gap: 15px;
                    padding: 20px;
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 12px;
                    border-left: 3px solid var(--accent-purple);
                }
                .insight-icon {
                    font-size: 1.5rem;
                    margin-top: 5px;
                }
                .insight-content h4 {
                    color: var(--accent-purple);
                    margin-bottom: 8px;
                    font-size: 1.1rem;
                }
                .insight-content p {
                    color: var(--text-secondary);
                    line-height: 1.5;
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    generateAIInsights() {
        const summary = this.auditData.summary;
        const totalRules = summary.total_rules_checked;
        const passedRules = summary.passed_count;
        const complianceRate = Math.round((passedRules / totalRules) * 100);
        
        return {
            riskAssessment: summary.failed_count > 0 
                ? '检测到高风险项目，建议暂停审批流程并进行人工审核。'
                : summary.warning_count > 0 
                    ? '存在中等风险，建议加强监控并定期复查。'
                    : '风险等级较低，符合标准审批流程要求。',
            recommendation: summary.failed_count > 0
                ? '建议立即联系申请人补充相关材料，并重新提交审核。'
                : summary.warning_count > 0
                    ? '建议财务部门进行二次审核，确保合规性。'
                    : '可以正常进入审批流程，建议加快处理速度。',
            complianceScore: `合规评分：${complianceRate}/100，${complianceRate >= 90 ? '优秀' : complianceRate >= 80 ? '良好' : complianceRate >= 70 ? '一般' : '需改进'}`
        };
    }
    
    updateDetailedResults() {
        const tbody = document.getElementById('results-tbody');
        const details = this.auditData.details;
        
        tbody.innerHTML = '';
        
        details.forEach((item, index) => {
            setTimeout(() => {
                const row = this.createResultRow(item);
                tbody.appendChild(row);
                
                // 添加进入动画
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.5s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    }
    
    createResultRow(item) {
        const row = document.createElement('tr');
        row.dataset.status = item.status;



        const statusClass = {
            'PASS': 'pass',
            'WARNING': 'warning',
            'FAIL': 'fail',
            'UNKNOWN': 'unknown'
        }[item.status] || 'unknown';

        const statusText = {
            'PASS': '通过',
            'WARNING': '警告',
            'FAIL': '失败',
            'UNKNOWN': '无法判断'
        }[item.status] || '未知状态';

        const riskLevel = {
            'PASS': 'low',
            'WARNING': 'medium',
            'FAIL': 'high',
            'UNKNOWN': 'medium'
        }[item.status] || 'medium';
        
        const riskText = {
            'low': '低风险',
            'medium': '中风险',
            'high': '高风险'
        }[riskLevel];
        
        const reasonText = item.reason || '无详细信息';
        const truncatedReason = reasonText.length > 100 ? reasonText.substring(0, 100) + '...' : reasonText;

        row.innerHTML = `
            <td>
                <div class="status-badge ${statusClass}">
                    <div class="status-icon"></div>
                    ${statusText}
                </div>
            </td>
            <td>${item.rule_id}</td>
            <td class="reason-cell">
                <div class="reason-content" title="${this.escapeHtml(reasonText)}">
                    <span class="reason-text">${this.escapeHtml(truncatedReason)}</span>
                    ${reasonText.length > 100 ? '<button class="expand-btn" onclick="this.parentElement.parentElement.classList.toggle(\'expanded\')">展开</button>' : ''}
                </div>
                ${reasonText.length > 100 ? `<div class="reason-full">${this.escapeHtml(reasonText)}</div>` : ''}
            </td>
            <td>
                <div class="risk-level ${riskLevel}">${riskText}</div>
            </td>
        `;
        
        return row;
    }
    
    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        // 过滤表格行
        const rows = document.querySelectorAll('#results-tbody tr');
        rows.forEach(row => {
            if (filter === 'all' || row.dataset.status === filter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    
    showExportModal() {
        document.getElementById('export-modal').style.display = 'flex';
    }
    
    exportReport(format) {
        // 关闭模态框
        document.getElementById('export-modal').style.display = 'none';
        
        // 显示成功提示
        this.showSuccessToast(`报告已导出为 ${format.toUpperCase()} 格式`);
        
        // 模拟导出过程
        console.log(`导出格式: ${format}`);
        
        // 实际导出逻辑可以在这里实现
        if (format === 'json') {
            this.downloadJSON();
        } else if (format === 'pdf') {
            this.generatePDF();
        } else if (format === 'excel') {
            this.generateExcel();
        }
    }
    
    downloadJSON() {
        const dataStr = JSON.stringify(this.auditData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'audit_report.json';
        link.click();
        URL.revokeObjectURL(url);
    }
    
    generatePDF() {
        // PDF生成逻辑
        console.log('生成PDF报告...');
    }
    
    generateExcel() {
        // Excel生成逻辑
        console.log('生成Excel报告...');
    }
    
    startNewAudit() {
        this.showSuccessToast('正在启动新的审核流程...');
        setTimeout(() => {
            window.open('ai_console.html', '_blank');
        }, 1000);
    }
    
    showSuccessToast(message) {
        const toast = document.getElementById('success-toast');
        const messageEl = document.querySelector('.toast-message');
        
        messageEl.textContent = message;
        toast.style.display = 'block';
        
        setTimeout(() => {
            toast.style.display = 'none';
        }, 3000);
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    hideResultsContent() {
        // 隐藏统计数据
        const statsSection = document.querySelector('.stats-dashboard');
        if (statsSection) {
            statsSection.style.display = 'none';
        }

        // 隐藏AI洞察
        const insightsSection = document.querySelector('.ai-insights');
        if (insightsSection) {
            insightsSection.style.display = 'none';
        }

        // 隐藏详细结果
        const resultsSection = document.querySelector('.detailed-results');
        if (resultsSection) {
            resultsSection.style.display = 'none';
        }
    }

    showNotAuditedMessage(documentNumber) {
        // 在AI洞察区域显示未审核提示
        const insightsContent = document.getElementById('insights-content');
        const insightsSection = document.querySelector('.ai-insights');

        if (insightsSection) {
            insightsSection.style.display = 'block';
        }

        insightsContent.innerHTML = `
            <div class="not-audited-message">
                <div class="message-icon">📋</div>
                <div class="message-content">
                    <h3>单据未审核</h3>
                    <p>单据编号 <strong>"${documentNumber}"</strong> 尚未进行AI智能审核。</p>
                    <div class="next-steps">
                        <h4>下一步操作：</h4>
                        <ul>
                            <li>🔄 前往控制台启动智能审核流程</li>
                            <li>📊 等待审核完成后查看结果</li>
                            <li>📝 确认单据编号是否正确</li>
                        </ul>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="window.open('ai_console.html?doc_num=${documentNumber}', '_blank')">
                            <span class="btn-icon">🚀</span>
                            <span class="btn-text">启动审核</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addNotAuditedStyles();
    }

    showUsageInstructions() {
        // 在AI洞察区域显示使用说明
        const insightsContent = document.getElementById('insights-content');
        const insightsSection = document.querySelector('.ai-insights');

        if (insightsSection) {
            insightsSection.style.display = 'block';
        }

        insightsContent.innerHTML = `
            <div class="usage-instructions">
                <div class="message-icon">💡</div>
                <div class="message-content">
                    <h3>使用说明</h3>
                    <p>请在URL中提供单据编号参数以查看对应的审核结果。</p>
                    <div class="url-examples">
                        <h4>URL参数示例：</h4>
                        <div class="example-list">
                            <div class="example-item">
                                <code>ai_results.html?doc=12345</code>
                                <span class="example-desc">使用 doc 参数</span>
                            </div>
                            <div class="example-item">
                                <code>ai_results.html?document=DOC001</code>
                                <span class="example-desc">使用 document 参数</span>
                            </div>
                            <div class="example-item">
                                <code>ai_results.html?number=2024-001</code>
                                <span class="example-desc">使用 number 参数</span>
                            </div>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="window.open('ai_console.html', '_blank')">
                            <span class="btn-icon">🖥️</span>
                            <span class="btn-text">前往控制台</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addUsageInstructionsStyles();
    }

    addNotAuditedStyles() {
        if (!document.getElementById('not-audited-styles')) {
            const style = document.createElement('style');
            style.id = 'not-audited-styles';
            style.textContent = `
                .not-audited-message {
                    display: flex;
                    align-items: flex-start;
                    gap: 20px;
                    padding: 30px;
                    background: rgba(255, 165, 0, 0.1);
                    border-radius: 15px;
                    border-left: 4px solid var(--accent-orange);
                    margin: 20px 0;
                }
                .not-audited-message .message-icon {
                    font-size: 3rem;
                    margin-top: 10px;
                }
                .not-audited-message .message-content h3 {
                    color: var(--accent-orange);
                    margin-bottom: 15px;
                    font-size: 1.5rem;
                }
                .not-audited-message .message-content p {
                    color: var(--text-secondary);
                    line-height: 1.6;
                    margin-bottom: 20px;
                }
                .next-steps h4 {
                    color: var(--text-primary);
                    margin-bottom: 10px;
                    font-size: 1.1rem;
                }
                .next-steps ul {
                    list-style: none;
                    padding: 0;
                }
                .next-steps li {
                    color: var(--text-secondary);
                    margin-bottom: 8px;
                    padding-left: 10px;
                }
                .action-buttons {
                    margin-top: 25px;
                }
            `;
            document.head.appendChild(style);
        }
    }

    addUsageInstructionsStyles() {
        if (!document.getElementById('usage-instructions-styles')) {
            const style = document.createElement('style');
            style.id = 'usage-instructions-styles';
            style.textContent = `
                .usage-instructions {
                    display: flex;
                    align-items: flex-start;
                    gap: 20px;
                    padding: 30px;
                    background: rgba(255, 0, 0, 0.1);
                    border-radius: 15px;
                    border-left: 4px solid var(--accent-red);
                    margin: 20px 0;
                }
                .usage-instructions .message-icon {
                    font-size: 3rem;
                    margin-top: 10px;
                }
                .usage-instructions .message-content h3 {
                    color: var(--accent-red);
                    margin-bottom: 15px;
                    font-size: 1.5rem;
                }
                .usage-instructions .message-content p {
                    color: var(--text-secondary);
                    line-height: 1.6;
                    margin-bottom: 20px;
                }
                .url-examples h4 {
                    color: var(--text-primary);
                    margin-bottom: 15px;
                    font-size: 1.1rem;
                }
                .example-list {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                .example-item {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    padding: 10px;
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 8px;
                }
                .example-item code {
                    background: rgba(0, 212, 255, 0.2);
                    color: var(--accent-blue);
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                }
                .example-desc {
                    color: var(--text-secondary);
                    font-size: 0.9rem;
                }
                .action-buttons {
                    margin-top: 25px;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // AI思维链相关方法
    autoLoadThinkingChain() {
        console.log('🔍 自动加载AI思维链...');
        console.log('📊 审核数据:', this.auditData);

        // 检查审核数据中是否包含AI思维链
        if (this.auditData && this.auditData.ai_thinking_chain) {
            console.log('✅ 找到AI思维链数据，自动加载内容');
            console.log('🧠 AI思维链内容:', this.auditData.ai_thinking_chain);

            // 自动加载思维链内容
            this.loadThinkingChainContent();

            console.log('🎯 AI思维链已自动加载');
        } else {
            console.log('❌ 未找到AI思维链数据');
            console.log('   - auditData存在:', !!this.auditData);
            console.log('   - ai_thinking_chain存在:', !!(this.auditData && this.auditData.ai_thinking_chain));

            // 隐藏整个思维链区域
            const thinkingChainSection = document.getElementById('ai-thinking-chain');
            thinkingChainSection.style.display = 'none';
        }
    }

    checkAndShowThinkingChainButton() {
        // 保留原方法以兼容其他调用
        this.autoLoadThinkingChain();
    }

    async loadThinkingChainContent() {
        const thinkingContent = document.getElementById('thinking-content');
        const metadataSection = document.getElementById('thinking-metadata');

        try {
            // 显示加载状态
            thinkingContent.innerHTML = `
                <div class="thinking-loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载AI思考过程...</p>
                </div>
            `;

            await this.sleep(1000); // 模拟加载时间

            const aiThinkingData = this.auditData.ai_thinking_chain;

            if (!aiThinkingData) {
                thinkingContent.innerHTML = `
                    <div class="thinking-error">
                        <p>❌ AI思维链数据不可用</p>
                        <p>该审核报告不包含AI思考过程数据</p>
                    </div>
                `;
                return;
            }

            // 构建思维链内容
            let contentHTML = '';

            // 显示组合思维链
            if (aiThinkingData.combined_thinking) {
                // 检查combined_thinking是否缺少第二部分内容
                let combinedContent = aiThinkingData.combined_thinking;

                // 如果combined_thinking不包含第二部分内容，动态补充
                if (!combinedContent.includes('阶段 2/4') && aiThinkingData.phases_history) {
                    const phase2Group1 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第1组)'];
                    const phase2Group2 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第2组)'];

                    if (phase2Group1 || phase2Group2) {
                        // 找到阶段1结束和阶段3开始的位置
                        const stage1End = combinedContent.indexOf('---\n\n## 🔍 金额与标准检查');
                        if (stage1End !== -1) {
                            // 构建第二部分内容
                            let phase2Content = '\n\n---\n\n## 🔍 字段内容与一致性检查 (阶段 2/4)\n\n';

                            if (phase2Group1) {
                                phase2Content += '### 第1组 (规则6-14)\n\n';
                                phase2Content += phase2Group1.ai_thinking + '\n\n';
                            }

                            if (phase2Group2) {
                                phase2Content += '### 第2组 (规则15-24)\n\n';
                                phase2Content += phase2Group2.ai_thinking + '\n\n';
                            }

                            // 插入第二部分内容
                            combinedContent = combinedContent.substring(0, stage1End) +
                                            phase2Content +
                                            combinedContent.substring(stage1End);
                        }
                    }
                }

                contentHTML += `
                    <div class="combined-thinking">
                        <div class="combined-thinking-title">
                            🧠 完整思维链概览
                        </div>
                        <div class="combined-thinking-content">${this.parseMarkdown(combinedContent)}</div>
                    </div>
                `;
            }

            // 显示各阶段思维链
            if (aiThinkingData.phases_history) {
                const phases = Object.entries(aiThinkingData.phases_history);

                if (phases.length > 0) {
                    // 处理和合并阶段数据
                    const processedPhases = [];
                    let phase2Combined = null;



                    phases.forEach(([phaseKey, phaseData]) => {
                        if (phaseKey === 'phase1') {
                            processedPhases.push({
                                key: 'phase1',
                                name: phaseData.phase_name || '附件完整性检查',
                                thinking: phaseData.ai_thinking || '无思考内容',
                                status: phaseData.status || 'completed',
                                timestamp: phaseData.timestamp || '',
                                order: 1
                            });
                        } else if (phaseKey.includes('第二部分')) {
                            if (!phase2Combined) {
                                phase2Combined = {
                                    key: 'phase2_combined',
                                    name: '第二部分：字段内容与一致性检查',
                                    thinking: '',
                                    status: 'completed',
                                    timestamp: phaseData.timestamp || '',
                                    order: 2
                                };
                            }
                            // 合并第二部分的思考内容
                            const groupName = phaseKey.includes('第1组') ? '第1组' : '第2组';
                            const thinkingContent = phaseData.ai_thinking || '无思考内容';
                            if (phase2Combined.thinking === '') {
                                phase2Combined.thinking = `### ${groupName}\n\n${thinkingContent}`;
                            } else {
                                phase2Combined.thinking += `\n\n---\n\n### ${groupName}\n\n${thinkingContent}`;
                            }
                            // 使用最新的时间戳
                            if (phaseData.timestamp && phaseData.timestamp > phase2Combined.timestamp) {
                                phase2Combined.timestamp = phaseData.timestamp;
                            }
                        } else if (phaseKey === 'phase3') {
                            processedPhases.push({
                                key: 'phase3',
                                name: phaseData.phase_name || '第三部分：金额与标准检查',
                                thinking: phaseData.ai_thinking || '无思考内容',
                                status: phaseData.status || 'completed',
                                timestamp: phaseData.timestamp || '',
                                order: 3
                            });
                        } else if (phaseKey === 'phase4') {
                            processedPhases.push({
                                key: 'phase4',
                                name: phaseData.phase_name || '第四部分：八项规定合规性检查',
                                thinking: phaseData.ai_thinking || '无思考内容',
                                status: phaseData.status || 'completed',
                                timestamp: phaseData.timestamp || '',
                                order: 4
                            });
                        }
                    });

                    // 添加合并的第二部分
                    if (phase2Combined) {

                        processedPhases.push(phase2Combined);
                    }

                    // 按顺序排序
                    processedPhases.sort((a, b) => a.order - b.order);



                    contentHTML += '<div class="phases-container">';

                    processedPhases.forEach((phase, index) => {
                        const phaseNumber = index + 1;
                        const phaseName = phase.name;
                        const status = phase.status;
                        const timestamp = phase.timestamp;
                        const thinking = phase.thinking.trim();

                        contentHTML += `
                            <div class="thinking-phase" data-phase="${phase.key}">
                                <div class="phase-header" onclick="this.parentElement.querySelector('.phase-content').classList.toggle('expanded'); this.classList.toggle('expanded')">
                                    <div class="phase-title">
                                        阶段 ${phaseNumber}: ${this.escapeHtml(phaseName)}
                                    </div>
                                    <div class="phase-meta">
                                        <span class="phase-status ${status}">${this.getStatusText(status)}</span>
                                        <span class="phase-time">${this.formatTimestamp(timestamp)}</span>
                                        <span class="phase-expand-icon">▶</span>
                                    </div>
                                </div>
                                <div class="phase-content">
                                    <div class="thinking-text">${this.parseMarkdown(thinking)}</div>
                                </div>
                            </div>
                        `;
                    });

                    contentHTML += '</div>';
                }
            }

            thinkingContent.innerHTML = contentHTML;

            // 显示元数据
            this.displayThinkingMetadata(aiThinkingData.extraction_metadata);

        } catch (error) {
            console.error('加载AI思维链失败:', error);
            thinkingContent.innerHTML = `
                <div class="thinking-error">
                    <p>❌ 加载失败</p>
                    <p>错误信息: ${error.message}</p>
                </div>
            `;
        }
    }

    displayThinkingMetadata(metadata) {
        const metadataSection = document.getElementById('thinking-metadata');
        const metadataContent = document.getElementById('metadata-content');

        if (!metadata) {
            metadataSection.style.display = 'none';
            return;
        }

        let metadataHTML = '';

        const metadataItems = [
            { label: '提取时间', value: metadata.extracted_at },
            { label: '审核ID', value: metadata.audit_id },
            { label: '审核状态', value: metadata.audit_status },
            { label: '完成时间', value: metadata.completion_time },
            { label: '集成版本', value: metadata.integration_version || '1.0' }
        ];

        metadataItems.forEach(item => {
            if (item.value) {
                metadataHTML += `
                    <div class="metadata-item">
                        <div class="metadata-label">${item.label}</div>
                        <div class="metadata-value">${this.escapeHtml(item.value)}</div>
                    </div>
                `;
            }
        });

        metadataContent.innerHTML = metadataHTML;
        metadataSection.style.display = 'block';
    }

    expandAllPhases() {
        const phaseContents = document.querySelectorAll('.phase-content');
        const phaseHeaders = document.querySelectorAll('.phase-header');

        phaseContents.forEach(content => {
            content.classList.add('expanded');
        });

        phaseHeaders.forEach(header => {
            header.classList.add('expanded');
        });
    }

    collapseAllPhases() {
        const phaseContents = document.querySelectorAll('.phase-content');
        const phaseHeaders = document.querySelectorAll('.phase-header');

        phaseContents.forEach(content => {
            content.classList.remove('expanded');
        });

        phaseHeaders.forEach(header => {
            header.classList.remove('expanded');
        });
    }

    async copyThinkingContent() {
        try {
            const aiThinkingData = this.auditData.ai_thinking_chain;
            let textContent = '=== AI思考过程 ===\n\n';

            // 添加组合思维链
            if (aiThinkingData.combined_thinking) {
                // 检查combined_thinking是否缺少第二部分内容
                let combinedContent = aiThinkingData.combined_thinking;

                // 如果combined_thinking不包含第二部分内容，动态补充
                if (!combinedContent.includes('阶段 2/4') && aiThinkingData.phases_history) {
                    const phase2Group1 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第1组)'];
                    const phase2Group2 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第2组)'];

                    if (phase2Group1 || phase2Group2) {
                        // 找到阶段1结束和阶段3开始的位置
                        const stage1End = combinedContent.indexOf('---\n\n## 🔍 金额与标准检查');
                        if (stage1End !== -1) {
                            // 构建第二部分内容
                            let phase2Content = '\n\n---\n\n## 🔍 字段内容与一致性检查 (阶段 2/4)\n\n';

                            if (phase2Group1) {
                                phase2Content += '### 第1组 (规则6-14)\n\n';
                                phase2Content += phase2Group1.ai_thinking + '\n\n';
                            }

                            if (phase2Group2) {
                                phase2Content += '### 第2组 (规则15-24)\n\n';
                                phase2Content += phase2Group2.ai_thinking + '\n\n';
                            }

                            // 插入第二部分内容
                            combinedContent = combinedContent.substring(0, stage1End) +
                                            phase2Content +
                                            combinedContent.substring(stage1End);
                        }
                    }
                }

                textContent += '【完整思维链概览】\n';
                textContent += combinedContent + '\n\n';
            }

            // 添加各阶段思维链
            if (aiThinkingData.phases_history) {
                const phases = Object.entries(aiThinkingData.phases_history);

                // 按阶段顺序排序并合并第二部分
                const processedPhases = [];
                let phase2Combined = null;

                phases.forEach(([phaseKey, phaseData]) => {
                    if (phaseKey === 'phase1') {
                        processedPhases.push({
                            key: 'phase1',
                            name: phaseData.phase_name || '附件完整性检查',
                            thinking: phaseData.ai_thinking || '无思考内容',
                            order: 1
                        });
                    } else if (phaseKey.includes('第二部分')) {
                        if (!phase2Combined) {
                            phase2Combined = {
                                key: 'phase2_combined',
                                name: '第二部分：字段内容与一致性检查',
                                thinking: '',
                                order: 2
                            };
                        }
                        // 合并第二部分的思考内容
                        const groupName = phaseKey.includes('第1组') ? '第1组' : '第2组';
                        const thinkingContent = phaseData.ai_thinking || '无思考内容';
                        if (phase2Combined.thinking === '') {
                            phase2Combined.thinking = `### ${groupName}\n\n${thinkingContent}`;
                        } else {
                            phase2Combined.thinking += `\n\n---\n\n### ${groupName}\n\n${thinkingContent}`;
                        }
                    } else if (phaseKey === 'phase3') {
                        processedPhases.push({
                            key: 'phase3',
                            name: phaseData.phase_name || '第三部分：金额与标准检查',
                            thinking: phaseData.ai_thinking || '无思考内容',
                            order: 3
                        });
                    } else if (phaseKey === 'phase4') {
                        processedPhases.push({
                            key: 'phase4',
                            name: phaseData.phase_name || '第四部分：八项规定合规性检查',
                            thinking: phaseData.ai_thinking || '无思考内容',
                            order: 4
                        });
                    }
                });

                // 添加合并的第二部分
                if (phase2Combined) {
                    processedPhases.push(phase2Combined);
                }

                // 按顺序排序
                processedPhases.sort((a, b) => a.order - b.order);

                // 输出处理后的阶段
                processedPhases.forEach((phase, index) => {
                    const phaseNumber = index + 1;
                    textContent += `【阶段 ${phaseNumber}: ${phase.name}】\n`;
                    textContent += phase.thinking.trim() + '\n\n';
                });
            }

            // 添加元数据
            if (aiThinkingData.extraction_metadata) {
                textContent += '【元数据信息】\n';
                const metadata = aiThinkingData.extraction_metadata;
                textContent += `提取时间: ${metadata.extracted_at || 'N/A'}\n`;
                textContent += `审核ID: ${metadata.audit_id || 'N/A'}\n`;
                textContent += `审核状态: ${metadata.audit_status || 'N/A'}\n`;
                textContent += `完成时间: ${metadata.completion_time || 'N/A'}\n`;
            }

            await navigator.clipboard.writeText(textContent);
            this.showSuccessToast('AI思考过程已复制到剪贴板');

        } catch (error) {
            console.error('复制失败:', error);
            this.showSuccessToast('复制失败，请手动选择内容复制');
        }
    }

    searchThinkingContent() {
        const searchInput = document.getElementById('thinking-search-input');
        const searchResults = document.getElementById('search-results');
        const query = searchInput.value.trim().toLowerCase();

        if (query.length < 2) {
            this.clearSearchResults();
            return;
        }

        const aiThinkingData = this.auditData.ai_thinking_chain;
        if (!aiThinkingData) return;

        let results = [];

        // 搜索组合思维链
        if (aiThinkingData.combined_thinking) {
            const content = aiThinkingData.combined_thinking.toLowerCase();
            if (content.includes(query)) {
                const context = this.getSearchContext(aiThinkingData.combined_thinking, query);
                results.push({
                    type: 'combined',
                    title: '完整思维链概览',
                    context: context
                });
            }
        }

        // 搜索各阶段思维链
        if (aiThinkingData.phases_history) {
            const phases = Object.entries(aiThinkingData.phases_history);

            // 处理和合并阶段数据用于搜索
            const processedPhases = [];
            let phase2Combined = null;

            phases.forEach(([phaseKey, phaseData]) => {
                if (phaseKey === 'phase1') {
                    processedPhases.push({
                        key: 'phase1',
                        name: phaseData.phase_name || '附件完整性检查',
                        thinking: phaseData.ai_thinking || '无思考内容',
                        order: 1
                    });
                } else if (phaseKey.includes('第二部分')) {
                    if (!phase2Combined) {
                        phase2Combined = {
                            key: 'phase2_combined',
                            name: '第二部分：字段内容与一致性检查',
                            thinking: '',
                            order: 2
                        };
                    }
                    // 合并第二部分的思考内容
                    const groupName = phaseKey.includes('第1组') ? '第1组' : '第2组';
                    const thinkingContent = phaseData.ai_thinking || '无思考内容';
                    if (phase2Combined.thinking === '') {
                        phase2Combined.thinking = `### ${groupName}\n\n${thinkingContent}`;
                    } else {
                        phase2Combined.thinking += `\n\n---\n\n### ${groupName}\n\n${thinkingContent}`;
                    }
                } else if (phaseKey === 'phase3') {
                    processedPhases.push({
                        key: 'phase3',
                        name: phaseData.phase_name || '第三部分：金额与标准检查',
                        thinking: phaseData.ai_thinking || '无思考内容',
                        order: 3
                    });
                } else if (phaseKey === 'phase4') {
                    processedPhases.push({
                        key: 'phase4',
                        name: phaseData.phase_name || '第四部分：八项规定合规性检查',
                        thinking: phaseData.ai_thinking || '无思考内容',
                        order: 4
                    });
                }
            });

            // 添加合并的第二部分
            if (phase2Combined) {
                processedPhases.push(phase2Combined);
            }

            // 按顺序排序
            processedPhases.sort((a, b) => a.order - b.order);

            // 搜索处理后的阶段
            processedPhases.forEach((phase, index) => {
                const thinking = phase.thinking || '';
                const content = thinking.toLowerCase();

                if (content.includes(query)) {
                    const context = this.getSearchContext(thinking, query);
                    results.push({
                        type: 'phase',
                        phaseKey: phase.key,
                        title: `阶段 ${index + 1}: ${phase.name}`,
                        context: context
                    });
                }
            });
        }

        this.displaySearchResults(results, query);
    }

    getSearchContext(text, query, contextLength = 100) {
        const lowerText = text.toLowerCase();
        const lowerQuery = query.toLowerCase();
        const index = lowerText.indexOf(lowerQuery);

        if (index === -1) return '';

        const start = Math.max(0, index - contextLength);
        const end = Math.min(text.length, index + query.length + contextLength);

        let context = text.substring(start, end);

        if (start > 0) context = '...' + context;
        if (end < text.length) context = context + '...';

        return context;
    }

    displaySearchResults(results, query) {
        const searchResults = document.getElementById('search-results');

        if (results.length === 0) {
            searchResults.innerHTML = '<p style="color: var(--text-muted); text-align: center;">未找到相关内容</p>';
            searchResults.style.display = 'block';
            return;
        }

        let resultsHTML = '';
        results.forEach(result => {
            const highlightedContext = this.highlightSearchTerm(result.context, query);
            resultsHTML += `
                <div class="search-result-item" onclick="window.aiResultsViewer.scrollToResult('${result.type}', '${result.phaseKey || ''}')">
                    <div style="font-weight: 600; color: var(--accent-blue); margin-bottom: 5px;">
                        ${result.title}
                    </div>
                    <div class="search-result-text">${highlightedContext}</div>
                </div>
            `;
        });

        searchResults.innerHTML = resultsHTML;
        searchResults.style.display = 'block';
    }

    highlightSearchTerm(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }

    clearSearchResults() {
        const searchResults = document.getElementById('search-results');
        searchResults.style.display = 'none';
        searchResults.innerHTML = '';
    }

    // 辅助方法
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Markdown转HTML的简单解析器
    parseMarkdown(text) {
        if (!text) return '';

        let html = text;

        // 处理标题
        html = html.replace(/^## (.+)$/gm, '<h2 class="md-h2">$1</h2>');
        html = html.replace(/^### (.+)$/gm, '<h3 class="md-h3">$1</h3>');
        html = html.replace(/^#### (.+)$/gm, '<h4 class="md-h4">$1</h4>');

        // 处理粗体（支持中文）
        html = html.replace(/\*\*([^*]+?)\*\*/g, '<strong class="md-bold">$1</strong>');

        // 处理斜体（更保守的匹配，避免与粗体冲突）
        html = html.replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em class="md-italic">$1</em>');

        // 处理代码块
        html = html.replace(/```([\s\S]*?)```/g, '<pre class="md-code-block"><code>$1</code></pre>');

        // 处理行内代码
        html = html.replace(/`([^`\n]+)`/g, '<code class="md-inline-code">$1</code>');

        // 处理无序列表（支持多级）
        html = html.replace(/^- (.+)$/gm, '<li class="md-list-item">$1</li>');

        // 将连续的列表项包装在ul中
        html = html.replace(/(<li class="md-list-item">.*?<\/li>\s*)+/gs, (match) => {
            return `<ul class="md-list">${match}</ul>`;
        });

        // 处理分隔线
        html = html.replace(/^---+$/gm, '<hr class="md-divider">');

        // 处理段落（按双换行分割）
        const sections = html.split(/\n\s*\n/);
        html = sections.map(section => {
            section = section.trim();
            if (!section) return '';

            // 如果已经是HTML标签，直接返回
            if (section.match(/^<(h[1-6]|ul|pre|hr)/)) {
                return section;
            }

            // 如果包含列表项但没有被ul包装，包装它
            if (section.includes('<li class="md-list-item">')) {
                return `<ul class="md-list">${section}</ul>`;
            }

            // 普通段落
            return `<p class="md-paragraph">${section.replace(/\n/g, '<br>')}</p>`;
        }).filter(p => p).join('\n');

        return html;
    }

    getStatusText(status) {
        const statusMap = {
            'completed': '已完成',
            'running': '进行中',
            'failed': '失败',
            'unknown': '未知'
        };
        return statusMap[status] || status;
    }

    formatTimestamp(timestamp) {
        if (!timestamp) return '';

        try {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            return timestamp;
        }
    }

    scrollToResult(type, phaseKey) {
        if (type === 'combined') {
            const combinedThinking = document.querySelector('.combined-thinking');
            if (combinedThinking) {
                combinedThinking.scrollIntoView({ behavior: 'smooth', block: 'center' });
                combinedThinking.style.animation = 'highlight 2s ease-out';
            }
        } else if (type === 'phase' && phaseKey) {
            const phaseElement = document.querySelector(`[data-phase="${phaseKey}"]`);
            if (phaseElement) {
                // 展开该阶段
                const phaseContent = phaseElement.querySelector('.phase-content');
                const phaseHeader = phaseElement.querySelector('.phase-header');

                if (phaseContent && phaseHeader) {
                    phaseContent.classList.add('expanded');
                    phaseHeader.classList.add('expanded');
                }

                // 滚动到该阶段
                phaseElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                phaseElement.style.animation = 'highlight 2s ease-out';
            }
        }

        // 清除搜索结果
        this.clearSearchResults();
    }
}

// 初始化结果查看器
document.addEventListener('DOMContentLoaded', () => {
    window.aiResultsViewer = new AIResultsViewer();
});

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI审核系统API服务器
提供实时状态查询和数据接口
"""

import os
import json
import time
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from pathlib import Path


class AuditAPIHandler(BaseHTTPRequestHandler):
    """审核API请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 设置CORS头
        self.send_cors_headers()
        
        try:
            if path == '/api/status':
                self.handle_status_request()
            elif path == '/api/state':
                self.handle_state_request()
            elif path == '/api/report':
                self.handle_report_request()
            elif path == '/api/rules':
                self.handle_rules_request()
            elif path == '/api/progress':
                self.handle_progress_request()
            elif path == '/api/thinking-status':
                self.handle_thinking_status_request()
            else:
                self.send_error_response(404, "API endpoint not found")
        except Exception as e:
            self.send_error_response(500, str(e))
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_cors_headers()
        self.end_headers()
    
    def send_cors_headers(self):
        """发送CORS头"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Content-Type', 'application/json; charset=utf-8')
    
    def handle_status_request(self):
        """处理状态查询请求（兼容旧版API）"""
        # 统一使用 audit_state.json 文件
        state_file = Path(__file__).parent / 'audit_state.json'

        if state_file.exists():
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 获取完整的AI思维链内容
            ai_thinking_content = self._get_complete_ai_thinking(state_data)

            # 转换新格式到旧格式（向后兼容）
            status_data = {
                "current_step": state_data.get("current_phase", "ready"),
                "status": "audit-complete" if state_data.get("audit_status") == "completed" else state_data.get("audit_status", "ready"),
                "message": state_data.get("message", "系统就绪"),
                "timestamp": state_data.get("last_updated", time.strftime("%Y-%m-%d %H:%M:%S")),
                "detail": state_data.get("detail", ""),
                "ai_thinking": ai_thinking_content,
                "final_stats": state_data.get("summary", {}),
                "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "server_status": "online"
            }

            self.send_json_response(status_data)
        else:
            # 返回默认状态
            default_status = {
                "current_step": "idle",
                "status": "ready",
                "message": "系统就绪，等待开始审核",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "ai_thinking": "系统就绪，等待开始审核",
                "server_status": "online"
            }
            self.send_json_response(default_status)

    def _get_complete_ai_thinking(self, state_data):
        """获取完整的AI思维链内容"""
        # 新架构：优先从报告文件读取AI思维链
        audit_id = state_data.get("audit_id")
        if audit_id:
            try:
                # 尝试从报告文件读取AI思维链
                project_root = Path(__file__).parent.parent
                report_file = project_root / "audit_reports" / f"audit_report_{audit_id}.json"

                if report_file.exists():
                    with open(report_file, 'r', encoding='utf-8') as f:
                        report_data = json.load(f)

                    ai_thinking_chain = report_data.get("ai_thinking_chain", {})
                    combined_thinking = ai_thinking_chain.get("combined_thinking", "")

                    if combined_thinking and len(combined_thinking) > 100:
                        print(f"[新架构] 从报告文件读取AI思维链，长度: {len(combined_thinking)}")
                        return combined_thinking
                    else:
                        print(f"[新架构] 报告文件中AI思维链内容不足，回退到状态文件")

            except Exception as e:
                print(f"[新架构] 从报告文件读取AI思维链失败: {e}")

        # 传统模式：从状态文件读取（保持兼容性）
        # 优先级：
        # 1. phases_history.finished.ai_thinking (审核完成后的完整内容)
        # 2. 顶层 ai_thinking (当前进行中的内容)
        # 3. 默认内容

        phases_history = state_data.get("phases_history", {})

        # 如果审核已完成，使用finished阶段的完整AI思维链
        audit_status = state_data.get("audit_status")
        print(f"[API调试] audit_status: {audit_status}")
        print(f"[API调试] phases_history keys: {list(phases_history.keys())}")

        if audit_status == "completed" and "finished" in phases_history:
            finished_thinking = phases_history["finished"].get("ai_thinking", "")
            print(f"[API调试] finished_thinking length: {len(finished_thinking)}")
            if finished_thinking and len(finished_thinking) > 100:  # 确保不是默认内容
                print(f"[API] 返回完整AI思维链，长度: {len(finished_thinking)}")
                return finished_thinking
            else:
                print(f"[API调试] finished_thinking太短或为空: {repr(finished_thinking[:100])}")
        else:
            print(f"[API调试] 条件不满足: audit_status={audit_status}, finished_exists={'finished' in phases_history}")

        # 否则，尝试组合所有阶段的AI思维链
        combined_thinking_parts = []

        # 按阶段顺序组合
        phase_order = ["phase1", "phase2", "phase3", "phase4", "finished"]
        for phase_key in phase_order:
            if phase_key == "phase2":
                # 特殊处理第二部分：合并分组数据
                phase2_content = self._build_phase2_combined_content(phases_history)
                if phase2_content:
                    combined_thinking_parts.append(phase2_content)
            elif phase_key in phases_history:
                phase_data = phases_history[phase_key]
                phase_thinking = phase_data.get("ai_thinking", "")
                if phase_thinking and phase_thinking.strip():
                    combined_thinking_parts.append(phase_thinking)

        if combined_thinking_parts:
            combined_content = "\n\n---\n\n".join(combined_thinking_parts)
            print(f"[API] 返回组合AI思维链，长度: {len(combined_content)}")
            return combined_content

        # 最后使用顶层的ai_thinking
        top_level_thinking = state_data.get("ai_thinking", "系统就绪，等待开始AI审核分析...")
        print(f"[API] 返回顶层AI思维链，长度: {len(top_level_thinking)}")
        return top_level_thinking

    def _build_phase2_combined_content(self, phases_history: dict) -> str:
        """构建第二部分的合并内容"""
        phase2_parts = []

        # 查找第二部分的分组数据
        group1_key = "第二部分：字段内容与一致性检查_(第1组)"
        group2_key = "第二部分：字段内容与一致性检查_(第2组)"

        # 添加第1组内容
        if group1_key in phases_history:
            group1_data = phases_history[group1_key]
            group1_thinking = group1_data.get("ai_thinking", "")
            if group1_thinking and group1_thinking.strip():
                phase2_parts.append("### 第1组 (规则6-14)")
                phase2_parts.append("")
                phase2_parts.append(group1_thinking)
                phase2_parts.append("")

        # 添加第2组内容
        if group2_key in phases_history:
            group2_data = phases_history[group2_key]
            group2_thinking = group2_data.get("ai_thinking", "")
            if group2_thinking and group2_thinking.strip():
                if phase2_parts:  # 如果已有第1组内容，添加分隔符
                    phase2_parts.append("---")
                    phase2_parts.append("")
                phase2_parts.append("### 第2组 (规则15-24)")
                phase2_parts.append("")
                phase2_parts.append(group2_thinking)
                phase2_parts.append("")

        # 如果没有找到分组数据，尝试查找标准的phase2
        if not phase2_parts and "phase2" in phases_history:
            phase2_data = phases_history["phase2"]
            phase2_thinking = phase2_data.get("ai_thinking", "")
            if phase2_thinking and phase2_thinking.strip():
                phase2_parts.append(phase2_thinking)

        return "\n".join(phase2_parts)

    def handle_state_request(self):
        """处理新版状态查询请求"""
        state_file = Path(__file__).parent / 'audit_state.json'

        if state_file.exists():
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 获取完整的AI思维链内容
            complete_ai_thinking = self._get_complete_ai_thinking(state_data)
            state_data['ai_thinking'] = complete_ai_thinking

            # 添加额外的状态信息
            state_data['api_timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
            state_data['server_status'] = 'online'
            state_data['file_source'] = str(state_file)

            print(f"[API] 读取权威状态文件: {state_file}")
            print(f"[API] AI思考内容长度: {len(complete_ai_thinking)}")

            self.send_json_response(state_data)
        else:
            # 返回默认状态
            default_state = {
                "audit_id": None,
                "audit_status": "ready",
                "current_phase": "ready",
                "progress_percent": 0,
                "start_time": None,
                "completion_time": None,
                "summary": {
                    "total_rules": 0,
                    "completed_rules": 0,
                    "passed_rules": 0,
                    "failed_rules": 0,
                    "warning_rules": 0
                },
                "ai_thinking": "系统就绪，等待开始审核",
                "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "message": "系统就绪，等待开始审核",
                "detail": "请启动审核流程",
                "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "server_status": "online"
            }
            self.send_json_response(default_state)
    
    def handle_report_request(self):
        """处理报告查询请求"""
        # 解析查询参数，支持指定文档编号
        from urllib.parse import urlparse, parse_qs
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        doc_num = query_params.get('doc_num', [None])[0]

        # 查找审核报告
        reports_dir = Path(__file__).parent.parent / 'audit_reports'

        report_files = []
        if reports_dir.exists():
            if doc_num:
                # 查找特定文档编号的报告
                specific_report = reports_dir / f'audit_report_{doc_num}.json'
                if specific_report.exists():
                    report_files = [specific_report]
                    print(f"✅ 找到指定文档报告: audit_report_{doc_num}.json")
                else:
                    print(f"⚠️ 未找到文档 {doc_num} 的报告，查找最新报告")

            # 如果没有找到特定报告或没有指定doc_num，查找最新报告
            if not report_files:
                report_files = list(reports_dir.glob('audit_report*.json'))
                report_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        if report_files:
            with open(report_files[0], 'r', encoding='utf-8') as f:
                report_data = json.load(f)
            
            # 添加元数据
            report_data['meta'] = {
                'file_path': str(report_files[0]),
                'last_modified': time.strftime("%Y-%m-%d %H:%M:%S", 
                                             time.localtime(report_files[0].stat().st_mtime)),
                'api_timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.send_json_response(report_data)
        else:
            self.send_error_response(404, "No audit report found")
    
    def handle_rules_request(self):
        """处理规则信息查询请求"""
        rules_info = {
            "deterministic_rules": {
                "count": 12,
                "description": "确定性规则引擎 - 数据验证、格式检查、逻辑判断",
                "rules": [
                    "规则1：检查是否上传发票",
                    "规则2：检查是否上传事前审批表", 
                    "规则3：检查是否上传招待明细",
                    "规则4：金额一致性检查",
                    "规则5：日期范围检查",
                    "规则6：发票购买方一致性",
                    "规则9：金额预算范围检查",
                    "规则10：参与人数一致性",
                    "规则11：日期一致性检查",
                    "规则12：地点一致性检查"
                ]
            },
            "keyword_rules": {
                "count": 14,
                "description": "关键词规则引擎 - 敏感词检测、风险识别、合规筛查",
                "rules": [
                    "规则7：敏感关键词检测",
                    "规则8：招待关键词检查",
                    "规则13：奢侈品关键词",
                    "规则14：酒类关键词",
                    "规则17：礼品关键词",
                    "规则18：旅游关键词",
                    "规则19：高额消费关键词",
                    "规则20：私人会所关键词"
                ]
            },
            "semantic_rules": {
                "count": 12,
                "description": "AI语义规则引擎 - 深度理解、语义分析、智能推理",
                "rules": [
                    "规则15：项目描述语义相似度分析",
                    "规则16：申报事由与消费内容逻辑一致性",
                    "规则28：发票地址风景名胜区判断"
                ]
            },
            "total_rules": 38,
            "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.send_json_response(rules_info)
    
    def handle_progress_request(self):
        """处理进度查询请求"""
        # 结合状态文件和报告文件生成详细进度
        status_file = Path(__file__).parent / 'audit_status.json'
        
        progress_data = {
            "overall_progress": 0,
            "current_phase": "idle",
            "phases": {
                "data_loading": {"status": "pending", "progress": 0},
                "deterministic_rules": {"status": "pending", "progress": 0},
                "keyword_rules": {"status": "pending", "progress": 0},
                "semantic_rules": {"status": "pending", "progress": 0},
                "report_generation": {"status": "pending", "progress": 0}
            },
            "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if status_file.exists():
            with open(status_file, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            # 根据当前步骤更新进度
            current_step = status_data.get('current_step', 'idle')
            status = status_data.get('status', 'pending')
            
            if current_step == 'data-loading':
                progress_data["current_phase"] = "data_loading"
                progress_data["phases"]["data_loading"]["status"] = status
                progress_data["overall_progress"] = 10
            elif current_step == 'rule-parsing':
                progress_data["current_phase"] = "keyword_rules"
                progress_data["phases"]["data_loading"]["status"] = "completed"
                progress_data["phases"]["deterministic_rules"]["status"] = "completed"
                progress_data["phases"]["keyword_rules"]["status"] = status
                progress_data["overall_progress"] = 50
            elif current_step == 'audit-execution':
                progress_data["current_phase"] = "semantic_rules"
                progress_data["phases"]["data_loading"]["status"] = "completed"
                progress_data["phases"]["deterministic_rules"]["status"] = "completed"
                progress_data["phases"]["keyword_rules"]["status"] = "completed"
                progress_data["phases"]["semantic_rules"]["status"] = status
                progress_data["overall_progress"] = 80
            elif current_step == 'report-generation':
                progress_data["current_phase"] = "report_generation"
                for phase in progress_data["phases"]:
                    progress_data["phases"][phase]["status"] = "completed"
                progress_data["overall_progress"] = 100
        
        self.send_json_response(progress_data)

    def handle_thinking_status_request(self):
        """处理思维链状态查询请求 - 新增API"""
        from urllib.parse import urlparse, parse_qs
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        doc_num = query_params.get('doc_num', [None])[0]

        if not doc_num:
            error_response = {
                "error": "缺少文档编号参数",
                "message": "请提供doc_num参数",
                "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            self.send_json_response(error_response)
            return

        # 查找报告文件
        report_file = Path(__file__).parent.parent / "audit_reports" / f"audit_report_{doc_num}.json"

        if not report_file.exists():
            error_response = {
                "error": "报告文件不存在",
                "message": f"未找到文档 {doc_num} 的审核报告",
                "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "file_path": str(report_file)
            }
            self.send_json_response(error_response)
            return

        try:
            # 获取文件状态信息
            file_stat = report_file.stat()

            # 读取AI思维链数据
            with open(report_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)

            ai_thinking_chain = report_data.get("ai_thinking_chain", {})
            combined_thinking = ai_thinking_chain.get("combined_thinking", "")
            phases_history = ai_thinking_chain.get("phases_history", {})

            # 构建响应
            thinking_status = {
                "doc_num": doc_num,
                "file_modified_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(file_stat.st_mtime)),
                "file_size": file_stat.st_size,
                "combined_thinking_length": len(combined_thinking),
                "phases_count": len(phases_history),
                "phases_list": list(phases_history.keys()),
                "has_combined_thinking": bool(combined_thinking and len(combined_thinking) > 50),
                "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "combined_thinking": combined_thinking  # 包含完整内容
            }

            print(f"[API] 思维链状态查询: {doc_num}, 长度: {len(combined_thinking)}")
            self.send_json_response(thinking_status)

        except Exception as e:
            error_response = {
                "error": "读取思维链数据失败",
                "message": str(e),
                "api_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "file_path": str(report_file)
            }
            self.send_json_response(error_response)

    def send_json_response(self, data):
        """发送JSON响应"""
        self.end_headers()
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def send_error_response(self, code, message):
        """发送错误响应"""
        self.send_response(code)
        self.end_headers()
        error_data = {
            "error": True,
            "code": code,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        json_data = json.dumps(error_data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """静默日志输出"""
        pass


class AuditAPIServer:
    """审核API服务器"""

    def __init__(self, port=None):
        # 支持环境变量配置端口
        self.port = port or int(os.environ.get('API_PORT', 8001))
        self.server = None
        self.server_thread = None
    
    def start(self):
        """启动API服务器"""
        try:
            self.server = HTTPServer(('localhost', self.port), AuditAPIHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            print(f"✅ API服务器已启动: http://localhost:{self.port}")
            print(f"📡 API端点:")
            print(f"   • 状态查询: http://localhost:{self.port}/api/status")
            print(f"   • 报告查询: http://localhost:{self.port}/api/report")
            print(f"   • 规则信息: http://localhost:{self.port}/api/rules")
            print(f"   • 进度查询: http://localhost:{self.port}/api/progress")
            print(f"   • 思维链状态: http://localhost:{self.port}/api/thinking-status?doc_num=ZDBXD2025042900003")
            
            return True
        except Exception as e:
            print(f"❌ API服务器启动失败: {e}")
            return False
    
    def stop(self):
        """停止API服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("🛑 API服务器已停止")


def main():
    """主函数"""
    print("🚀 AI审核系统API服务器")
    print("=" * 50)
    
    api_server = AuditAPIServer()
    
    if api_server.start():
        try:
            print("\n按 Ctrl+C 停止API服务器")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止API服务器...")
            api_server.stop()
    else:
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())

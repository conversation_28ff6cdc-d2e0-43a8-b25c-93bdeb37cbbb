<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI财务审核控制台 - 修复版</title>
    <link rel="stylesheet" href="ai_console.css">
    <style>
        /* 调试信息样式 */
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
        .debug-toggle {
            position: fixed;
            top: 10px;
            right: 320px;
            background: #333;
            color: #fff;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            z-index: 10001;
        }
    </style>
</head>
<body>
    <!-- 调试信息 -->
    <button class="debug-toggle" onclick="toggleDebug()">调试</button>
    <div class="debug-info" id="debug-info" style="display: none;">
        <div id="debug-log">正在初始化...</div>
    </div>

    <!-- 动态粒子背景 -->
    <div id="particles-background"></div>
    
    <!-- 主控制台界面 -->
    <div class="console-container">
        <!-- 顶部状态栏 -->
        <header class="console-header">
            <div class="header-left">
                <div class="logo-section">
                    <div class="ai-logo">
                        <div class="logo-core"></div>
                        <div class="logo-ring"></div>
                        <div class="logo-pulse"></div>
                    </div>
                    <div class="system-title">
                        <h1>AI财务审核系统</h1>
                        <span class="version">v2.0 Neural Engine - 修复版</span>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="status-indicators">
                    <div class="status-item">
                        <span class="status-label">系统状态</span>
                        <span class="status-value online" id="system-status">在线</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">API连接</span>
                        <span class="status-value" id="api-status">检测中...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="console-main">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- AI思维过程 -->
                <div class="thinking-panel">
                    <div class="panel-header">
                        <h3>🧠 AI思维过程</h3>
                        <div class="neural-activity">
                            <div class="neuron"></div>
                            <div class="neuron"></div>
                            <div class="neuron"></div>
                        </div>
                    </div>
                    <div class="thinking-content" id="thinking-content">
                        <div class="thinking-step">
                            <div class="step-indicator"></div>
                            <div class="step-text">正在初始化AI审核引擎...</div>
                        </div>
                    </div>
                </div>

                <!-- 审核统计 -->
                <div class="stats-panel">
                    <div class="panel-header">
                        <h3>📊 审核统计</h3>
                    </div>
                    <div class="audit-statistics" id="audit-statistics">
                        <div class="stat-item">
                            <div class="stat-label">总规则数</div>
                            <div class="stat-value" id="total-rules">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">通过数</div>
                            <div class="stat-value success" id="passed-rules">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">失败数</div>
                            <div class="stat-value error" id="failed-rules">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">警告数</div>
                            <div class="stat-value warning" id="warning-rules">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">通过率</div>
                            <div class="stat-value" id="pass-rate">0%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 实时进度 -->
                <div class="progress-panel">
                    <div class="panel-header">
                        <h3>⚡ 实时进度</h3>
                    </div>
                    <div class="progress-content">
                        <div class="progress-bar" id="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                            <div class="progress-text">0/0</div>
                        </div>
                        <div class="current-status" id="current-status">
                            系统就绪，等待开始审核...
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="controls-panel">
                    <button class="control-btn primary" onclick="startAudit()">
                        <span class="btn-icon">🚀</span>
                        开始审核
                    </button>
                    <button class="control-btn secondary" onclick="refreshData()">
                        <span class="btn-icon">🔄</span>
                        刷新数据
                    </button>
                    <button class="control-btn secondary" onclick="testConnection()">
                        <span class="btn-icon">🔗</span>
                        测试连接
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script>
        // 调试日志系统
        let debugVisible = false;
        let debugLog = [];

        function debugMsg(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            // 保持最新50条日志
            if (debugLog.length > 50) {
                debugLog.shift();
            }
            
            // 更新调试显示
            const debugElement = document.getElementById('debug-log');
            if (debugElement) {
                debugElement.innerHTML = debugLog.join('<br>');
                debugElement.scrollTop = debugElement.scrollHeight;
            }
            
            // 同时输出到控制台
            console.log(`[DEBUG] ${logEntry}`);
        }

        function toggleDebug() {
            debugVisible = !debugVisible;
            const debugInfo = document.getElementById('debug-info');
            debugInfo.style.display = debugVisible ? 'block' : 'none';
        }

        // 全局变量
        let apiBaseUrl = null;
        let auditData = null;

        // 初始化函数
        async function initializeSystem() {
            debugMsg('开始初始化系统...');
            
            try {
                // 1. 检测API服务器
                debugMsg('检测API服务器...');
                apiBaseUrl = await detectAPIServer();
                
                if (apiBaseUrl) {
                    debugMsg(`API服务器检测成功: ${apiBaseUrl}`, 'success');
                    document.getElementById('api-status').textContent = '已连接';
                    document.getElementById('api-status').className = 'status-value online';
                } else {
                    debugMsg('API服务器检测失败', 'error');
                    document.getElementById('api-status').textContent = '离线';
                    document.getElementById('api-status').className = 'status-value offline';
                }
                
                // 2. 加载审核数据
                debugMsg('加载审核数据...');
                await loadAuditData();
                
                // 3. 更新界面
                debugMsg('更新界面显示...');
                updateInterface();
                
                debugMsg('系统初始化完成', 'success');
                
            } catch (error) {
                debugMsg(`初始化失败: ${error.message}`, 'error');
            }
        }

        // API服务器检测
        async function detectAPIServer() {
            const ports = [8001, 8002, 8003, 8004];
            
            for (const port of ports) {
                try {
                    debugMsg(`测试端口 ${port}...`);
                    const response = await fetch(`http://localhost:${port}/api/status`, {
                        method: 'GET',
                        timeout: 3000
                    });
                    
                    if (response.ok) {
                        debugMsg(`端口 ${port} 响应正常`);
                        return `http://localhost:${port}`;
                    }
                } catch (error) {
                    debugMsg(`端口 ${port} 连接失败: ${error.message}`);
                }
            }
            
            return null;
        }

        // 加载审核数据
        async function loadAuditData() {
            if (!apiBaseUrl) {
                debugMsg('API服务器不可用，跳过数据加载');
                return;
            }
            
            try {
                // 获取文档编号
                const urlParams = new URLSearchParams(window.location.search);
                const docNum = urlParams.get('doc_num') || 'ZDBXD2025042900003';
                debugMsg(`使用文档编号: ${docNum}`);
                
                // 请求审核报告
                const reportUrl = `${apiBaseUrl}/api/report?doc_num=${docNum}`;
                debugMsg(`请求URL: ${reportUrl}`);
                
                const response = await fetch(reportUrl);
                if (response.ok) {
                    auditData = await response.json();
                    debugMsg('审核数据加载成功');
                    debugMsg(`数据摘要: ${JSON.stringify(auditData.summary)}`);
                } else {
                    debugMsg(`数据加载失败: HTTP ${response.status}`, 'error');
                }
                
            } catch (error) {
                debugMsg(`数据加载异常: ${error.message}`, 'error');
            }
        }

        // 更新界面
        function updateInterface() {
            if (!auditData || !auditData.summary) {
                debugMsg('没有可用数据，使用默认显示');
                return;
            }
            
            const summary = auditData.summary;
            debugMsg('开始更新界面数据...');
            
            try {
                // 更新统计数据
                document.getElementById('total-rules').textContent = summary.total_rules_checked || 0;
                document.getElementById('passed-rules').textContent = summary.passed_count || 0;
                document.getElementById('failed-rules').textContent = summary.failed_count || 0;
                document.getElementById('warning-rules').textContent = summary.warning_count || 0;
                
                // 计算通过率
                const total = summary.total_rules_checked || 0;
                const passed = summary.passed_count || 0;
                const passRate = total > 0 ? Math.round((passed / total) * 100) : 0;
                document.getElementById('pass-rate').textContent = `${passRate}%`;
                
                // 更新进度条
                const progressFill = document.querySelector('.progress-fill');
                const progressText = document.querySelector('.progress-text');
                if (progressFill && progressText) {
                    progressFill.style.width = '100%';
                    progressText.textContent = `${total}/${total}`;
                }
                
                // 更新状态
                document.getElementById('current-status').textContent = '审核完成！数据已加载';
                
                debugMsg('界面更新完成', 'success');
                
            } catch (error) {
                debugMsg(`界面更新失败: ${error.message}`, 'error');
            }
        }

        // 控制按钮函数
        function startAudit() {
            debugMsg('开始审核按钮被点击');
            if (auditData) {
                debugMsg('数据已存在，重新显示');
                updateInterface();
            } else {
                debugMsg('重新加载数据...');
                loadAuditData().then(updateInterface);
            }
        }

        function refreshData() {
            debugMsg('刷新数据按钮被点击');
            auditData = null;
            loadAuditData().then(updateInterface);
        }

        function testConnection() {
            debugMsg('测试连接按钮被点击');
            detectAPIServer().then(result => {
                if (result) {
                    debugMsg(`连接测试成功: ${result}`, 'success');
                    apiBaseUrl = result;
                    document.getElementById('api-status').textContent = '已连接';
                    document.getElementById('api-status').className = 'status-value online';
                } else {
                    debugMsg('连接测试失败', 'error');
                    document.getElementById('api-status').textContent = '离线';
                    document.getElementById('api-status').className = 'status-value offline';
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            debugMsg('DOM加载完成');
            setTimeout(initializeSystem, 1000);
        });

        // 错误处理
        window.addEventListener('error', function(event) {
            debugMsg(`JavaScript错误: ${event.error?.message || event.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', function(event) {
            debugMsg(`Promise错误: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>

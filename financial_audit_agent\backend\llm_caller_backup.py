import os
import logging
from typing import Optional, Dict, Any, Tuple
from data_models import DataContext

# 尝试导入新版OpenAI库
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# 保持对旧版dashscope的兼容性
try:
    from dashscope import Generation
    from dashscope.api_entities.dashscope_response import Role
    DASHSCOPE_AVAILABLE = True
except ImportError:
    DASHSCOPE_AVAILABLE = False

# 导入模拟LLM调用器
try:
    from mock_llm_caller import MockLLMCaller
    MOCK_LLM_AVAILABLE = True
except ImportError:
    MOCK_LLM_AVAILABLE = False

class LLMCaller:
    def __init__(self, api_key: str, model_name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化LLM调用器

        Args:
            api_key: API密钥
            model_name: 模型名称
            config: 配置字典, 包含API模式, base_url等配置
        """
        self.api_key = api_key
        self.model = model_name
        self.config = config or {}

        # 确定使用的API模式
        self.api_mode = self.config.get('LLM_API_MODE', 'openai_compatible')
        self.base_url = self.config.get('LLM_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        self.temperature = self.config.get('LLM_TEMPERATURE', 0.7)
        self.max_tokens = self.config.get('LLM_MAX_TOKENS', 2000)

        # 检查是否启用模拟模式
        self.use_mock = self.config.get('LLM_USE_MOCK', False)
        self.mock_caller = None

        # 初始化客户端
        self._init_client()

    def _init_client(self):
        """初始化API客户端"""
        # 检查是否使用模拟模式
        if self.use_mock and MOCK_LLM_AVAILABLE:
            print("🤖 [LLM] 启用模拟模式 - 将生成测试数据")
            self.mock_caller = MockLLMCaller(self.api_key, self.model, self.config)
            return

        if self.api_mode == 'openai_compatible' and OPENAI_AVAILABLE:
            # 使用OpenAI兼容模式（推荐）
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            self.use_openai = True
            logging.info("使用OpenAI兼容模式连接阿里云百炼")
        elif self.api_mode == 'dashscope' and DASHSCOPE_AVAILABLE:
            # 使用传统DashScope模式
            Generation.api_key = self.api_key
            self.use_openai = False
            logging.info("使用传统DashScope模式连接阿里云百炼")
        else:
            # 自动选择可用的模式
            if OPENAI_AVAILABLE:
                self.client = OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url
                )
                self.use_openai = True
                logging.info("自动选择OpenAI兼容模式")
            elif DASHSCOPE_AVAILABLE:
                Generation.api_key = self.api_key
                self.use_openai = False
                logging.info("自动选择DashScope模式")
            else:
                raise ImportError("未找到可用的API库.请安装 openai 或 dashscope 库.")

    def query_semantic_rule(self, prompt: str, max_retries: int = 3) -> str:
        """向大模型发送一个具体, 有针对性的提示, 并返回响应文本."""
        # 如果使用模拟模式
        if self.mock_caller:
            return self.mock_caller.query_semantic_rule(prompt, max_retries)

        for attempt in range(max_retries):
            try:
                if self.use_openai:
                    return self._query_with_openai(prompt)
                else:
                    return self._query_with_dashscope(prompt)
            except KeyboardInterrupt:
                logging.info("用户中断了操作")
                return "错误: 用户中断操作"
            except Exception as e:
                logging.error(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return f"错误: LLM调用失败 - {str(e)}"
                else:
                    import time
                    time.sleep(2 ** attempt)  # 指数退避
        return "错误: 达到最大重试次数"

    def query_with_reasoning(self, prompt: str, max_retries: int = 3) -> tuple[str, str]:
        """
        向大模型发送提示并返回思维链和最终结果

        Returns:
            tuple: (思维链文本, 最终结果文本)
        """
        # 如果使用模拟模式
        if self.mock_caller:
            return self.mock_caller.query_with_reasoning(prompt, max_retries)
        """
        for attempt in range(max_retries):
            try:
                if self.use_openai:
                    return self._query_with_reasoning_openai(prompt)
                else:
                    return self._query_with_reasoning_dashscope(prompt)
            except KeyboardInterrupt:
                logging.info("用户中断了操作")
                return "错误: 用户中断操作", "错误: 用户中断操作"
            except Exception as e:
                logging.error(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    error_msg = f"错误: LLM调用失败 - {str(e)}"
                    return error_msg, error_msg
                else:
                    import time
                    time.sleep(2 ** attempt)  # 指数退避
        return "错误: 达到最大重试次数", "错误: 达到最大重试次数"

    def _query_with_openai(self, prompt: str) -> str:
        """使用OpenAI兼容模式调用, 支持思维链"""
        messages = [
            {"role": "system", "content": "你是一个专业的财务审核助手.请详细展示你的分析思考过程, 然后给出最终结果."},
            {"role": "user", "content": prompt}
        ]

        # 添加思维链相关参数
        call_params = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "stream": False
        }

        # 根据模型类型添加思维链参数
        if "qwen" in self.model.lower():
            # 通义千问模型的思维链参数
            call_params["extra_body"] = {
                "enable_reasoning": True,
                "reasoning_effort": "medium"  # low, medium, high
            }

        response = self.client.chat.completions.create(**call_params)

        # 检查是否有思维链内容
        choice = response.choices[0]
        content = choice.message.content

        # 如果响应中包含思维链, 提取并返回完整内容
        if hasattr(choice.message, 'reasoning') and choice.message.reasoning:
            reasoning_content = choice.message.reasoning
            return f"思考过程:\n{reasoning_content}\n\n最终结果:\n{content}"

        return content

    def _query_with_reasoning_openai(self, prompt: str) -> tuple[str, str]:
        """使用OpenAI兼容模式调用, 专门获取思维链"""

        # 强化系统提示,明确要求思维链
        system_prompt = """你是一个专业的财务审核助手.请严格按照以下格式回答:

**第一部分:详细思考过程**
请详细展示你的分析思考过程,包括:
1. 如何理解和解析每条审核规则
2. 如何在JSON数据中查找相关信息
3. 如何进行逻辑判断和推理
4. 得出结论的思维过程

**第二部分:审核结果**
然后给出最终的审核结果JSON格式.

请确保思考过程详细, 具体, 逻辑性强, 让用户能够清楚地看到AI的推理过程."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

        # 添加思维链相关参数
        call_params = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.3,  # 降低温度以获得更稳定的推理
            "max_tokens": self.max_tokens,
            "stream": False
        }

        # 根据模型类型添加思维链参数
        if "thinking" in self.model.lower():
            # Thinking模型的专门参数
            call_params["extra_body"] = {
                "enable_thinking": self.config.get('LLM_ENABLE_THINKING', True),
                "thinking_budget": self.config.get('LLM_THINKING_BUDGET', 8192),
                "result_format": "message"
            }
        elif "qwen" in self.model.lower():
            # 通义千问模型的思维链参数
            call_params["extra_body"] = {
                "enable_reasoning": True,
                "reasoning_effort": "high"  # 使用高质量思维链
            }

        response = self.client.chat.completions.create(**call_params)

        # 检查是否有思维链内容
        choice = response.choices[0]
        content = choice.message.content

        # 优先检查Thinking模型的专门字段
        if "thinking" in self.model.lower():
            # Thinking模型的专门处理
            if hasattr(choice.message, 'reasoning_content') and choice.message.reasoning_content:
                reasoning_content = choice.message.reasoning_content
                print(f"[LLM] Thinking模型获取到reasoning_content字段,长度: {len(reasoning_content)}")
                return reasoning_content, content
            elif hasattr(choice, 'reasoning_content') and choice.reasoning_content:
                reasoning_content = choice.reasoning_content
                print(f"[LLM] Thinking模型从choice获取到reasoning_content,长度: {len(reasoning_content)}")
                return reasoning_content, content
            elif hasattr(response, 'reasoning_content') and response.reasoning_content:
                reasoning_content = response.reasoning_content
                print(f"[LLM] Thinking模型从response获取到reasoning_content,长度: {len(reasoning_content)}")
                return reasoning_content, content

        # 检查通用的reasoning字段
        if hasattr(choice.message, 'reasoning_content') and choice.message.reasoning_content:
            reasoning_content = choice.message.reasoning_content
            print(f"[LLM] 获取到专门的reasoning_content字段,长度: {len(reasoning_content)}")
            return reasoning_content, content
        elif hasattr(choice.message, 'reasoning') and choice.message.reasoning:
            reasoning_content = choice.message.reasoning
            print(f"[LLM] 获取到reasoning字段,长度: {len(reasoning_content)}")
            return reasoning_content, content

        # 如果没有专门的思维链字段,从内容中分离
        print(f"[LLM] 未找到专门的reasoning字段,尝试从内容中提取")
        return self._extract_reasoning_from_content(content)

    def _query_with_reasoning_dashscope(self, prompt: str) -> tuple[str, str]:
        """使用传统DashScope模式调用,专门获取思维链"""
        messages = [
            {'role': Role.SYSTEM, 'content': '你是一个专业的财务审核助手.请按照以下格式回答:\n1. 首先详细展示你的分析思考过程\n2. 然后给出最终的审核结果\n请确保思考过程清晰, 逻辑性强.'},
            {'role': Role.USER, 'content': prompt}
        ]

        # 添加思维链相关参数
        call_params = {
            'model': self.model,
            'messages': messages,
            'result_format': 'text'
        }

        # 根据模型类型添加思维链参数
        if "thinking" in self.model.lower():
            # Thinking模型的专门参数
            call_params['enable_thinking'] = self.config.get('LLM_ENABLE_THINKING', True)
            call_params['thinking_budget'] = self.config.get('LLM_THINKING_BUDGET', 8192)
            call_params['result_format'] = 'message'
        elif "qwen" in self.model.lower():
            call_params['enable_reasoning'] = True
            call_params['reasoning_effort'] = 'high'  # 使用高质量思维链

        response = Generation.call(**call_params)

        if response.status_code == 200:
            content = response.output.text

            # 检查是否有思维链内容
            if hasattr(response.output, 'reasoning') and response.output.reasoning:
                reasoning_content = response.output.reasoning
                return reasoning_content, content

            # 如果没有专门的思维链字段,尝试从内容中分离
            return self._extract_reasoning_from_content(content)
        else:
            error_msg = f"错误: {response.code} - {response.message}"
            return error_msg, error_msg

    def _extract_reasoning_from_content(self, content: str) -> tuple[str, str]:
        """从响应内容中提取思维链和最终结果"""
        if not content:
            return "", ""

        print(f"[LLM] 开始从内容中提取思维链,内容长度: {len(content)}")
        print(f"[LLM] 内容预览: {content[:300]}...")

        # 尝试多种分割方式
        separators = [
            "**第一部分:详细思考过程**",
            "思考过程:",
            "分析过程:",
            "推理过程:",
            "最终结果:",
            "审核结果:",
            "结论:",
            "**第二部分:审核结果**",
            "## 审核结果",
            "### 最终结果"
        ]

        reasoning = ""
        result = content

        # 优先处理标准格式
        if "**第一部分:详细思考过程**" in content and "**第二部分:审核结果**" in content:
            parts = content.split("**第二部分:审核结果**")
            if len(parts) >= 2:
                reasoning_part = parts[0].replace("**第一部分:详细思考过程**", "").strip()
                result = parts[1].strip()
                reasoning = reasoning_part
                print(f"[LLM] 使用标准格式分割成功,思维链长度: {len(reasoning)}")
                return reasoning, result

        # 查找思考过程部分
        for sep in separators[1:4]:  # 思考过程分隔符
            if sep in content:
                parts = content.split(sep, 1)
                if len(parts) == 2:
                    reasoning_part = parts[1]
                    # 查找结果部分
                    for result_sep in separators[4:]:
                        if result_sep in reasoning_part:
                            reasoning_result_parts = reasoning_part.split(result_sep, 1)
                            reasoning = reasoning_result_parts[0].strip()
                            result = reasoning_result_parts[1].strip()
                            print(f"[LLM] 使用分隔符分割成功,思维链长度: {len(reasoning)}")
                            return reasoning, result
                    else:
                        # 如果没找到结果分隔符,整个作为思考过程
                        reasoning = reasoning_part.strip()
                        result = content
                    break

        # 如果没有找到明确的分割,尝试基于JSON位置分割
        import re
        json_pattern = r'\[.*?\]'
        json_match = re.search(json_pattern, content, re.DOTALL)
        if json_match:
            json_start = json_match.start()
            reasoning = content[:json_start].strip()
            result = content[json_start:].strip()
            print(f"[LLM] 使用JSON位置分割成功,思维链长度: {len(reasoning)}")
            return reasoning, result

        # 最后的备用方案:将前70%作为思维链,后30%作为结果
        if len(content) > 100:  # 只有内容足够长才使用比例分割
            split_point = int(len(content) * 0.7)
            reasoning = content[:split_point].strip()
            result = content[split_point:].strip()
            print(f"[LLM] 使用比例分割,思维链长度: {len(reasoning)}")
        else:
            reasoning = "AI正在分析审核规则和数据,进行逻辑推理..."
            result = content
            print(f"[LLM] 内容过短,使用默认思维链")

        return reasoning, result

    def _query_with_dashscope(self, prompt: str) -> str:
        """使用传统DashScope模式调用,支持思维链"""
        messages = [
            {'role': Role.SYSTEM, 'content': '你是一个专业的财务审核助手.请详细展示你的分析思考过程,然后给出最终结果.'},
            {'role': Role.USER, 'content': prompt}
        ]

        # 添加思维链相关参数
        call_params = {
            'model': self.model,
            'messages': messages,
            'result_format': 'text'
        }

        # 根据模型类型添加思维链参数
        if "qwen" in self.model.lower():
            call_params['enable_reasoning'] = True
            call_params['reasoning_effort'] = 'medium'  # low, medium, high

        response = Generation.call(**call_params)

        if response.status_code == 200:
            content = response.output.text

            # 检查是否有思维链内容
            if hasattr(response.output, 'reasoning') and response.output.reasoning:
                reasoning_content = response.output.reasoning
                return f"思考过程:\n{reasoning_content}\n\n最终结果:\n{content}"

            return content
        else:
            return f"错误: {response.code} - {response.message}"
    # 用于生成特定规则提示的静态方法
    @staticmethod
    def get_rule_15_prompt(reason_from_form: str, project_name_from_details: str) -> str:
        return f"""
        分析以下两个项目描述的语义相似度.
        - 描述A (从事前审批表): "{reason_from_form}"
        - 描述B (从招待明细): "{project_name_from_details}"
        这两个描述是否指向同一个项目或高度相关的活动?
        请仅回答"是"或"否",并附上简要解释.
        例如: 是,两者都指向同一个项目的施工阶段.
        """

    @staticmethod
    def get_rule_16_prompt(reimbursement_reason: str, expense_details: str) -> str:
        return f"""
        分析是否存在逻辑冲突.
        - 申报事由: "{reimbursement_reason}"
        - 实际消费内容: "{expense_details}"
        申报的事由与实际的消费内容之间是否存在逻辑矛盾或冲突?
        请仅回答"是"或"否",并附上简要解释.
        例如: 是,事由是"客户会议",但消费包含"KTV费用",存在冲突.
        """

    @staticmethod
    def get_rule_28_prompt(invoice_address: str) -> str:
        return f"""
        根据你的知识,判断地址"{invoice_address}"是否可能是一个风景名胜区或五星级酒店?
        请仅回答"是"或"否",如果知道,请指出具体可能是什么地方.
        例如: 是,这是华尔道夫酒店的地址.
        """

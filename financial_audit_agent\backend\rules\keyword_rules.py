from data_models import DataContext, AuditResult
from typing import List

class KeywordRules:
    def __init__(self, context: DataContext):
        self.context = context.all_extracted_data

    def check_rule_7_sensitive_keywords(self) -> AuditResult:
        """规则7：分析"招待对象"字段，判断其内容是否包含党政机关相关的关键词。"""
        target = self.context.get("招待明细", {}).get("招待对象", "")
        keywords = ["局", "科", "办公室", "军", "政", "委员会", "人大", "政协", "法院", "检察院", "公安"]
        found_keywords = [kw for kw in keywords if kw in str(target)]
        
        if found_keywords:
            return AuditResult(rule_id="规则7", status="WARNING", 
                             message=f"招待对象'{target}'中包含敏感关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则7", status="PASS", message="招待对象未发现敏感关键词。")

    def check_rule_8_entertainment_keywords(self) -> AuditResult:
        """规则8：分析"招待内容"字段，判断其内容是否包含娱乐活动相关的关键词。"""
        content = self.context.get("招待明细", {}).get("招待内容", "")
        entertainment_keywords = ["KTV", "酒吧", "夜总会", "桑拿", "按摩", "洗浴", "娱乐", "唱歌", "跳舞"]
        found_keywords = [kw for kw in entertainment_keywords if kw in str(content)]
        
        if found_keywords:
            return AuditResult(rule_id="规则8", status="WARNING", 
                             message=f"招待内容'{content}'中包含娱乐活动关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则8", status="PASS", message="招待内容未发现娱乐活动关键词。")

    def check_rule_13_luxury_keywords(self) -> AuditResult:
        """规则13：分析发票的"销售方名称"，判断是否包含高档消费场所的关键词。"""
        seller_name = self.context.get("发票", {}).get("销售方名称", "")
        luxury_keywords = ["五星", "豪华", "高级", "奢华", "会所", "俱乐部", "度假村", "温泉", "SPA"]
        found_keywords = [kw for kw in luxury_keywords if kw in str(seller_name)]
        
        if found_keywords:
            return AuditResult(rule_id="规则13", status="WARNING", 
                             message=f"销售方名称'{seller_name}'中包含高档消费场所关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则13", status="PASS", message="销售方名称未发现高档消费场所关键词。")

    def check_rule_14_alcohol_keywords(self) -> AuditResult:
        """规则14：分析发票的"货物或应税劳务名称"，判断是否包含酒类相关的关键词。"""
        goods_name = self.context.get("发票", {}).get("货物或应税劳务名称", "")
        alcohol_keywords = ["酒", "白酒", "红酒", "啤酒", "洋酒", "威士忌", "伏特加", "香槟", "茅台", "五粮液"]
        found_keywords = [kw for kw in alcohol_keywords if kw in str(goods_name)]
        
        if found_keywords:
            return AuditResult(rule_id="规则14", status="WARNING", 
                             message=f"货物名称'{goods_name}'中包含酒类关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则14", status="PASS", message="货物名称未发现酒类关键词。")

    def check_rule_17_gift_keywords(self) -> AuditResult:
        """规则17：分析"招待内容"，判断是否包含礼品赠送相关的关键词。"""
        content = self.context.get("招待明细", {}).get("招待内容", "")
        gift_keywords = ["礼品", "赠送", "纪念品", "礼物", "礼盒", "伴手礼", "土特产", "购物"]
        found_keywords = [kw for kw in gift_keywords if kw in str(content)]
        
        if found_keywords:
            return AuditResult(rule_id="规则17", status="WARNING", 
                             message=f"招待内容'{content}'中包含礼品赠送关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则17", status="PASS", message="招待内容未发现礼品赠送关键词。")

    def check_rule_18_travel_keywords(self) -> AuditResult:
        """规则18：分析"招待地点"，判断是否包含旅游景点相关的关键词。"""
        location = self.context.get("招待明细", {}).get("招待地点", "")
        travel_keywords = ["景区", "风景区", "旅游", "度假", "海滨", "山庄", "古镇", "名胜", "公园", "博物馆"]
        found_keywords = [kw for kw in travel_keywords if kw in str(location)]
        
        if found_keywords:
            return AuditResult(rule_id="规则18", status="WARNING", 
                             message=f"招待地点'{location}'中包含旅游景点关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则18", status="PASS", message="招待地点未发现旅游景点关键词。")

    def check_rule_19_high_amount_keywords(self) -> AuditResult:
        """规则19：分析货物名称，判断是否包含高价值商品的关键词。"""
        goods_name = self.context.get("发票", {}).get("货物或应税劳务名称", "")
        high_value_keywords = ["燕窝", "鱼翅", "鲍鱼", "海参", "松茸", "虫草", "人参", "高档", "进口"]
        found_keywords = [kw for kw in high_value_keywords if kw in str(goods_name)]
        
        if found_keywords:
            return AuditResult(rule_id="规则19", status="WARNING", 
                             message=f"货物名称'{goods_name}'中包含高价值商品关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则19", status="PASS", message="货物名称未发现高价值商品关键词。")

    def check_rule_20_private_club_keywords(self) -> AuditResult:
        """规则20：分析销售方名称，判断是否包含私人会所相关的关键词。"""
        seller_name = self.context.get("发票", {}).get("销售方名称", "")
        club_keywords = ["会所", "俱乐部", "私人", "VIP", "贵宾", "高尔夫", "马术", "游艇", "私密"]
        found_keywords = [kw for kw in club_keywords if kw in str(seller_name)]
        
        if found_keywords:
            return AuditResult(rule_id="规则20", status="WARNING", 
                             message=f"销售方名称'{seller_name}'中包含私人会所关键词: {', '.join(found_keywords)}。")
        else:
            return AuditResult(rule_id="规则20", status="PASS", message="销售方名称未发现私人会所关键词。")

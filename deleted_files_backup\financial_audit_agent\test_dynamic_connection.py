#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前后端动态连接的完整脚本
"""

import json
import time
import requests
import threading
from pathlib import Path


def test_api_endpoints():
    """测试所有API端点"""
    print("测试API端点...")
    
    endpoints = [
        '/api/status',
        '/api/report?doc_num=ZDBXD2025051300001',
        '/api/rules',
        '/api/progress'
    ]
    
    base_url = 'http://localhost:8001'
    
    for endpoint in endpoints:
        try:
            response = requests.get(f'{base_url}{endpoint}', timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {endpoint} - 正常")
                
                # 显示关键信息
                if endpoint == '/api/status':
                    print(f"   状态: {data.get('status', 'N/A')}")
                    print(f"   当前步骤: {data.get('current_step', 'N/A')}")
                elif endpoint.startswith('/api/report'):
                    summary = data.get('summary', {})
                    print(f"   总规则: {summary.get('total_rules_checked', 'N/A')}")
                    print(f"   通过: {summary.get('passed_count', 'N/A')}")
                elif endpoint == '/api/rules':
                    print(f"   总规则数: {data.get('total_rules', 'N/A')}")
            else:
                print(f"❌ {endpoint} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")


def simulate_status_updates():
    """模拟状态更新"""
    print("\n模拟状态更新...")
    
    status_file = Path(__file__).parent / "backend" / "audit_status.json"
    
    # 模拟不同的审核阶段
    test_statuses = [
        {
            "current_step": "data-loading",
            "status": "running",
            "message": "正在加载数据...",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detail": "加载表单和附件数据"
        },
        {
            "current_step": "rule-parsing",
            "status": "running",
            "message": "正在解析规则...",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detail": "解析审核规则文件"
        },
        {
            "current_step": "audit-execution",
            "status": "running",
            "message": "正在执行审核...",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detail": "执行智能审核规则"
        },
        {
            "current_step": "report-generation",
            "status": "audit-complete",
            "message": "审核完成！",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detail": "生成最终审核报告"
        }
    ]
    
    for i, status in enumerate(test_statuses):
        print(f"更新状态 {i+1}/4: {status['message']}")
        
        try:
            # 更新状态文件
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
            
            # 测试API是否返回更新后的状态
            time.sleep(1)  # 等待文件写入
            response = requests.get('http://localhost:8001/api/status', timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"   API返回: {data.get('current_step', 'N/A')} - {data.get('message', 'N/A')}")
            else:
                print(f"   API错误: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   更新失败: {e}")
        
        time.sleep(3)  # 等待3秒，模拟前端轮询间隔


def test_frontend_polling():
    """测试前端轮询机制"""
    print("\n测试前端轮询机制...")
    
    # 模拟前端的轮询行为
    print("模拟前端每3秒轮询一次状态...")
    
    for i in range(5):
        try:
            response = requests.get('http://localhost:8001/api/status', timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"轮询 {i+1}: {data.get('current_step', 'N/A')} - {data.get('message', 'N/A')}")
            else:
                print(f"轮询 {i+1}: HTTP {response.status_code}")
        except Exception as e:
            print(f"轮询 {i+1}: 错误 {e}")
        
        if i < 4:  # 最后一次不需要等待
            time.sleep(3)


def test_web_server():
    """测试Web服务器"""
    print("\n测试Web服务器...")
    
    try:
        response = requests.get('http://localhost:8002/frontend/ai_console.html', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正常")
            print(f"   页面大小: {len(response.text)} 字符")
            
            # 检查页面是否包含关键元素
            if 'ai_console_enhanced.js' in response.text:
                print("   ✅ 包含增强版JavaScript")
            if 'api/status' in response.text:
                print("   ✅ 包含API调用代码")
        else:
            print(f"❌ Web服务器错误: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Web服务器连接失败: {e}")


def main():
    """主函数"""
    print("前后端动态连接测试")
    print("=" * 50)
    
    # 1. 测试API端点
    test_api_endpoints()
    
    # 2. 测试Web服务器
    test_web_server()
    
    # 3. 测试前端轮询
    test_frontend_polling()
    
    # 4. 模拟状态更新
    simulate_status_updates()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n建议:")
    print("1. 在浏览器中打开: http://localhost:8002/frontend/ai_console.html?doc_num=ZDBXD2025051300001")
    print("2. 打开浏览器开发者工具查看控制台日志")
    print("3. 观察页面是否实时更新状态信息")
    print("4. 检查网络面板中的API调用")


if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端数据联动最终验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 8px 0;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        .data-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .highlight { background-color: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端数据联动最终验证</h1>
        
        <div class="highlight">
            <strong>验证目标：</strong>确认AI控制台页面能够实时显示后台审核数据
        </div>
        
        <div class="test-section">
            <h3>📡 1. API服务器连接测试</h3>
            <div id="api-test-status" class="status info">测试中...</div>
            <button onclick="testAPIConnection()">重新测试</button>
            <div id="api-test-result" class="data-display"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 2. 数据获取测试</h3>
            <div id="data-test-status" class="status info">等待测试...</div>
            <button onclick="testDataRetrieval()">测试数据获取</button>
            <div id="data-test-result" class="data-display"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 3. JavaScript功能测试</h3>
            <div id="js-test-status" class="status info">等待测试...</div>
            <button onclick="testJavaScriptFunctions()">测试JavaScript</button>
            <div id="js-test-result" class="data-display"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 4. 模拟数据更新测试</h3>
            <div id="update-test-status" class="status info">等待测试...</div>
            <button onclick="testDataUpdate()">模拟数据更新</button>
            <div id="update-test-result" class="data-display"></div>
        </div>
        
        <div class="test-section">
            <h3>📋 5. 最终验证结果</h3>
            <div id="final-result" class="status info">等待所有测试完成...</div>
            <button onclick="runAllTests()">运行所有测试</button>
        </div>
    </div>

    <script>
        let apiBaseUrl = null;
        let testResults = {
            api: false,
            data: false,
            js: false,
            update: false
        };

        // 测试API连接
        async function testAPIConnection() {
            const statusEl = document.getElementById('api-test-status');
            const resultEl = document.getElementById('api-test-result');
            
            statusEl.className = 'status info';
            statusEl.textContent = '🔍 检测API服务器...';
            
            const ports = [8001, 8002, 8003];
            let results = [];
            
            for (const port of ports) {
                try {
                    const response = await fetch(`http://localhost:${port}/api/status`, {
                        signal: AbortSignal.timeout(3000)
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        apiBaseUrl = `http://localhost:${port}`;
                        results.push(`✅ 端口 ${port}: 连接成功`);
                        results.push(`   状态: ${data.status}`);
                        results.push(`   消息: ${data.message}`);
                        testResults.api = true;
                        break;
                    }
                } catch (error) {
                    results.push(`❌ 端口 ${port}: ${error.message}`);
                }
            }
            
            if (testResults.api) {
                statusEl.className = 'status success';
                statusEl.textContent = `✅ API连接成功: ${apiBaseUrl}`;
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ API连接失败';
            }
            
            resultEl.textContent = results.join('\n');
        }

        // 测试数据获取
        async function testDataRetrieval() {
            if (!apiBaseUrl) {
                document.getElementById('data-test-status').textContent = '❌ 请先测试API连接';
                return;
            }
            
            const statusEl = document.getElementById('data-test-status');
            const resultEl = document.getElementById('data-test-result');
            
            statusEl.className = 'status info';
            statusEl.textContent = '📊 获取审核数据...';
            
            try {
                const docNum = new URLSearchParams(window.location.search).get('doc_num') || 'ZDBXD2025051300001';
                const response = await fetch(`${apiBaseUrl}/api/report?doc_num=${docNum}`);
                
                if (response.ok) {
                    const data = await response.json();
                    
                    let results = [];
                    results.push('=== 审核报告数据 ===');
                    if (data.summary) {
                        results.push(`总规则: ${data.summary.total_rules_checked}`);
                        results.push(`通过: ${data.summary.passed_count}`);
                        results.push(`失败: ${data.summary.failed_count}`);
                        results.push(`警告: ${data.summary.warning_count}`);
                    }
                    if (data.metadata) {
                        results.push(`数据源: ${data.metadata.source}`);
                        results.push(`时间戳: ${data.metadata.timestamp}`);
                    }
                    
                    statusEl.className = 'status success';
                    statusEl.textContent = '✅ 数据获取成功';
                    resultEl.textContent = results.join('\n');
                    testResults.data = true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 数据获取失败: ${error.message}`;
                resultEl.textContent = `错误: ${error.message}`;
            }
        }

        // 测试JavaScript功能
        function testJavaScriptFunctions() {
            const statusEl = document.getElementById('js-test-status');
            const resultEl = document.getElementById('js-test-result');
            
            let results = [];
            results.push('=== JavaScript环境检查 ===');
            
            // 检查全局对象
            results.push(`window.aiConsole: ${typeof window.aiConsole}`);
            results.push(`AIConsoleEnhanced: ${typeof AIConsoleEnhanced}`);
            
            // 检查关键DOM元素
            const keyElements = [
                'rules-completed',
                'total-rules',
                'data-link-status',
                'data-link-indicator'
            ];
            
            results.push('\n=== 关键DOM元素检查 ===');
            let elementsFound = 0;
            keyElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results.push(`✅ ${id}: 存在`);
                    elementsFound++;
                } else {
                    results.push(`❌ ${id}: 不存在`);
                }
            });
            
            if (elementsFound === keyElements.length) {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ JavaScript功能正常';
                testResults.js = true;
            } else {
                statusEl.className = 'status warning';
                statusEl.textContent = `⚠️ 部分功能缺失 (${elementsFound}/${keyElements.length})`;
            }
            
            resultEl.textContent = results.join('\n');
        }

        // 测试数据更新
        function testDataUpdate() {
            const statusEl = document.getElementById('update-test-status');
            const resultEl = document.getElementById('update-test-result');
            
            statusEl.className = 'status info';
            statusEl.textContent = '🎯 模拟数据更新...';
            
            try {
                // 模拟更新数据
                const mockData = {
                    total_rules_checked: 38,
                    passed_count: 35,
                    failed_count: 1,
                    warning_count: 2
                };
                
                let results = [];
                results.push('=== 模拟数据更新测试 ===');
                
                // 尝试更新DOM元素
                const rulesCompleted = document.getElementById('rules-completed');
                const totalRules = document.getElementById('total-rules');
                
                if (rulesCompleted) {
                    const oldValue = rulesCompleted.textContent;
                    rulesCompleted.textContent = '36';
                    results.push(`✅ rules-completed: ${oldValue} → 36`);
                } else {
                    results.push('❌ rules-completed: 元素不存在');
                }
                
                if (totalRules) {
                    const oldValue = totalRules.textContent;
                    totalRules.textContent = '38';
                    results.push(`✅ total-rules: ${oldValue} → 38`);
                } else {
                    results.push('❌ total-rules: 元素不存在');
                }
                
                // 测试AIConsoleEnhanced的updateRealStats方法
                if (window.aiConsole && typeof window.aiConsole.updateRealStats === 'function') {
                    window.aiConsole.updateRealStats(mockData);
                    results.push('✅ AIConsoleEnhanced.updateRealStats: 调用成功');
                    testResults.update = true;
                } else {
                    results.push('❌ AIConsoleEnhanced.updateRealStats: 方法不可用');
                }
                
                statusEl.className = testResults.update ? 'status success' : 'status warning';
                statusEl.textContent = testResults.update ? '✅ 数据更新测试成功' : '⚠️ 数据更新部分成功';
                resultEl.textContent = results.join('\n');
                
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 数据更新测试失败: ${error.message}`;
                resultEl.textContent = `错误: ${error.message}`;
            }
        }

        // 运行所有测试
        async function runAllTests() {
            await testAPIConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDataRetrieval();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testJavaScriptFunctions();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testDataUpdate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 显示最终结果
            const finalEl = document.getElementById('final-result');
            const passedTests = Object.values(testResults).filter(Boolean).length;
            const totalTests = Object.keys(testResults).length;
            
            if (passedTests === totalTests) {
                finalEl.className = 'status success';
                finalEl.textContent = `🎉 所有测试通过 (${passedTests}/${totalTests}) - 前端数据联动功能正常！`;
            } else {
                finalEl.className = 'status warning';
                finalEl.textContent = `⚠️ 部分测试通过 (${passedTests}/${totalTests}) - 需要进一步检查`;
            }
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>

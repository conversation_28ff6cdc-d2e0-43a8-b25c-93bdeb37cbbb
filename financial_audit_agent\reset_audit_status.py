#!/usr/bin/env python3
"""
重置审核状态文件脚本
用于清理所有状态文件，避免缓存导致的"审核已完成"误判
"""

import os
import json
import time
from pathlib import Path

def reset_audit_status():
    """重置所有审核状态文件为初始状态"""
    
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 需要重置的状态文件路径（新版：只有一个权威文件）
    status_files = [
        project_root / "backend" / "audit_state.json"  # 唯一权威状态文件
    ]

    # 需要删除的旧状态文件（如果存在）
    old_status_files = [
        project_root / "audit_status.json",
        project_root / "backend" / "audit_status.json",
        project_root / "frontend" / "audit_status.json",
        project_root / "frontend" / "audit_status.js"
    ]
    
    # 初始状态数据（新版：标准化格式）
    initial_state = {
        "audit_id": None,
        "audit_status": "ready",
        "current_phase": "ready",
        "progress_percent": 0,
        "start_time": None,
        "completion_time": None,
        "summary": {
            "total_rules": 0,
            "completed_rules": 0,
            "passed_rules": 0,
            "failed_rules": 0,
            "warning_rules": 0
        },
        "ai_thinking": "系统就绪，等待开始审核",
        "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        "message": "系统就绪，等待开始审核",
        "detail": "请启动审核流程"
    }
    

    
    print("🔄 开始重置审核状态文件...")

    # 删除旧的状态文件
    print("\n🗑️ 清理旧状态文件...")
    for old_file in old_status_files:
        if old_file.exists():
            try:
                old_file.unlink()
                print(f"   ✅ 删除旧文件: {old_file}")
            except Exception as e:
                print(f"   ❌ 删除失败 {old_file}: {e}")

    # 创建新的权威状态文件
    print("\n📝 创建权威状态文件...")
    for status_file in status_files:
        try:
            # 确保目录存在
            status_file.parent.mkdir(parents=True, exist_ok=True)

            # 写入初始状态
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(initial_state, f, ensure_ascii=False, indent=2)

            print(f"   ✅ 创建成功: {status_file}")

        except Exception as e:
            print(f"   ❌ 创建失败 {status_file}: {e}")
    
    # 清理审核报告文件（重要：避免前端误判为已完成）
    reports_dir = project_root / "audit_reports"
    if reports_dir.exists():
        print(f"\n📁 清理审核报告目录: {reports_dir}")
        report_files = list(reports_dir.glob("*.json"))
        if report_files:
            print(f"   发现 {len(report_files)} 个报告文件，正在删除...")
            for report_file in report_files:
                try:
                    report_file.unlink()
                    print(f"   ✅ 删除: {report_file.name}")
                except Exception as e:
                    print(f"   ❌ 删除失败 {report_file.name}: {e}")
        else:
            print("   无报告文件需要清理")
    
    print(f"\n🎉 状态重置完成！")
    print(f"📝 所有状态文件已重置为初始状态")
    print(f"🗑️ 审核报告文件已清理")
    print(f"🌐 现在可以重新启动审核流程")
    print(f"\n💡 建议操作：")
    print(f"   1. 按 Ctrl+F5 强制刷新浏览器页面")
    print(f"   2. 确保没有后台Python进程在运行")
    print(f"   3. 重新启动审核系统")

if __name__ == "__main__":
    reset_audit_status()

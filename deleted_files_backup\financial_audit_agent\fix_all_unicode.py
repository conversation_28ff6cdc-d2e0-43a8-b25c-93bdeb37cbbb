#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复所有文件中的Unicode编码问题
"""

import re
from pathlib import Path


def fix_unicode_in_file(file_path):
    """修复文件中的Unicode字符"""
    print(f"修复文件: {file_path}")
    
    # emoji到文本的映射
    emoji_map = {
        '🚀': '[启动]',
        '🎯': '[目标]', 
        '📄': '[文档]',
        '⚙️': '[设置]',
        '🌐': '[网络]',
        '📁': '[目录]',
        '📋': '[清单]',
        '🔍': '[查找]',
        '💡': '[提示]',
        '🎉': '[完成]',
        '❌': '[错误]',
        '⚠️': '[警告]',
        '✅': '[成功]',
        '📡': '[API]',
        '🔄': '[处理]',
        '📊': '[统计]',
        '🛑': '[停止]',
        '🔧': '[工具]',
        '🤖': '[AI]',
        '📝': '[文档]',
        '💾': '[保存]',
        '🔥': '[热点]',
        '⭐': '[重要]',
        '🎨': '[界面]',
        '🔐': '[安全]',
        '📈': '[进度]',
        '🎪': '[演示]',
        '🏃': '[运行]',
        '🎭': '[模式]',
        '🎬': '[开始]',
        '🎵': '[音频]',
        '🎮': '[游戏]',
        '🎲': '[随机]',
        '🎳': '[测试]',
        '🎺': '[通知]',
        '🎻': '[音乐]',
        '🎼': '[配置]',
        '🎽': '[运动]',
        '🎾': '[球类]',
        '🎿': '[滑雪]',
        '🏀': '[篮球]',
        '🏁': '[结束]',
        '🏂': '[滑板]',
        '🏃': '[跑步]',
        '🏄': '[冲浪]',
        '🏅': '[奖牌]',
        '🏆': '[奖杯]',
        '🏇': '[赛马]',
        '🏈': '[橄榄球]',
        '🏉': '[橄榄球]',
        '🏊': '[游泳]',
        '🏋': '[举重]',
        '🏌': '[高尔夫]',
        '🏍': '[摩托]',
        '🏎': '[赛车]',
        '🏏': '[板球]',
        '🏐': '[排球]',
        '🏑': '[曲棍球]',
        '🏒': '[冰球]',
        '🏓': '[乒乓球]',
        '🏔': '[山峰]',
        '🏕': '[露营]',
        '🏖': '[海滩]',
        '🏗': '[建设]',
        '🏘': '[房屋]',
        '🏙': '[城市]',
        '🏚': '[废墟]',
        '🏛': '[建筑]',
        '🏜': '[沙漠]',
        '🏝': '[岛屿]',
        '🏞': '[风景]',
        '🏟': '[体育场]',
        '🏠': '[房子]',
        '🏡': '[花园]',
        '🏢': '[办公楼]',
        '🏣': '[邮局]',
        '🏤': '[欧洲邮局]',
        '🏥': '[医院]',
        '🏦': '[银行]',
        '🏧': '[ATM]',
        '🏨': '[酒店]',
        '🏩': '[爱情酒店]',
        '🏪': '[便利店]',
        '🏫': '[学校]',
        '🏬': '[百货商店]',
        '🏭': '[工厂]',
        '🏮': '[灯笼]',
        '🏯': '[城堡]',
        '🏰': '[欧洲城堡]'
    }
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换emoji字符
        original_content = content
        for emoji, replacement in emoji_map.items():
            content = content.replace(emoji, replacement)
        
        # 如果有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 已修复 {file_path}")
            return True
        else:
            print(f"- 无需修复 {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ 修复失败 {file_path}: {e}")
        return False


def main():
    """主函数"""
    print("批量修复Unicode编码问题")
    print("=" * 50)
    
    # 需要修复的文件列表
    files_to_fix = [
        "start_backend_v2.py",
        "backend/auditor_v2/run_audit_v2.py",
        "backend/auditor_v2/orchestrator_v2.py",
        "backend/orchestrator.py",
        "simple_launcher.py"
    ]
    
    project_root = Path(__file__).parent
    fixed_count = 0
    
    for file_name in files_to_fix:
        file_path = project_root / file_name
        if file_path.exists():
            if fix_unicode_in_file(file_path):
                fixed_count += 1
        else:
            print(f"- 文件不存在: {file_path}")
    
    print(f"\n修复完成: {fixed_count} 个文件")
    print("现在可以正常启动服务了")


if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容刷新修复测试</title>
    <link rel="stylesheet" href="ai_console.css">
    <style>
        body {
            background: #0a0a0a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1a1a2e;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #00ffff;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background: #16213e;
            border-radius: 8px;
            border-left: 4px solid #00ffff;
        }
        
        .test-button {
            background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
        }
        
        .status-display {
            background: #0a0a0a;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        
        .log-info { color: #00ffff; }
        .log-success { color: #00ff88; }
        .log-warning { color: #ffaa00; }
        .log-error { color: #ff4444; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 AI控制台内容刷新修复测试</h1>
        
        <div class="test-section">
            <h2>📋 测试说明</h2>
            <p>此页面用于测试AI控制台内容刷新问题的修复效果：</p>
            <ul>
                <li>✅ 防止重复刷新和重新显示之前的内容</li>
                <li>✅ 优化规则解析逻辑，避免重复匹配</li>
                <li>✅ 改进状态管理，正确处理完成状态</li>
                <li>✅ 增量更新内容而非完全替换</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>🧪 模拟测试</h2>
            <button class="test-button" onclick="testIncrementalUpdate()">测试增量更新</button>
            <button class="test-button" onclick="testCompletionStatus()">测试完成状态</button>
            <button class="test-button" onclick="testRuleParsing()">测试规则解析</button>
            <button class="test-button" onclick="testStateManagement()">测试状态管理</button>
            <button class="test-button" onclick="clearLogs()">清空日志</button>
            
            <div class="status-display" id="testLogs"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 AI思考内容模拟区域</h2>
            <div class="thinking-section">
                <div class="thinking-header">
                    <h3 class="thinking-title">🤖 AI正在深度分析...</h3>
                </div>
                <div class="thinking-content" id="thinking-content">
                    <!-- 模拟内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="js/state_manager.js"></script>
    <script src="ai_console_enhanced.js"></script>
    <script>
        let testConsole;
        let logCounter = 0;

        // 初始化测试环境
        document.addEventListener('DOMContentLoaded', function() {
            // 创建模拟的AI控制台实例
            testConsole = new AIConsoleEnhanced();
            testConsole.init();
            
            addLog('info', '测试环境初始化完成');
        });

        function addLog(type, message) {
            const logsContainer = document.getElementById('testLogs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${++logCounter}] ${new Date().toLocaleTimeString()} - ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function testIncrementalUpdate() {
            addLog('info', '开始测试增量更新...');
            
            const baseContent = "## 🔍 附件完整性检查 (阶段 1/4)\n\n正在分析第一阶段...";
            const incrementalContent = baseContent + "\n\n✅ 规则1：检查是否上传发票 - 通过\n✅ 规则2：检查是否上传事前审批表 - 通过";
            
            // 模拟第一次更新
            testConsole.updateAIThinking(baseContent);
            addLog('success', '第一次内容更新完成');
            
            // 模拟增量更新
            setTimeout(() => {
                testConsole.updateAIThinking(incrementalContent);
                addLog('success', '增量更新完成 - 应该只添加新内容');
            }, 2000);
        }

        function testCompletionStatus() {
            addLog('info', '开始测试完成状态...');
            
            // 模拟完成状态
            const completedState = {
                audit_status: 'completed',
                completion_time: new Date().toISOString(),
                detail: '耗时 505.44 秒',
                message: '✅ 审核完成！共检查38条规则，通过34条'
            };
            
            // 设置状态管理器的当前状态
            if (testConsole.stateManager) {
                testConsole.stateManager.currentState = completedState;
            }
            
            testConsole.updateCompletionStatus(completedState);
            addLog('success', '完成状态显示测试完成');
        }

        function testRuleParsing() {
            addLog('info', '开始测试规则解析...');
            
            const testContent = `
## 审核结果

[
  {
    "rule_id": "规则1：检查是否上传发票",
    "status": "通过",
    "reason": "附件概览中包含发票"
  },
  {
    "rule_id": "规则2：检查是否上传事前审批表", 
    "status": "通过",
    "reason": "附件概览中包含事前审批表"
  }
]
            `;
            
            const phaseProgress = testConsole.parseAIThinkingForRuleProgress(testContent);
            addLog('success', `规则解析完成 - 附件阶段: ${phaseProgress.attachment.completed}/${phaseProgress.attachment.total}`);
        }

        function testStateManagement() {
            addLog('info', '开始测试状态管理...');
            
            const oldState = {
                audit_status: 'running',
                current_phase: 'attachment',
                progress_percent: 20,
                ai_thinking: '正在分析...'
            };
            
            const newState = {
                audit_status: 'completed',
                current_phase: 'finished',
                progress_percent: 100,
                ai_thinking: '正在分析...完成分析'
            };
            
            if (testConsole.stateManager) {
                testConsole.stateManager.currentState = oldState;
                const hasChanged = testConsole.stateManager.hasStateChanged(newState);
                addLog('success', `状态变化检测: ${hasChanged ? '检测到变化' : '无变化'}`);
            }
        }

        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '';
            logCounter = 0;
            addLog('info', '日志已清空');
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI审核结果测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f3a 100%);
            color: #e2e8f0;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(26, 31, 58, 0.4);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4ff;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-link {
            background: linear-gradient(135deg, #8b5cf6, #00d4ff);
            border: none;
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
            display: block;
        }
        
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.5);
            text-decoration: none;
            color: white;
        }
        
        .test-link h3 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
        }
        
        .test-link p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }
        
        .info-section {
            background: rgba(42, 47, 74, 0.4);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }
        
        .info-section h3 {
            color: #00d4ff;
            margin-top: 0;
        }
        
        .file-list {
            list-style: none;
            padding: 0;
        }
        
        .file-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.1);
        }
        
        .file-list li:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-success {
            background: #00ff88;
        }
        
        .status-warning {
            background: #ffd700;
        }
        
        .status-error {
            background: #ff6b35;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧠 AI审核结果测试页面</h1>
            <p>测试AI思维链集成功能的不同场景</p>
        </div>
        
        <div class="test-links">
            <a href="frontend/ai_results.html?doc=123" class="test-link">
                <h3>📋 测试报告123</h3>
                <p>加载 audit_report_123.json<br>包含完整的AI思维链数据</p>
            </a>
            
            <a href="frontend/ai_results.html?doc=ZDBXD2025042900003" class="test-link">
                <h3>📊 标准审核报告</h3>
                <p>加载标准格式的审核报告<br>测试基础功能</p>
            </a>
            
            <a href="frontend/ai_results.html" class="test-link">
                <h3>🔄 自动检测模式</h3>
                <p>不指定文件，自动检测可用报告<br>测试默认加载逻辑</p>
            </a>
            
            <a href="test_ai_thinking_chain_ui.html" class="test-link">
                <h3>🧪 完整功能演示</h3>
                <p>独立的AI思维链功能演示<br>包含测试数据和交互功能</p>
            </a>
        </div>
        
        <div class="info-section">
            <h3>📁 可用的审核报告文件</h3>
            <ul class="file-list" id="file-list">
                <li><span class="status-indicator status-success"></span>audit_report_123.json - 包含AI思维链</li>
                <li><span class="status-indicator status-success"></span>audit_report_ZDBXD2025042900003.json - 标准报告</li>
                <li><span class="status-indicator status-success"></span>enhanced_audit_report_ZDBXD2025042900003.json - 增强报告</li>
                <li><span class="status-indicator status-success"></span>demo_enhanced_report.json - 演示报告</li>
            </ul>
        </div>
        
        <div class="info-section">
            <h3>🎯 测试要点</h3>
            <ul>
                <li><strong>AI思维链按钮显示</strong>: 检查"查看AI思考过程"按钮是否出现</li>
                <li><strong>思维链内容展示</strong>: 验证AI思考过程的完整展示</li>
                <li><strong>阶段展开功能</strong>: 测试手风琴式展开/收起</li>
                <li><strong>搜索功能</strong>: 验证思维链内容搜索和高亮</li>
                <li><strong>复制功能</strong>: 测试内容复制到剪贴板</li>
                <li><strong>元数据显示</strong>: 检查思维链元数据信息</li>
                <li><strong>响应式设计</strong>: 在不同屏幕尺寸下的显示效果</li>
            </ul>
        </div>
        
        <div class="info-section">
            <h3>🔧 故障排除</h3>
            <ul>
                <li><strong>按钮不显示</strong>: 检查审核报告是否包含 ai_thinking_chain 字段</li>
                <li><strong>内容不加载</strong>: 打开浏览器开发者工具查看控制台错误</li>
                <li><strong>样式异常</strong>: 确保CSS文件正确加载</li>
                <li><strong>功能无响应</strong>: 检查JavaScript文件是否正确加载</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 检查文件可用性
        async function checkFileAvailability() {
            const files = [
                'audit_reports/audit_report_123.json',
                'audit_reports/audit_report_ZDBXD2025042900003.json', 
                'audit_reports/enhanced_audit_report_ZDBXD2025042900003.json',
                'audit_reports/demo_enhanced_report.json'
            ];
            
            const fileList = document.getElementById('file-list');
            fileList.innerHTML = '';
            
            for (const file of files) {
                const li = document.createElement('li');
                const indicator = document.createElement('span');
                indicator.className = 'status-indicator';
                
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        const data = await response.json();
                        const hasAiThinking = data.ai_thinking_chain ? true : false;
                        
                        indicator.className += hasAiThinking ? ' status-success' : ' status-warning';
                        li.innerHTML = `${indicator.outerHTML}${file} - ${hasAiThinking ? '包含AI思维链' : '无AI思维链'}`;
                    } else {
                        indicator.className += ' status-error';
                        li.innerHTML = `${indicator.outerHTML}${file} - 文件不存在`;
                    }
                } catch (error) {
                    indicator.className += ' status-error';
                    li.innerHTML = `${indicator.outerHTML}${file} - 加载失败`;
                }
                
                fileList.appendChild(li);
            }
        }
        
        // 页面加载时检查文件
        document.addEventListener('DOMContentLoaded', checkFileAvailability);
    </script>
</body>
</html>

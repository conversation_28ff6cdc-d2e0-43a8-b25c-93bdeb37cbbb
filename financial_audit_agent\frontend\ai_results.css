/* AI审核结果页面 - 科技感样式 */
:root {
    /* 科技感配色 */
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f3a;
    --bg-tertiary: #2a2f4a;
    --accent-blue: #00d4ff;
    --accent-green: #00ff88;
    --accent-purple: #8b5cf6;
    --accent-orange: #ff6b35;
    --accent-red: #ff3366;
    --accent-gold: #ffd700;
    --text-primary: #ffffff;
    --text-secondary: #a0a9c0;
    --text-muted: #6b7280;
    
    /* 发光效果 */
    --glow-blue: 0 0 30px rgba(0, 212, 255, 0.6);
    --glow-green: 0 0 30px rgba(0, 255, 136, 0.6);
    --glow-purple: 0 0 30px rgba(139, 92, 246, 0.6);
    --glow-gold: 0 0 30px rgba(255, 215, 0, 0.6);
    
    /* 字体 */
    --font-primary: '<PERSON><PERSON><PERSON>', sans-serif;
    --font-mono: 'Orbitron', monospace;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
}

/* 宇宙背景效果 */
.cosmic-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a0e1a 0%, #1a1f3a 50%, #2a2f4a 100%);
    animation: cosmicPulse 10s ease-in-out infinite alternate;
}

.cosmic-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 212, 255, 0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(139, 92, 246, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(0, 255, 136, 0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: starTwinkle 8s linear infinite;
}

@keyframes cosmicPulse {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
}

@keyframes starTwinkle {
    0% { transform: translateY(0); }
    100% { transform: translateY(-200px); }
}

/* 主容器 */
.results-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    gap: 40px;
}



/* 顶部标题区域 */
.results-header {
    text-align: center;
    position: relative;
    padding: 60px 0;
    background: rgba(26, 31, 58, 0.3);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    overflow: hidden;
}

.header-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
    animation: headerGlow 6s ease-in-out infinite alternate;
}

@keyframes headerGlow {
    0% { transform: rotate(0deg) scale(1); }
    100% { transform: rotate(180deg) scale(1.1); }
}

.ai-emblem {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 30px;
}

.emblem-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: var(--accent-blue);
    border-radius: 50%;
    box-shadow: var(--glow-blue);
    animation: emblemPulse 3s ease-in-out infinite;
}

.emblem-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring-1 {
    width: 60px;
    height: 60px;
    border-color: var(--accent-blue);
    border-top-color: transparent;
    animation: ringRotate1 4s linear infinite;
}

.ring-2 {
    width: 80px;
    height: 80px;
    border-color: var(--accent-purple);
    border-right-color: transparent;
    animation: ringRotate2 6s linear infinite reverse;
}

.ring-3 {
    width: 100px;
    height: 100px;
    border-color: var(--accent-green);
    border-bottom-color: transparent;
    animation: ringRotate3 8s linear infinite;
}

@keyframes emblemPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes ringRotate1 {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes ringRotate2 {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes ringRotate3 {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.main-title {
    font-family: var(--font-mono);
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple), var(--accent-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 300;
    margin-bottom: 30px;
}

.scan-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
    animation: scanLine 3s ease-in-out infinite;
}

@keyframes scanLine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 整体状态展示 */
.overall-status {
    display: flex;
    justify-content: center;
    margin: 40px 0;
}

.status-hologram {
    position: relative;
    background: rgba(26, 31, 58, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    min-width: 400px;
    text-align: center;
    overflow: hidden;
}

.hologram-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.status-icon {
    position: relative;
    width: 80px;
    height: 80px;
}

.icon-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--accent-green);
    box-shadow: var(--glow-green);
    animation: iconCorePulse 2s ease-in-out infinite;
}

.icon-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border: 2px solid rgba(0, 255, 136, 0.5);
    border-radius: 50%;
    animation: iconPulseExpand 2s ease-in-out infinite;
}

@keyframes iconCorePulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes iconPulseExpand {
    0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

#status-title {
    font-family: var(--font-mono);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-green);
    margin-bottom: 10px;
}

#status-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.hologram-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: gridMove 10s linear infinite;
    opacity: 0.3;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

/* 统计数据展示 */
.stats-dashboard {
    background: rgba(26, 31, 58, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
}

.dashboard-title {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-title h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.title-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-blue), transparent);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.stat-card {
    position: relative;
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-blue);
    box-shadow: var(--glow-blue);
}

.card-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
    animation: cardGlow 4s ease-in-out infinite alternate;
}

@keyframes cardGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

.card-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.card-number {
    font-family: var(--font-mono);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--accent-blue);
    text-shadow: var(--glow-blue);
}

.card-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-card.passed-rules .card-number {
    color: var(--accent-green);
    text-shadow: var(--glow-green);
}

.stat-card.warning-rules .card-number {
    color: var(--accent-orange);
    text-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
}

.stat-card.failed-rules .card-number {
    color: var(--accent-red);
    text-shadow: 0 0 30px rgba(255, 51, 102, 0.6);
}

.card-circuit {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
    animation: circuitFlow 2s ease-in-out infinite;
}

@keyframes circuitFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* AI分析洞察 */
.ai-insights {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 20px;
    padding: 40px;
}

.insights-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.insights-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--accent-purple);
}

.neural-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.neural-dot {
    width: 10px;
    height: 10px;
    background: var(--accent-purple);
    border-radius: 50%;
    animation: neuralPulse 1.5s ease-in-out infinite;
}

@keyframes neuralPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.3); }
}

.insight-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 40px;
}

.loading-brain {
    display: flex;
    gap: 8px;
}

.brain-wave {
    width: 4px;
    height: 30px;
    background: var(--accent-purple);
    border-radius: 2px;
    animation: brainWave 1.5s ease-in-out infinite;
}

.brain-wave:nth-child(2) {
    animation-delay: 0.2s;
}

.brain-wave:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes brainWave {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(2); }
}

/* 详细结果表格 */
.detailed-results {
    background: rgba(26, 31, 58, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
}

.results-header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.results-header-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.filter-controls {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
    color: var(--text-primary);
    box-shadow: var(--glow-blue);
}

.results-table-container {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table thead {
    background: rgba(0, 212, 255, 0.1);
}

.results-table th {
    padding: 20px;
    text-align: left;
    font-weight: 600;
    color: var(--accent-blue);
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    font-size: 1rem;
}

.results-table td {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    vertical-align: middle;
}

.results-table tbody tr {
    transition: all 0.3s ease;
}

.results-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-badge.pass {
    background: rgba(0, 255, 136, 0.2);
    color: var(--accent-green);
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-badge.warning {
    background: rgba(255, 107, 53, 0.2);
    color: var(--accent-orange);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.status-badge.fail {
    background: rgba(255, 51, 102, 0.2);
    color: var(--accent-red);
    border: 1px solid rgba(255, 51, 102, 0.3);
}

.status-badge.unknown {
    background: rgba(139, 92, 246, 0.2);
    color: var(--accent-purple);
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.risk-level {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 500;
}

.risk-level.low {
    background: rgba(0, 255, 136, 0.2);
    color: var(--accent-green);
}

.risk-level.medium {
    background: rgba(255, 107, 53, 0.2);
    color: var(--accent-orange);
}

.risk-level.high {
    background: rgba(255, 51, 102, 0.2);
    color: var(--accent-red);
}

.loading-row {
    text-align: center;
    padding: 40px !important;
}

.table-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-top-color: var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 操作按钮区域 */
.action-panel {
    position: relative;
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 40px;
    background: rgba(26, 31, 58, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
}

.panel-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
    animation: panelGlow 8s ease-in-out infinite alternate;
}

@keyframes panelGlow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.action-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-family: var(--font-primary);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    z-index: 2;
}

.action-btn.primary {
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
    color: var(--text-primary);
    box-shadow: var(--glow-blue);
}

.action-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 40px rgba(0, 212, 255, 0.8);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.3rem;
}

.btn-ripple {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover .btn-ripple {
    left: 100%;
}

/* 页脚信息 */
.results-footer {
    background: rgba(26, 31, 58, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-signature {
    display: flex;
    align-items: center;
    gap: 15px;
}

.signature-icon {
    font-size: 2rem;
    animation: signatureGlow 3s ease-in-out infinite;
}

@keyframes signatureGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.5) drop-shadow(0 0 10px var(--accent-blue)); }
}

.signature-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.signature-title {
    font-family: var(--font-mono);
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-blue);
}

.signature-version {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.generation-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    text-align: right;
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: var(--glow-blue);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--accent-red);
}

.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.export-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-option:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--accent-blue);
    transform: translateY(-2px);
}

.option-icon {
    font-size: 2rem;
}

.option-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 成功提示 */
.success-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    background: rgba(0, 255, 136, 0.2);
    border: 1px solid var(--accent-green);
    border-radius: 15px;
    padding: 15px 20px;
    box-shadow: var(--glow-green);
    animation: toastSlideIn 0.5s ease-out;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-icon {
    font-size: 1.5rem;
}

.toast-message {
    color: var(--accent-green);
    font-weight: 500;
}

@keyframes toastSlideIn {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .results-container {
        padding: 30px 15px;
        gap: 30px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .main-title {
        font-size: 2rem;
    }

    .results-header {
        padding: 40px 20px;
    }

    .results-header-section {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .filter-controls {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-panel {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .results-table {
        font-size: 0.9rem;
    }

    .results-table th,
    .results-table td {
        padding: 12px 15px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .export-options {
        grid-template-columns: 1fr;
    }

    .status-hologram {
        min-width: auto;
        padding: 30px 20px;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-blue);
    border-radius: 4px;
    box-shadow: var(--glow-blue);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-purple);
}

/* 思维链内容滚动条样式 */
.thinking-text::-webkit-scrollbar,
.combined-thinking-content::-webkit-scrollbar {
    width: 8px;
}

.thinking-text::-webkit-scrollbar-track,
.combined-thinking-content::-webkit-scrollbar-track {
    background: rgba(10, 14, 26, 0.3);
    border-radius: 4px;
}

.thinking-text::-webkit-scrollbar-thumb,
.combined-thinking-content::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.3);
    border-radius: 4px;
}

.thinking-text::-webkit-scrollbar-thumb:hover,
.combined-thinking-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 212, 255, 0.5);
}

/* AI思维链展示区域 */
.ai-thinking-chain {
    background: rgba(26, 31, 58, 0.4);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    animation: slideInUp 0.6s ease-out;
}

.thinking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.thinking-header h3 {
    font-family: var(--font-mono);
    font-size: 1.8rem;
    color: var(--accent-blue);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.thinking-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.control-btn {
    background: rgba(42, 47, 74, 0.8);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 15px;
    padding: 8px 16px;
    color: var(--text-secondary);
    font-family: var(--font-primary);
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(0, 212, 255, 0.1);
    color: var(--accent-blue);
    border-color: var(--accent-blue);
    transform: translateY(-1px);
}

.thinking-search {
    margin-bottom: 25px;
}

.search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-input {
    flex: 1;
    background: rgba(42, 47, 74, 0.6);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 25px;
    padding: 12px 20px;
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--accent-blue);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-btn {
    background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
}

.search-results {
    background: rgba(42, 47, 74, 0.4);
    border-radius: 10px;
    padding: 15px;
    display: none;
    max-height: 200px;
    overflow-y: auto;
}

.search-result-item {
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid var(--accent-blue);
}

.search-result-item:hover {
    background: rgba(0, 212, 255, 0.1);
}

.search-result-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.search-highlight {
    background: rgba(255, 215, 0, 0.3);
    color: var(--accent-gold);
    padding: 2px 4px;
    border-radius: 3px;
}

/* 思维链内容区域 */
.thinking-content {
    background: rgba(42, 47, 74, 0.3);
    border-radius: 15px;
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
    border: 1px solid rgba(0, 212, 255, 0.1);
}

.thinking-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--text-secondary);
}

.thinking-phase {
    background: rgba(26, 31, 58, 0.6);
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.thinking-phase:hover {
    border-color: var(--accent-blue);
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
}

.phase-header {
    background: rgba(0, 212, 255, 0.1);
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.phase-header:hover {
    background: rgba(0, 212, 255, 0.15);
}

.phase-title {
    font-family: var(--font-mono);
    font-size: 1.1rem;
    color: var(--accent-blue);
    font-weight: 600;
}

.phase-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.phase-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.phase-status.completed {
    background: rgba(0, 255, 136, 0.2);
    color: var(--accent-green);
    border: 1px solid var(--accent-green);
}

.phase-status.running {
    background: rgba(255, 107, 53, 0.2);
    color: var(--accent-orange);
    border: 1px solid var(--accent-orange);
}

.phase-expand-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    color: var(--accent-blue);
}

.phase-header.expanded .phase-expand-icon {
    transform: rotate(180deg);
}

.phase-content {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease;
}

.phase-content.expanded {
    padding: 20px;
    max-height: none;
    height: auto;
}

.thinking-text {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
    white-space: pre-wrap;
    font-family: 'Consolas', 'Monaco', monospace;
    background: rgba(10, 14, 26, 0.5);
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--accent-blue);
    margin-top: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.combined-thinking {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.combined-thinking-title {
    font-family: var(--font-mono);
    font-size: 1.2rem;
    color: var(--accent-purple);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.combined-thinking-content {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
    white-space: pre-wrap;
    font-family: 'Consolas', 'Monaco', monospace;
    background: rgba(10, 14, 26, 0.3);
    padding: 15px;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
}

/* 思维链元数据 */
.thinking-metadata {
    background: rgba(42, 47, 74, 0.4);
    border-radius: 12px;
    padding: 20px;
    margin-top: 25px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.metadata-title {
    font-family: var(--font-mono);
    font-size: 1.1rem;
    color: var(--accent-gold);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.metadata-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.metadata-item {
    background: rgba(26, 31, 58, 0.5);
    padding: 12px 15px;
    border-radius: 8px;
    border-left: 3px solid var(--accent-gold);
}

.metadata-label {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-bottom: 5px;
}

.metadata-value {
    font-size: 0.95rem;
    color: var(--text-primary);
    font-weight: 500;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes highlight {
    0% {
        background: rgba(0, 212, 255, 0.3);
        transform: scale(1.02);
    }
    50% {
        background: rgba(0, 212, 255, 0.2);
        transform: scale(1.01);
    }
    100% {
        background: transparent;
        transform: scale(1);
    }
}

/* 响应式设计 - AI思维链 */
@media (max-width: 768px) {
    .thinking-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .thinking-controls {
        justify-content: center;
    }

    .control-btn {
        flex: 1;
        justify-content: center;
        min-width: 0;
    }

    .search-container {
        flex-direction: column;
    }

    .search-btn {
        align-self: center;
    }

    .metadata-content {
        grid-template-columns: 1fr;
    }

    .thinking-content {
        max-height: 60vh;
    }

    .combined-thinking-content {
        max-height: 250px;
    }

    .thinking-text {
        max-height: 300px;
    }

    .thinking-text {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .ai-thinking-chain {
        padding: 20px 15px;
    }

    .thinking-header h3 {
        font-size: 1.5rem;
    }

    .control-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .phase-title {
        font-size: 1rem;
    }

    .thinking-text,
    .combined-thinking-content {
        font-size: 0.85rem;
        padding: 12px;
    }
}

/* Markdown样式 */
.md-h2 {
    color: var(--accent-blue);
    font-size: 1.4rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--accent-blue);
}

.md-h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 1.2rem 0 0.8rem 0;
}

.md-h4 {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
    margin: 1rem 0 0.6rem 0;
}

.md-bold {
    font-weight: 600;
    color: var(--text-primary);
}

.md-italic {
    font-style: italic;
    color: var(--text-secondary);
}

.md-code-block {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    overflow-x: auto;
}

.md-code-block code {
    background: none;
    padding: 0;
    border: none;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    color: var(--text-primary);
}

.md-inline-code {
    background: rgba(139, 92, 246, 0.15);
    color: var(--accent-purple);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
}

.md-list {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.md-list-item {
    margin: 0.5rem 0;
    line-height: 1.6;
    color: var(--text-primary);
}

.md-paragraph {
    margin: 1rem 0;
    line-height: 1.6;
    color: var(--text-primary);
}

.md-divider {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    margin: 2rem 0;
}

/* 审核结果列样式 */
.reason-cell {
    position: relative;
    max-width: 300px;
    min-width: 200px;
}

.reason-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.reason-text {
    flex: 1;
    line-height: 1.4;
    word-break: break-word;
}

.expand-btn {
    background: var(--accent-blue);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.expand-btn:hover {
    background: var(--accent-purple);
    transform: scale(1.05);
}

.reason-full {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.5;
    font-size: 0.9rem;
    color: var(--text-primary);
    margin-top: 4px;
}

.reason-cell.expanded .reason-full {
    display: block;
}

.reason-cell.expanded .expand-btn {
    background: var(--accent-red);
}

.reason-cell.expanded .expand-btn::after {
    content: ' 收起';
}

.reason-cell:not(.expanded) .expand-btn::after {
    content: '';
}

/* 表格列宽调整 */
.results-table th:nth-child(1),
.results-table td:nth-child(1) {
    width: 120px;
}

.results-table th:nth-child(2),
.results-table td:nth-child(2) {
    width: 150px;
}

.results-table th:nth-child(3),
.results-table td:nth-child(3) {
    width: auto;
    min-width: 200px;
}

.results-table th:nth-child(4),
.results-table td:nth-child(4) {
    width: 100px;
}

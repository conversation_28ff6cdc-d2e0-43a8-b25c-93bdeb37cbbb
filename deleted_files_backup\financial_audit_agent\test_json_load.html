<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 JSON文件加载测试</h1>
        
        <div class="test-section info">
            <h2>当前页面信息</h2>
            <p><strong>URL:</strong> <span id="current-url"></span></p>
            <p><strong>单据编号:</strong> <span id="doc-number"></span></p>
            <p><strong>目标JSON文件:</strong> <span id="target-json"></span></p>
        </div>
        
        <div class="test-section">
            <h2>测试操作</h2>
            <button onclick="testJSONLoad()">测试JSON加载</button>
            <button onclick="testAllPaths()">测试所有路径</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        // 显示页面信息
        function updatePageInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') || urlParams.get('number') || 'ZDBXD2025042900003';
            document.getElementById('doc-number').textContent = documentNumber;
            document.getElementById('target-json').textContent = `audit_report_${documentNumber}.json`;
            
            return documentNumber;
        }
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<h3>${title}</h3><div>${content}</div>`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testJSONLoad() {
            const documentNumber = updatePageInfo();
            addResult('🔄 开始测试', `正在测试加载 audit_report_${documentNumber}.json`);
            
            const paths = [
                `../audit_reports/audit_report_${documentNumber}.json`,
                `/audit_reports/audit_report_${documentNumber}.json`,
                `audit_reports/audit_report_${documentNumber}.json`
            ];
            
            for (const path of paths) {
                try {
                    addResult(`🔄 测试路径`, `正在尝试: ${path}`);
                    
                    const response = await fetch(path);
                    if (response.ok) {
                        const data = await response.json();
                        addResult(`✅ 成功加载`, `
                            <strong>路径:</strong> ${path}<br>
                            <strong>状态:</strong> ${response.status}<br>
                            <strong>数据预览:</strong><br>
                            <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                        `, 'success');
                        return;
                    } else {
                        addResult(`❌ 加载失败`, `路径: ${path}<br>状态: ${response.status} ${response.statusText}`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ 请求错误`, `路径: ${path}<br>错误: ${error.message}`, 'error');
                }
            }
            
            addResult(`❌ 所有路径都失败`, '无法加载JSON文件', 'error');
        }
        
        async function testAllPaths() {
            const documentNumber = updatePageInfo();
            
            const allPaths = [
                `../audit_reports/audit_report_${documentNumber}.json`,
                `/audit_reports/audit_report_${documentNumber}.json`,
                `audit_reports/audit_report_${documentNumber}.json`,
                `../audit_reports/audit_report_v2.json`,
                `/audit_reports/audit_report_v2.json`,
                `audit_reports/audit_report_v2.json`
            ];
            
            addResult('🔄 测试所有可能路径', `共测试 ${allPaths.length} 个路径`);
            
            for (const path of allPaths) {
                try {
                    const response = await fetch(path);
                    const status = response.ok ? '✅' : '❌';
                    const statusText = response.ok ? 'OK' : `${response.status} ${response.statusText}`;
                    const type = response.ok ? 'success' : 'error';
                    
                    addResult(`${status} ${path}`, `状态: ${statusText}`, type);
                } catch (error) {
                    addResult(`❌ ${path}`, `错误: ${error.message}`, 'error');
                }
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时更新信息
        window.onload = updatePageInfo;
    </script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恢复原始状态脚本
将所有配置和状态恢复到修改前的原始状态
"""

import json
import time
from pathlib import Path

def reset_audit_state():
    """重置审核状态到初始状态"""
    print("🔄 重置审核状态到初始状态...")
    
    # 创建初始状态
    state_data = {
        "audit_id": "ZDBXD2025042900003",
        "audit_status": "ready",
        "current_phase": "ready",
        "progress_percent": 0,
        "start_time": None,
        "completion_time": None,
        "summary": {
            "total_rules": 0,
            "completed_rules": 0,
            "passed_rules": 0,
            "failed_rules": 0,
            "warning_rules": 0
        },
        "ai_thinking": "系统就绪，等待开始审核",
        "last_updated": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        "message": "系统就绪，等待开始审核",
        "detail": "V2审核引擎准备就绪"
    }
    
    # 写入状态文件
    state_file = Path("backend/audit_state.json")
    with open(state_file, 'w', encoding='utf-8') as f:
        json.dump(state_data, f, ensure_ascii=False, indent=2)
    
    print("✅ 审核状态已重置")
    return True

def verify_restoration():
    """验证所有配置是否已恢复到原始状态"""
    print("\n🔍 验证配置恢复状态...")
    
    issues = []
    
    # 检查前端配置
    js_file = Path("frontend/ai_console_enhanced.js")
    if js_file.exists():
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查原始配置标志
        if "consistency: { start: 6, end: 24 }" in content:
            print("✅ 前端规则范围映射已恢复")
        else:
            issues.append("前端规则范围映射")
            
        if "'field-consistency': { index: 1" in content:
            print("✅ 前端阶段映射已恢复")
        else:
            issues.append("前端阶段映射")
    else:
        issues.append("前端文件不存在")
    
    # 检查后端配置
    fix_file = Path("backend/fix_audit_status.py")
    if fix_file.exists():
        with open(fix_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查原始进度配置
        if "'progress': 60" in content and "字段内容与一致性检查" in content:
            print("✅ 后端进度映射已恢复")
        else:
            issues.append("后端进度映射")
            
        if "('field-consistency', 60, '字段内容与一致性检查')" in content:
            print("✅ 后端阶段顺序已恢复")
        else:
            issues.append("后端阶段顺序")
    else:
        issues.append("后端配置文件不存在")
    
    # 检查规则文件
    rules_file = Path("业务招待费审核规则_V2.txt")
    if rules_file.exists():
        with open(rules_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查原始规则文件标志
        if "第二部分：字段内容与一致性检查" in content and "规则6：检查商务招待发起主体" in content:
            print("✅ 规则文件已恢复到原始版本")
        else:
            issues.append("规则文件版本")
    else:
        issues.append("规则文件不存在")
    
    # 检查orchestrator配置
    orch_file = Path("backend/auditor_v2/orchestrator_v2.py")
    if orch_file.exists():
        with open(orch_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "'field-consistency': 60" in content:
            print("✅ orchestrator进度映射已恢复")
        else:
            issues.append("orchestrator进度映射")
    else:
        issues.append("orchestrator文件不存在")
    
    return issues

def clean_temporary_files():
    """清理临时文件"""
    print("\n🧹 清理临时文件...")
    
    temp_files = [
        "test_phase_adjustment.py",
        "test_configuration_changes.py", 
        "reorganize_rules.py",
        "reset_audit_state.py",
        "阶段调整实施计划.md"
    ]
    
    for file_name in temp_files:
        file_path = Path(file_name)
        if file_path.exists():
            file_path.unlink()
            print(f"  🗑️ 删除: {file_name}")
    
    print("✅ 临时文件清理完成")

def main():
    print("="*60)
    print("🔄 恢复原始状态 - 撤销所有阶段调整修改")
    print("="*60)
    
    # 重置审核状态
    reset_audit_state()
    
    # 验证恢复状态
    issues = verify_restoration()
    
    # 清理临时文件
    clean_temporary_files()
    
    print("\n📋 恢复结果:")
    if not issues:
        print("🎉 所有配置已成功恢复到原始状态！")
        
        print("\n✅ 恢复的配置:")
        print("  - 前端规则范围映射：恢复到原始编号")
        print("  - 前端阶段映射：恢复到原始顺序")
        print("  - 后端进度映射：恢复到原始进度")
        print("  - 后端阶段顺序：恢复到原始顺序")
        print("  - 规则文件：恢复到V2.0原始版本")
        print("  - orchestrator配置：恢复到原始配置")
        print("  - 审核状态：重置到初始状态")
        
        print("\n🚀 系统已恢复正常！")
        print("\n📝 下一步操作:")
        print("  1. 重启后端服务: python start_backend_v2.py")
        print("  2. 强制刷新前端页面 (Ctrl+F5)")
        print("  3. 重新开始审核流程")
        print("  4. 确认AI思考过程正常显示")
        print("  5. 验证审核流程按原始阶段顺序执行")
        
        print("\n📊 原始阶段顺序:")
        print("  1. 附件完整性检查 (25%)")
        print("  2. 字段内容与一致性检查 (63%)")
        print("  3. 金额与标准检查 (79%)")
        print("  4. 八项规定合规性检查 (100%)")
        
    else:
        print("⚠️ 发现以下问题，需要手动检查:")
        for issue in issues:
            print(f"  ❌ {issue}")
        print("\n请检查相关文件是否正确恢复")
    
    print("\n💡 提示:")
    print("  - 所有阶段调整相关的修改已撤销")
    print("  - 系统恢复到修改前的稳定状态")
    print("  - 如需重新调整，请确保充分测试")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM连接测试脚本
测试大模型API是否能正常调用
"""

import json
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_caller import LLMCaller

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def test_simple_call(llm_caller):
    """测试简单调用"""
    print("\n🧪 测试1: 简单问答")
    print("-" * 30)
    
    try:
        prompt = "请回答：1+1等于几？只需要回答数字。"
        response = llm_caller.query_semantic_rule(prompt)
        print(f"✅ 调用成功")
        print(f"📝 提问: {prompt}")
        print(f"🤖 回答: {response}")
        return True
    except Exception as e:
        print(f"❌ 调用失败: {e}")
        return False

def test_business_rule(llm_caller):
    """测试业务规则相关调用"""
    print("\n🧪 测试2: 业务规则判断")
    print("-" * 30)
    
    try:
        prompt = """
        请判断以下情况是否合规：
        - 申报事由: "客户商务洽谈"
        - 实际消费: "餐饮服务"
        这两者是否匹配？请回答"是"或"否"，并简要说明理由。
        """
        response = llm_caller.query_semantic_rule(prompt)
        print(f"✅ 调用成功")
        print(f"📝 提问: {prompt.strip()}")
        print(f"🤖 回答: {response}")
        return True
    except Exception as e:
        print(f"❌ 调用失败: {e}")
        return False

def test_api_parameters(llm_caller):
    """测试API参数配置"""
    print("\n🧪 测试3: API参数检查")
    print("-" * 30)
    
    print(f"🔧 API模式: {llm_caller.api_mode}")
    print(f"🌐 Base URL: {llm_caller.base_url}")
    print(f"🤖 模型名称: {llm_caller.model}")
    print(f"🌡️ Temperature: {llm_caller.temperature}")
    print(f"📏 Max Tokens: {llm_caller.max_tokens}")
    print(f"🔑 API密钥: {llm_caller.api_key[:8]}...{llm_caller.api_key[-4:]}")
    print(f"📚 使用OpenAI: {llm_caller.use_openai}")
    
    return True

def main():
    """主函数"""
    print("🔍 LLM连接测试工具")
    print("=" * 50)
    
    # 1. 加载配置
    print("📋 加载配置文件...")
    config = load_config()
    if not config:
        return 1
    
    # 检查API密钥
    api_key = config.get('LLM_API_KEY')
    if not api_key or api_key == "在此处填入你的API密钥":
        print("❌ API密钥未设置，请检查config.json")
        return 1
    
    print("✅ 配置文件加载成功")
    
    # 2. 初始化LLM调用器
    print("\n🤖 初始化LLM调用器...")
    try:
        llm_caller = LLMCaller(
            api_key=config['LLM_API_KEY'],
            model_name=config.get('LLM_MODEL_NAME', 'qwen-max'),
            config=config
        )
        print("✅ LLM调用器初始化成功")
    except Exception as e:
        print(f"❌ LLM调用器初始化失败: {e}")
        return 1
    
    # 3. 运行测试
    test_results = []
    
    # 测试API参数
    test_results.append(test_api_parameters(llm_caller))
    
    # 测试简单调用
    test_results.append(test_simple_call(llm_caller))
    
    # 测试业务规则
    test_results.append(test_business_rule(llm_caller))
    
    # 4. 显示结果
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！大模型连接正常")
        print("💡 如果审核仍然失败，问题可能在于:")
        print("   1. 数据文件路径不正确")
        print("   2. 规则文件格式问题")
        print("   3. 业务逻辑错误")
        return 0
    else:
        print("\n❌ 部分测试失败，大模型连接存在问题")
        print("💡 建议检查:")
        print("   1. 网络连接是否正常")
        print("   2. API密钥是否有效")
        print("   3. 模型名称是否正确")
        print("   4. Base URL是否可访问")
        return 1

if __name__ == "__main__":
    sys.exit(main())

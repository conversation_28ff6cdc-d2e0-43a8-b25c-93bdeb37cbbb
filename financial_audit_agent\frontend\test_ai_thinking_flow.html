<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI思维链数据流测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .content-area {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            min-height: 300px;
            white-space: pre-wrap;
            font-family: 'Segoe UI', sans-serif;
        }
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        .status-item {
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .incremental-content {
            border-left: 3px solid #28a745;
            padding-left: 10px;
            margin: 5px 0;
            background: #f8fff9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI思维链数据流测试工具</h1>
        <p>测试从后端到前端的AI思维链数据传输和显示</p>

        <div class="section">
            <h3>📊 状态信息</h3>
            <div class="status-info">
                <div class="status-item">
                    <strong>审核状态</strong><br>
                    <span id="audit-status">-</span>
                </div>
                <div class="status-item">
                    <strong>当前阶段</strong><br>
                    <span id="current-phase">-</span>
                </div>
                <div class="status-item">
                    <strong>进度</strong><br>
                    <span id="progress">-</span>
                </div>
                <div class="status-item">
                    <strong>AI内容长度</strong><br>
                    <span id="content-length">-</span>
                </div>
                <div class="status-item">
                    <strong>最后更新</strong><br>
                    <span id="last-updated">-</span>
                </div>
            </div>
            <button class="btn" onclick="fetchStatus()">🔄 手动获取状态</button>
            <button class="btn" onclick="toggleAutoRefresh()">⏯️ 切换自动刷新</button>
            <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
        </div>

        <div class="section">
            <h3>📝 API响应日志</h3>
            <div id="api-log" class="log-area"></div>
        </div>

        <div class="section">
            <h3>🧠 AI思维链内容</h3>
            <div id="ai-thinking-content" class="content-area">等待AI思维链内容...</div>
        </div>

        <div class="section">
            <h3>🔄 增量更新测试</h3>
            <div id="incremental-content" class="content-area">增量内容将在这里显示...</div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let lastThinkingContent = '';
        let isAutoRefreshEnabled = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 AI思维链测试工具初始化');
            fetchStatus();
            startAutoRefresh();
        });

        // 获取状态
        async function fetchStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                logMessage(`✅ API响应成功: ${JSON.stringify(data, null, 2)}`);
                
                // 更新状态显示
                updateStatusDisplay(data);
                
                // 更新AI思维链内容
                updateAIThinkingContent(data.ai_thinking || '');
                
            } catch (error) {
                logMessage(`❌ API请求失败: ${error.message}`);
            }
        }

        // 更新状态显示
        function updateStatusDisplay(data) {
            document.getElementById('audit-status').textContent = data.audit_status || '-';
            document.getElementById('current-phase').textContent = data.current_phase || '-';
            document.getElementById('progress').textContent = `${data.progress_percent || 0}%`;
            document.getElementById('content-length').textContent = (data.ai_thinking || '').length;
            document.getElementById('last-updated').textContent = data.last_updated || '-';
        }

        // 更新AI思维链内容
        function updateAIThinkingContent(newContent) {
            const contentArea = document.getElementById('ai-thinking-content');
            const incrementalArea = document.getElementById('incremental-content');
            
            // 显示完整内容
            contentArea.textContent = newContent;
            
            // 测试增量更新
            if (lastThinkingContent && newContent.length > lastThinkingContent.length && 
                newContent.startsWith(lastThinkingContent)) {
                
                const incrementalContent = newContent.substring(lastThinkingContent.length);
                
                if (incrementalContent.trim()) {
                    logMessage(`🔄 检测到增量内容: ${incrementalContent.length} 字符`);
                    
                    // 创建增量内容元素
                    const incrementalDiv = document.createElement('div');
                    incrementalDiv.className = 'incremental-content';
                    incrementalDiv.textContent = incrementalContent;
                    
                    // 添加时间戳
                    const timestamp = new Date().toLocaleTimeString();
                    incrementalDiv.setAttribute('title', `添加时间: ${timestamp}`);
                    
                    // 追加到增量区域
                    incrementalArea.appendChild(incrementalDiv);
                    
                    // 滚动到最新内容
                    incrementalDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
                }
            } else if (newContent !== lastThinkingContent) {
                logMessage(`🔄 内容完全更新: ${newContent.length} 字符`);
                incrementalArea.innerHTML = '<p>内容完全更新，重置增量显示</p>';
            }
            
            lastThinkingContent = newContent;
        }

        // 记录日志
        function logMessage(message) {
            const logArea = document.getElementById('api-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(message);
        }

        // 开始自动刷新
        function startAutoRefresh() {
            if (autoRefreshInterval) return;
            
            autoRefreshInterval = setInterval(fetchStatus, 2000); // 每2秒刷新
            isAutoRefreshEnabled = true;
            logMessage('🔄 自动刷新已启动 (2秒间隔)');
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                isAutoRefreshEnabled = false;
                logMessage('⏸️ 自动刷新已停止');
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefreshEnabled) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('api-log').textContent = '';
            document.getElementById('incremental-content').innerHTML = '增量内容将在这里显示...';
            logMessage('🗑️ 日志已清空');
        }
    </script>
</body>
</html>

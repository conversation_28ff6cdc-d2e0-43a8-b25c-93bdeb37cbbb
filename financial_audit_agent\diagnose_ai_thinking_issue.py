#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI思维链显示问题诊断脚本
检查可能导致AI思维链按钮不显示的问题
"""

import json
import os
from pathlib import Path

def check_audit_report_files():
    """检查审核报告文件"""
    print("🔍 检查审核报告文件")
    print("-" * 50)
    
    audit_reports_dir = Path("audit_reports")
    if not audit_reports_dir.exists():
        print("❌ audit_reports 目录不存在")
        return False
    
    report_files = list(audit_reports_dir.glob("*.json"))
    if not report_files:
        print("❌ 未找到任何JSON报告文件")
        return False
    
    print(f"✅ 找到 {len(report_files)} 个报告文件:")
    
    for file_path in report_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            has_ai_thinking = 'ai_thinking_chain' in data
            ai_thinking_status = "✅ 包含" if has_ai_thinking else "❌ 缺失"
            
            print(f"  📄 {file_path.name}: {ai_thinking_status} AI思维链")
            
            if has_ai_thinking:
                ai_thinking = data['ai_thinking_chain']
                combined_len = len(ai_thinking.get('combined_thinking', ''))
                phases_count = len(ai_thinking.get('phases_history', {}))
                metadata_exists = 'extraction_metadata' in ai_thinking
                
                print(f"    - 组合思维链: {combined_len} 字符")
                print(f"    - 阶段历史: {phases_count} 个")
                print(f"    - 元数据: {'✅' if metadata_exists else '❌'}")
                
        except Exception as e:
            print(f"  ❌ {file_path.name}: 读取失败 - {e}")
    
    return True

def check_frontend_files():
    """检查前端文件"""
    print("\n🌐 检查前端文件")
    print("-" * 50)
    
    frontend_files = {
        'frontend/ai_results.html': 'HTML页面',
        'frontend/ai_results.css': 'CSS样式',
        'frontend/ai_results.js': 'JavaScript功能'
    }
    
    all_exist = True
    
    for file_path, description in frontend_files.items():
        file_obj = Path(file_path)
        if file_obj.exists():
            size = file_obj.stat().st_size
            print(f"  ✅ {description}: {file_path} ({size:,} bytes)")
        else:
            print(f"  ❌ {description}: {file_path} (不存在)")
            all_exist = False
    
    return all_exist

def check_javascript_functions():
    """检查JavaScript关键函数"""
    print("\n⚡ 检查JavaScript关键函数")
    print("-" * 50)
    
    js_file = Path("frontend/ai_results.js")
    if not js_file.exists():
        print("❌ JavaScript文件不存在")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    required_functions = [
        'checkAndShowThinkingChainButton',
        'showThinkingChain',
        'loadThinkingChainContent',
        'loadDefaultAuditReport'
    ]
    
    missing_functions = []
    for func in required_functions:
        if func in js_content:
            print(f"  ✅ {func}")
        else:
            print(f"  ❌ {func}")
            missing_functions.append(func)
    
    if missing_functions:
        print(f"\n❌ 缺失关键函数: {missing_functions}")
        return False
    
    return True

def check_css_classes():
    """检查CSS关键类"""
    print("\n🎨 检查CSS关键类")
    print("-" * 50)
    
    css_file = Path("frontend/ai_results.css")
    if not css_file.exists():
        print("❌ CSS文件不存在")
        return False
    
    with open(css_file, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    required_classes = [
        '.thinking-chain-btn',
        '.ai-thinking-chain',
        '.thinking-header',
        '.thinking-content'
    ]
    
    missing_classes = []
    for css_class in required_classes:
        if css_class in css_content:
            print(f"  ✅ {css_class}")
        else:
            print(f"  ❌ {css_class}")
            missing_classes.append(css_class)
    
    if missing_classes:
        print(f"\n❌ 缺失关键CSS类: {missing_classes}")
        return False
    
    return True

def check_html_elements():
    """检查HTML关键元素"""
    print("\n📄 检查HTML关键元素")
    print("-" * 50)
    
    html_file = Path("frontend/ai_results.html")
    if not html_file.exists():
        print("❌ HTML文件不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    required_elements = [
        'id="thinking-chain-btn"',
        'id="ai-thinking-chain"',
        'id="thinking-content"',
        'class="thinking-chain-btn"'
    ]
    
    missing_elements = []
    for element in required_elements:
        if element in html_content:
            print(f"  ✅ {element}")
        else:
            print(f"  ❌ {element}")
            missing_elements.append(element)
    
    if missing_elements:
        print(f"\n❌ 缺失关键HTML元素: {missing_elements}")
        return False
    
    return True

def generate_test_url():
    """生成测试URL"""
    print("\n🔗 生成测试URL")
    print("-" * 50)
    
    base_url = "http://localhost:8081"
    
    test_urls = [
        f"{base_url}/frontend/ai_results.html?doc=123",
        f"{base_url}/frontend/ai_results.html",
        f"{base_url}/test_ai_thinking_chain_ui.html",
        f"{base_url}/test_ai_results_page.html"
    ]
    
    print("📋 建议测试的URL:")
    for i, url in enumerate(test_urls, 1):
        print(f"  {i}. {url}")
    
    return test_urls

def provide_troubleshooting_tips():
    """提供故障排除建议"""
    print("\n🔧 故障排除建议")
    print("-" * 50)
    
    tips = [
        "1. 确保使用HTTP服务器访问页面，不要直接打开HTML文件",
        "2. 打开浏览器开发者工具(F12)查看控制台错误信息",
        "3. 检查网络标签页，确认JSON文件正确加载",
        "4. 验证审核报告JSON文件包含ai_thinking_chain字段",
        "5. 确认JavaScript中的checkAndShowThinkingChainButton方法被调用",
        "6. 检查CSS样式是否正确加载，按钮可能被隐藏",
        "7. 尝试硬刷新页面(Ctrl+F5)清除缓存"
    ]
    
    for tip in tips:
        print(f"  {tip}")

def main():
    """主诊断函数"""
    print("🩺 AI思维链显示问题诊断")
    print("=" * 60)
    
    # 切换到正确的工作目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 执行各项检查
    checks = [
        ("审核报告文件", check_audit_report_files),
        ("前端文件", check_frontend_files),
        ("JavaScript函数", check_javascript_functions),
        ("CSS类", check_css_classes),
        ("HTML元素", check_html_elements)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
    
    # 生成测试URL
    test_urls = generate_test_url()
    
    # 提供故障排除建议
    provide_troubleshooting_tips()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断结果总结")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"  {check_name}: {status}")
    
    print(f"\n🎯 总体状态: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！问题可能在浏览器端或网络请求。")
        print("💡 建议: 打开浏览器开发者工具查看具体错误信息。")
    else:
        print("⚠️ 发现问题，请根据上述检查结果进行修复。")
    
    print(f"\n🌐 推荐测试URL: {test_urls[0]}")

if __name__ == "__main__":
    main()

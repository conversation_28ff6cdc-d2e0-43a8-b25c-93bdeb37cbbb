#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端API连接
"""

import requests
import json
import time

def test_api_endpoints():
    """测试API端点"""
    print("🧪 测试前端API连接")
    print("=" * 50)
    
    base_url = "http://localhost:8001"
    doc_num = "ZDBXD2025051300001"
    
    endpoints = [
        ("/api/status", "状态检查"),
        (f"/api/report?doc_num={doc_num}", "审核报告"),
        ("/api/progress", "审核进度"),
        ("/api/rules", "规则信息")
    ]
    
    for endpoint, description in endpoints:
        print(f"\n🔍 测试 {description}: {endpoint}")
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 响应成功")
                
                # 显示关键信息
                if endpoint.startswith("/api/report"):
                    if 'summary' in data:
                        summary = data['summary']
                        print(f"   📊 总规则: {summary.get('total_rules_checked', 0)}")
                        print(f"   ✅ 通过: {summary.get('passed_count', 0)}")
                        print(f"   ❌ 失败: {summary.get('failed_count', 0)}")
                        print(f"   ⚠️ 警告: {summary.get('warning_count', 0)}")
                    if 'metadata' in data:
                        metadata = data['metadata']
                        print(f"   📄 数据源: {metadata.get('source', 'unknown')}")
                        print(f"   🕒 时间戳: {metadata.get('timestamp', 'unknown')}")
                
                elif endpoint == "/api/progress":
                    print(f"   📈 总体进度: {data.get('overall_progress', 0)}%")
                    print(f"   🔄 当前阶段: {data.get('current_phase', 'unknown')}")
                
                elif endpoint == "/api/status":
                    print(f"   🔧 状态: {data.get('status', 'unknown')}")
                    print(f"   💬 消息: {data.get('message', 'no message')}")
                
                elif endpoint == "/api/rules":
                    print(f"   📋 总规则数: {data.get('total_rules', 0)}")
                
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")

def test_web_server():
    """测试Web服务器"""
    print(f"\n🌐 测试Web服务器")
    print("-" * 30)
    
    web_url = "http://localhost:8002"
    pages = [
        "/frontend/ai_console.html",
        "/frontend/debug_data_sync.html"
    ]
    
    for page in pages:
        try:
            response = requests.get(f"{web_url}{page}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {page} - 可访问")
            else:
                print(f"❌ {page} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {page} - 连接失败: {e}")

def main():
    """主函数"""
    print("🚀 前端API连接测试")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试Web服务器
    test_web_server()
    
    print(f"\n📊 测试完成")
    print("💡 如果API测试通过，前端应该能够正常获取数据")
    print("🌐 请在浏览器中检查控制台日志:")
    print("   http://localhost:8002/frontend/ai_console.html?doc_num=ZDBXD2025051300001")
    print("   http://localhost:8002/frontend/debug_data_sync.html?doc_num=ZDBXD2025051300001")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合诊断前后端数据流问题
分析数据格式、API连接、前端解析等各个环节
"""

import json
import time
import requests
from pathlib import Path


def analyze_data_sources():
    """分析数据源文件"""
    print("=" * 60)
    print("1. 数据源分析")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    
    # 检查审核报告文件
    report_file = project_root / "audit_reports" / "audit_report_ZDBXD2025042900003.json"
    print(f"审核报告文件: {report_file}")
    print(f"存在: {report_file.exists()}")
    
    if report_file.exists():
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
            
            print("✅ 审核报告数据结构:")
            print(f"   - summary: {report_data.get('summary', {})}")
            print(f"   - details数量: {len(report_data.get('details', []))}")
            print(f"   - metadata: {report_data.get('metadata', 'N/A')}")
            
            # 检查前端期望的数据格式
            summary = report_data.get('summary', {})
            expected_fields = ['total_rules_checked', 'passed_count', 'failed_count', 'warning_count']
            missing_fields = [field for field in expected_fields if field not in summary]
            
            if missing_fields:
                print(f"⚠️ 缺少字段: {missing_fields}")
            else:
                print("✅ 数据格式完整")
                
        except Exception as e:
            print(f"❌ 读取报告文件失败: {e}")
    
    # 检查状态文件
    status_file = project_root / "backend" / "audit_status.json"
    print(f"\n状态文件: {status_file}")
    print(f"存在: {status_file.exists()}")
    
    if status_file.exists():
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            print("✅ 状态文件数据结构:")
            print(f"   - current_step: {status_data.get('current_step')}")
            print(f"   - status: {status_data.get('status')}")
            print(f"   - message: {status_data.get('message')}")
            print(f"   - progress: {status_data.get('progress')}")
            print(f"   - final_stats: {status_data.get('final_stats', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 读取状态文件失败: {e}")


def test_api_connectivity():
    """测试API连接性"""
    print("\n" + "=" * 60)
    print("2. API连接性测试")
    print("=" * 60)
    
    ports = [8001, 8002, 8003, 8004, 8005]
    working_api = None
    
    for port in ports:
        try:
            response = requests.get(f'http://localhost:{port}/api/status', timeout=3)
            if response.status_code == 200:
                print(f"✅ 端口 {port} API正常")
                data = response.json()
                print(f"   - current_step: {data.get('current_step')}")
                print(f"   - status: {data.get('status')}")
                print(f"   - doc_num: {data.get('doc_num')}")
                working_api = f'http://localhost:{port}'
                break
            else:
                print(f"❌ 端口 {port} 返回状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 端口 {port} 连接失败: {e}")
    
    if not working_api:
        print("⚠️ 未找到可用的API服务器")
        return None
    
    # 测试所有API端点
    endpoints = ['/api/status', '/api/report?doc_num=ZDBXD2025042900003', '/api/rules', '/api/progress']
    
    for endpoint in endpoints:
        try:
            response = requests.get(f'{working_api}{endpoint}', timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {endpoint} - 正常")
                
                # 显示关键数据
                if endpoint == '/api/status':
                    print(f"   状态: {data.get('status')}")
                elif endpoint.startswith('/api/report'):
                    summary = data.get('summary', {})
                    print(f"   总规则: {summary.get('total_rules_checked')}")
                    print(f"   通过: {summary.get('passed_count')}")
                    print(f"   失败: {summary.get('failed_count')}")
                    print(f"   警告: {summary.get('warning_count')}")
            else:
                print(f"❌ {endpoint} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")
    
    return working_api


def test_frontend_data_parsing():
    """测试前端数据解析"""
    print("\n" + "=" * 60)
    print("3. 前端数据解析测试")
    print("=" * 60)
    
    # 模拟前端期望的数据格式
    project_root = Path(__file__).parent
    report_file = project_root / "audit_reports" / "audit_report_ZDBXD2025042900003.json"
    
    if not report_file.exists():
        print("❌ 审核报告文件不存在")
        return
    
    try:
        with open(report_file, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        # 检查前端JavaScript期望的数据结构
        print("检查前端期望的数据结构:")
        
        # 1. summary数据
        summary = report_data.get('summary', {})
        print(f"✅ summary数据: {summary}")
        
        # 2. 计算前端需要的统计数据
        total = summary.get('total_rules_checked', 0)
        passed = summary.get('passed_count', 0)
        failed = summary.get('failed_count', 0)
        warning = summary.get('warning_count', 0)
        
        if total > 0:
            pass_rate = round((passed / total) * 100)
            risk_rate = round(((failed + warning) / total) * 100)
            print(f"✅ 计算结果:")
            print(f"   通过率: {pass_rate}%")
            print(f"   风险率: {risk_rate}%")
        else:
            print("⚠️ 总规则数为0，无法计算比率")
        
        # 3. 检查details数据
        details = report_data.get('details', [])
        print(f"✅ details数据: {len(details)} 条规则")
        
        if details:
            # 统计各种状态
            status_count = {}
            for detail in details:
                status = detail.get('status', 'UNKNOWN')
                status_count[status] = status_count.get(status, 0) + 1
            
            print(f"   状态分布: {status_count}")
            
            # 验证数据一致性
            calculated_passed = status_count.get('PASS', 0)
            calculated_failed = status_count.get('FAIL', 0)
            calculated_warning = status_count.get('WARNING', 0)
            
            print(f"✅ 数据一致性检查:")
            print(f"   summary中通过数: {passed}, details中计算: {calculated_passed}")
            print(f"   summary中失败数: {failed}, details中计算: {calculated_failed}")
            print(f"   summary中警告数: {warning}, details中计算: {calculated_warning}")
            
            if (passed == calculated_passed and 
                failed == calculated_failed and 
                warning == calculated_warning):
                print("✅ 数据一致性验证通过")
            else:
                print("⚠️ 数据一致性验证失败")
        
    except Exception as e:
        print(f"❌ 数据解析失败: {e}")


def test_realtime_data_sync():
    """测试实时数据同步"""
    print("\n" + "=" * 60)
    print("4. 实时数据同步测试")
    print("=" * 60)
    
    # 检查是否有API服务器
    api_url = None
    for port in [8001, 8002, 8003]:
        try:
            response = requests.get(f'http://localhost:{port}/api/status', timeout=2)
            if response.status_code == 200:
                api_url = f'http://localhost:{port}'
                break
        except:
            continue
    
    if not api_url:
        print("❌ 没有可用的API服务器，无法测试实时同步")
        return
    
    print(f"✅ 使用API服务器: {api_url}")
    
    # 模拟前端轮询行为
    print("模拟前端轮询行为（5次，每次间隔3秒）:")
    
    for i in range(5):
        try:
            response = requests.get(f'{api_url}/api/status', timeout=3)
            if response.status_code == 200:
                data = response.json()
                print(f"[轮询 {i+1}] 成功获取数据:")
                print(f"   步骤: {data.get('current_step')}")
                print(f"   消息: {data.get('message')}")
                print(f"   进度: {data.get('progress', 'N/A')}%")
                print(f"   时间戳: {data.get('timestamp')}")
                
                # 检查是否有final_stats
                if 'final_stats' in data:
                    stats = data['final_stats']
                    print(f"   统计数据: 总计{stats.get('total', 0)}, 通过{stats.get('passed', 0)}")
            else:
                print(f"[轮询 {i+1}] API请求失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"[轮询 {i+1}] 连接失败: {e}")
        
        if i < 4:  # 最后一次不需要等待
            time.sleep(3)


def generate_frontend_debug_script():
    """生成前端调试脚本"""
    print("\n" + "=" * 60)
    print("5. 前端调试脚本生成")
    print("=" * 60)
    
    debug_script = '''
// 在浏览器控制台中运行此脚本来调试前端数据流
console.log("🔧 前端数据流调试工具");

// 1. 检查aiConsole对象
if (window.aiConsole) {
    console.log("✅ aiConsole对象存在");
    console.log("   apiBaseUrl:", window.aiConsole.apiBaseUrl);
    console.log("   realAuditData:", window.aiConsole.realAuditData);
} else {
    console.log("❌ aiConsole对象不存在");
}

// 2. 手动测试API连接
async function testAPIConnection() {
    const ports = [8001, 8002, 8003, 8004];
    for (const port of ports) {
        try {
            const response = await fetch(`http://localhost:${port}/api/status`);
            if (response.ok) {
                const data = await response.json();
                console.log(`✅ 端口 ${port} API正常:`, data);
                return `http://localhost:${port}`;
            }
        } catch (e) {
            console.log(`❌ 端口 ${port} 连接失败:`, e.message);
        }
    }
    return null;
}

// 3. 手动加载审核报告
async function loadAuditReport() {
    const apiUrl = await testAPIConnection();
    if (apiUrl) {
        try {
            const response = await fetch(`${apiUrl}/api/report?doc_num=ZDBXD2025042900003`);
            if (response.ok) {
                const data = await response.json();
                console.log("✅ 审核报告数据:", data);
                
                // 手动更新统计数据
                if (window.aiConsole && window.aiConsole.updateRealStats) {
                    window.aiConsole.updateRealStats(data.summary);
                    console.log("✅ 手动更新统计数据完成");
                }
                
                return data;
            }
        } catch (e) {
            console.log("❌ 加载审核报告失败:", e);
        }
    }
    return null;
}

// 4. 检查DOM元素
function checkDOMElements() {
    const elements = [
        'audit-statistics',
        'thinking-content', 
        'progress-bar',
        'status-display'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ 元素 #${id} 存在`);
        } else {
            console.log(`❌ 元素 #${id} 不存在`);
        }
    });
}

// 运行所有测试
console.log("开始前端调试...");
testAPIConnection();
loadAuditReport();
checkDOMElements();
'''
    
    print("前端调试脚本已生成，请在浏览器控制台中运行以下代码:")
    print("-" * 60)
    print(debug_script)
    print("-" * 60)


def main():
    """主函数"""
    print("前后端数据流综合诊断工具")
    print("专注于分析数据同步问题的根本原因")
    
    # 1. 分析数据源
    analyze_data_sources()
    
    # 2. 测试API连接
    test_api_connectivity()
    
    # 3. 测试前端数据解析
    test_frontend_data_parsing()
    
    # 4. 测试实时数据同步
    test_realtime_data_sync()
    
    # 5. 生成前端调试脚本
    generate_frontend_debug_script()
    
    print("\n" + "=" * 60)
    print("诊断完成！")
    print("=" * 60)
    print("请根据上述分析结果:")
    print("1. 确认数据文件格式是否正确")
    print("2. 验证API服务器是否正常运行")
    print("3. 在浏览器中运行前端调试脚本")
    print("4. 检查浏览器控制台的错误信息")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试改进的状态文件设计
"""

import json
import os
import sys
sys.path.append('backend/auditor_v2')

from orchestrator_v2 import AuditOrchestrator

def test_improved_state_design():
    """测试改进的状态文件设计"""
    print("🧪 测试改进的状态文件设计")
    print("=" * 60)
    
    # 创建测试编排器
    orchestrator = AuditOrchestrator("TEST_DOC", "test_rules.txt", {})
    
    # 模拟4个阶段的审核过程
    test_phases = [
        {
            "step_id": "phase1",
            "status": "completed",
            "message": "AI正在分析: 附件完整性检查",
            "ai_thinking": """## 🔍 附件完整性检查分析过程

**第一步:理解审核要求**
我需要检查业务招待费报销所需的基础材料是否齐全.根据规则要求,必须包括:发票, 审批表, 小票, 支付记录等关键附件.

**第二步:分析提供的数据**
从JSON数据中,我看到了以下附件信息:
- 发票文件:已提供,格式为PDF
- 审批表:已提供,包含完整的审批流程
- 小票:已提供,显示具体消费明细
- 支付记录:已提供,显示银行转账记录

**第三步:逐项验证**
1. 发票完整性:✅ 发票信息完整,包含必要的税务信息
2. 审批流程:✅ 审批表显示完整的审批链条
3. 消费凭证:✅ 小票显示详细的消费项目
4. 支付证明:✅ 银行记录与报销金额一致

**结论**: 所有必需的附件都已提供,且格式规范, 内容完整."""
        },
        {
            "step_id": "phase2", 
            "status": "completed",
            "message": "AI正在分析: 字段内容与一致性检查",
            "ai_thinking": """## 🔍 字段内容与一致性检查分析过程

**第一步:数据结构分析**
我正在分析表单数据和附件数据之间的一致性.需要重点关注金额, 日期, 人员信息等关键字段的匹配情况.

**第二步:金额一致性验证**
- 报销申请金额:1,250.00元
- 发票金额:1,250.00元
- 小票总额:1,250.00元
- 银行支付金额:1,250.00元
✅ 所有金额字段完全一致

**第三步:日期逻辑检查**
- 消费日期:2024-07-15
- 申请日期:2024-07-16
- 审批日期:2024-07-17
- 报销日期:2024-07-18
✅ 日期逻辑合理,符合业务流程

**第四步:人员信息核验**
- 申请人:张三
- 审批人:李四（部门经理）
- 财务审核:王五
✅ 人员信息在各文档中保持一致

**结论**: 数据一致性良好,各字段之间逻辑合理,无发现明显的矛盾或异常."""
        },
        {
            "step_id": "phase3",
            "status": "running", 
            "message": "AI正在分析: 金额与标准检查",
            "ai_thinking": """## 🔍 金额与标准检查分析过程

**第一步:预算标准分析**
根据公司政策,业务招待费的标准为:
- 部门级客户招待:每人每次不超过200元
- 重要客户招待:每人每次不超过300元
- 本次招待涉及5人,预算上限应为1,500元

**第二步:消费明细分析**
从小票明细可以看到:
- 主食费用:800元（4道菜品）
- 酒水费用:350元（2瓶红酒）
- 服务费:100元（10%服务费）
- 总计:1,250元

**第三步:单人消费计算**
总费用1,250元 ÷ 5人 = 250元/人
这个标准在重要客户招待的范围内（300元/人以下）,符合预算要求.

**正在进行详细的标准符合性分析...**"""
        }
    ]
    
    print("🔄 模拟4阶段审核过程...")
    
    # 清理测试状态文件
    test_state_file = "backend/test_audit_state.json"
    if os.path.exists(test_state_file):
        os.remove(test_state_file)
    
    # 设置测试状态文件路径
    orchestrator.state_file_path = test_state_file
    
    # 模拟各阶段更新
    for i, phase in enumerate(test_phases):
        print(f"\n📝 更新阶段 {i+1}: {phase['step_id']}")
        
        orchestrator._update_progress(
            step_id=phase["step_id"],
            status=phase["status"],
            message=phase["message"],
            ai_thinking=phase["ai_thinking"]
        )
        
        # 读取并显示当前状态
        with open(test_state_file, 'r', encoding='utf-8') as f:
            state = json.load(f)
        
        print(f"✅ 当前阶段: {state['current_phase']}")
        print(f"✅ 进度: {state['progress_percent']}%")
        print(f"✅ 历史阶段数: {len(state.get('phases_history', {}))}")
        print(f"✅ AI思维链长度: {len(state['ai_thinking'])} 字符")
    
    print("\n" + "=" * 60)
    print("📊 最终状态文件内容:")
    
    with open(test_state_file, 'r', encoding='utf-8') as f:
        final_state = json.load(f)
    
    print(f"📋 审核ID: {final_state['audit_id']}")
    print(f"📋 当前阶段: {final_state['current_phase']}")
    print(f"📋 进度: {final_state['progress_percent']}%")
    print(f"📋 历史阶段: {list(final_state.get('phases_history', {}).keys())}")
    print(f"📋 组合思维链长度: {len(final_state['ai_thinking'])} 字符")
    
    print("\n📝 组合思维链预览:")
    print("-" * 40)
    print(final_state['ai_thinking'][:500] + "...")
    print("-" * 40)
    
    # 验证改进效果
    print("\n✅ 改进效果验证:")
    phases_history = final_state.get('phases_history', {})
    
    if len(phases_history) >= 2:
        print("✅ 成功保存多个阶段的历史")
    else:
        print("❌ 阶段历史保存不完整")
    
    if "## 🔍 附件完整性检查" in final_state['ai_thinking']:
        print("✅ 组合思维链包含第一阶段内容")
    else:
        print("❌ 组合思维链缺少第一阶段内容")
    
    if "## 🔍 字段内容与一致性检查" in final_state['ai_thinking']:
        print("✅ 组合思维链包含第二阶段内容")
    else:
        print("❌ 组合思维链缺少第二阶段内容")
    
    if "## 🎯 审核总结" in final_state['ai_thinking']:
        print("✅ 组合思维链包含总结信息")
    else:
        print("❌ 组合思维链缺少总结信息")
    
    # 清理测试文件
    if os.path.exists(test_state_file):
        os.remove(test_state_file)
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    test_improved_state_design()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试控制台</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .debug-section {
            background: #2a2a2a;
            border: 1px solid #00ff00;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
        }
        .debug-title {
            color: #00ffff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .debug-output {
            background: #000;
            border: 1px solid #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #00ffff; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background: #00ff00;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 前端调试控制台</h1>
        
        <div class="debug-section">
            <div class="debug-title">1. 基础环境检测</div>
            <div id="env-output" class="debug-output">正在检测...</div>
            <button onclick="checkEnvironment()">重新检测环境</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">2. API连接测试</div>
            <div id="api-output" class="debug-output">等待测试...</div>
            <button onclick="testAPI()">测试API连接</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">3. 数据加载测试</div>
            <div id="data-output" class="debug-output">等待测试...</div>
            <button onclick="testDataLoading()">测试数据加载</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">4. 前端对象检测</div>
            <div id="frontend-output" class="debug-output">等待检测...</div>
            <button onclick="checkFrontendObjects()">检测前端对象</button>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">5. 手动数据更新</div>
            <div id="manual-output" class="debug-output">等待操作...</div>
            <button onclick="manualDataUpdate()">手动更新数据</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // 1. 环境检测
        function checkEnvironment() {
            clearLog('env-output');
            log('env-output', '开始环境检测...', 'info');
            
            // 检测基础信息
            log('env-output', `浏览器: ${navigator.userAgent}`, 'info');
            log('env-output', `当前URL: ${window.location.href}`, 'info');
            log('env-output', `协议: ${window.location.protocol}`, 'info');
            log('env-output', `主机: ${window.location.host}`, 'info');
            
            // 检测URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const docNum = urlParams.get('doc_num');
            if (docNum) {
                log('env-output', `文档编号: ${docNum}`, 'success');
            } else {
                log('env-output', '未找到文档编号参数', 'warning');
            }
            
            // 检测JavaScript支持
            log('env-output', 'JavaScript支持: 正常', 'success');
            log('env-output', `Fetch API支持: ${typeof fetch !== 'undefined' ? '是' : '否'}`, 
                typeof fetch !== 'undefined' ? 'success' : 'error');
            
            log('env-output', '环境检测完成', 'success');
        }

        // 2. API连接测试
        async function testAPI() {
            clearLog('api-output');
            log('api-output', '开始API连接测试...', 'info');
            
            const apiPorts = [8001, 8002, 8003, 8004];
            let workingAPI = null;
            
            for (const port of apiPorts) {
                try {
                    log('api-output', `测试端口 ${port}...`, 'info');
                    const response = await fetch(`http://localhost:${port}/api/status`, {
                        method: 'GET',
                        timeout: 5000
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        log('api-output', `✅ 端口 ${port} 连接成功`, 'success');
                        log('api-output', `状态: ${data.status}`, 'success');
                        log('api-output', `消息: ${data.message}`, 'success');
                        workingAPI = `http://localhost:${port}`;
                        break;
                    } else {
                        log('api-output', `❌ 端口 ${port} HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    log('api-output', `❌ 端口 ${port} 连接失败: ${error.message}`, 'error');
                }
            }
            
            if (workingAPI) {
                log('api-output', `API服务器地址: ${workingAPI}`, 'success');
                window.debugAPI = workingAPI;
            } else {
                log('api-output', '未找到可用的API服务器', 'error');
            }
        }

        // 3. 数据加载测试
        async function testDataLoading() {
            clearLog('data-output');
            log('data-output', '开始数据加载测试...', 'info');
            
            if (!window.debugAPI) {
                log('data-output', '请先测试API连接', 'error');
                return;
            }
            
            try {
                // 获取URL参数中的文档编号
                const urlParams = new URLSearchParams(window.location.search);
                const docNum = urlParams.get('doc_num') || 'ZDBXD2025042900003';
                
                log('data-output', `使用文档编号: ${docNum}`, 'info');
                
                // 测试审核报告API
                const reportUrl = `${window.debugAPI}/api/report?doc_num=${docNum}`;
                log('data-output', `请求URL: ${reportUrl}`, 'info');
                
                const response = await fetch(reportUrl);
                if (response.ok) {
                    const data = await response.json();
                    log('data-output', '✅ 审核报告加载成功', 'success');
                    
                    if (data.summary) {
                        const summary = data.summary;
                        log('data-output', `总规则数: ${summary.total_rules_checked}`, 'success');
                        log('data-output', `通过数: ${summary.passed_count}`, 'success');
                        log('data-output', `失败数: ${summary.failed_count}`, 'success');
                        log('data-output', `警告数: ${summary.warning_count}`, 'success');
                        
                        // 计算比率
                        const total = summary.total_rules_checked;
                        const passed = summary.passed_count;
                        const failed = summary.failed_count;
                        const warning = summary.warning_count;
                        
                        if (total > 0) {
                            const passRate = Math.round((passed / total) * 100);
                            const riskRate = Math.round(((failed + warning) / total) * 100);
                            log('data-output', `通过率: ${passRate}%`, 'success');
                            log('data-output', `风险率: ${riskRate}%`, 'success');
                        }
                        
                        window.debugData = data;
                    } else {
                        log('data-output', '⚠️ 数据中缺少summary字段', 'warning');
                    }
                } else {
                    log('data-output', `❌ 数据加载失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                log('data-output', `❌ 数据加载异常: ${error.message}`, 'error');
            }
        }

        // 4. 前端对象检测
        function checkFrontendObjects() {
            clearLog('frontend-output');
            log('frontend-output', '开始前端对象检测...', 'info');
            
            // 检测全局对象
            const objects = ['aiConsole', 'AIConsoleEnhanced', 'window.aiConsole'];
            
            for (const objName of objects) {
                try {
                    const obj = eval(objName);
                    if (obj) {
                        log('frontend-output', `✅ ${objName} 存在`, 'success');
                        if (objName === 'window.aiConsole' && obj.apiBaseUrl) {
                            log('frontend-output', `   API地址: ${obj.apiBaseUrl}`, 'info');
                        }
                        if (objName === 'window.aiConsole' && obj.realAuditData) {
                            log('frontend-output', '   已加载审核数据', 'success');
                        }
                    } else {
                        log('frontend-output', `❌ ${objName} 不存在`, 'error');
                    }
                } catch (error) {
                    log('frontend-output', `❌ ${objName} 检测失败: ${error.message}`, 'error');
                }
            }
            
            // 检测DOM元素
            const elements = ['audit-statistics', 'thinking-content', 'progress-bar'];
            for (const elementId of elements) {
                const element = document.getElementById(elementId);
                if (element) {
                    log('frontend-output', `✅ 元素 #${elementId} 存在`, 'success');
                } else {
                    log('frontend-output', `❌ 元素 #${elementId} 不存在`, 'error');
                }
            }
        }

        // 5. 手动数据更新
        async function manualDataUpdate() {
            clearLog('manual-output');
            log('manual-output', '开始手动数据更新...', 'info');
            
            if (!window.debugData) {
                log('manual-output', '请先加载数据', 'error');
                return;
            }
            
            try {
                // 尝试手动更新前端显示
                if (window.aiConsole && window.aiConsole.updateRealStats) {
                    log('manual-output', '找到updateRealStats方法', 'success');
                    window.aiConsole.updateRealStats(window.debugData.summary);
                    log('manual-output', '✅ 手动更新统计数据完成', 'success');
                } else {
                    log('manual-output', '❌ 未找到updateRealStats方法', 'error');
                }
                
                if (window.aiConsole && window.aiConsole.updateAuditStatistics) {
                    window.aiConsole.updateAuditStatistics(window.debugData.summary);
                    log('manual-output', '✅ 手动更新审核统计完成', 'success');
                } else {
                    log('manual-output', '❌ 未找到updateAuditStatistics方法', 'error');
                }
                
                // 尝试直接更新DOM元素
                const summary = window.debugData.summary;
                const total = summary.total_rules_checked;
                const passed = summary.passed_count;
                
                if (total > 0) {
                    const passRate = Math.round((passed / total) * 100);
                    
                    // 查找并更新可能的元素
                    const passRateElements = document.querySelectorAll('[class*="pass-rate"], [id*="pass-rate"]');
                    if (passRateElements.length > 0) {
                        passRateElements.forEach(el => {
                            el.textContent = `${passRate}%`;
                        });
                        log('manual-output', `✅ 直接更新通过率元素: ${passRate}%`, 'success');
                    }
                }
                
            } catch (error) {
                log('manual-output', `❌ 手动更新失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行环境检测
        window.addEventListener('load', function() {
            setTimeout(checkEnvironment, 1000);
        });
    </script>
</body>
</html>

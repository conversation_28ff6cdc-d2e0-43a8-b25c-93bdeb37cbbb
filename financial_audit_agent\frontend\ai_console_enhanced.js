// AI控制台增强版 - 集成真实数据
class AIConsoleEnhanced {
    constructor() {
        this.statusPollingInterval = null;
        this.realAuditData = null;
        this.realRulesData = null;
        this.apiBaseUrl = null;
        this.isInitialized = false;
        this.lastThinkingText = null; // 用于避免重复更新思考过程
        this.stateManager = null; // 新的状态管理器
        this.usingNewStateManager = false; // 标记是否使用新状态管理器
    }

    async init() {
        console.log('🔧 AIConsoleEnhanced.init() 开始...');
        try {
            console.log('🔧 检测API服务器...');
            this.apiBaseUrl = await this.detectAPIServer();

            if (this.apiBaseUrl) {
                console.log('✅ API服务器检测成功:', this.apiBaseUrl);
            } else {
                console.log('⚠️ API服务器未检测到，将使用默认数据');
            }

            console.log('🔧 初始化状态管理器...');
            if (this.apiBaseUrl) {
                this.stateManager = new window.StateManager();
                await this.stateManager.init(this.apiBaseUrl);
                this.usingNewStateManager = true;

                // 添加状态变化监听器
                this.stateManager.addListener((state) => {
                    this.handleStateChange(state);
                });
            }

            console.log('🔧 初始化审核统计...');
            this.initializeAuditStatistics();

            console.log('🔧 加载真实数据...');
            await this.loadRealData();

            console.log('🔧 状态管理已通过StateManager处理...');
            // 注意：不再启动旧的轮询机制，避免与新状态管理器冲突

            this.isInitialized = true;
            console.log('✅ AIConsoleEnhanced 初始化完成');
        } catch (error) {
            console.error('❌ AIConsoleEnhanced 初始化失败:', error);
        }
    }

    /**
     * 处理状态变化（新版：统一状态处理）
     */
    handleStateChange(state) {
        console.log('📊 状态变化:', state);
        console.log('📊 AI思考内容长度:', state.ai_thinking ? state.ai_thinking.length : 0);
        console.log('📊 AI思考内容预览:', state.ai_thinking ? state.ai_thinking.substring(0, 100) + '...' : '无');
        console.log('📊 审核状态:', state.audit_status);
        console.log('📊 当前阶段:', state.current_phase);

        // 检查是否审核完成
        if (this.stateManager && this.stateManager.isAuditComplete()) {
            // 获取文档编号
            const urlParams = new URLSearchParams(window.location.search);
            const docNum = urlParams.get('doc_num');

            if (docNum) {
                this.showCompletionNotification(docNum);

                // 更新页面标题
                const titleElement = document.querySelector('.system-title h1');
                if (titleElement) {
                    titleElement.textContent = 'AI财务审核系统 - 审核完成';
                }

                // 显示完成的思考过程
                this.showCompletedThinking(state.summary);
            }
        }

        // 更新进度
        const progressPercent = this.stateManager ? this.stateManager.getProgressPercent() : 0;
        this.updateProgressDisplay(progressPercent, state.message || '');

        // 更新各阶段进度 - 使用简化的状态数据
        this.updateRealEngineStatus(state.current_phase || 'ready', state);

        // 更新AI思考过程 - 简化逻辑，直接更新
        const aiThinking = state.ai_thinking || '';
        if (aiThinking) {
            console.log('🤖 执行AI思考过程更新');
            this.updateAIThinking(aiThinking);
        } else if (state.current_phase !== 'ready' && state.message) {
            // 如果没有AI思考内容，使用状态消息生成默认思考过程
            const defaultThinking = this.generateDefaultThinking(state.current_phase, state.message);
            this.updateAIThinking(defaultThinking);
        }

        // 更新统计数据 - 使用简化的摘要数据
        const summary = state.summary || this.stateManager?.getSummary() || {};
        this.updateStatisticsSimplified(summary, progressPercent);
    }

    updateStatisticsSimplified(summary, progressPercent) {
        /**
         * 简化的统计数据更新方法
         */
        try {
            // 基于进度百分比计算统计数据
            const totalRules = 38;
            let completedRules = 0;
            let passedRules = 0;
            let failedRules = 0;
            let warningRules = 0;

            // 根据进度计算完成的规则数
            if (progressPercent >= 100) {
                completedRules = totalRules;
                // 假设大部分规则通过，少数警告
                passedRules = Math.floor(totalRules * 0.9);
                warningRules = totalRules - passedRules;
            } else if (progressPercent > 0) {
                completedRules = Math.floor((progressPercent / 100) * totalRules);
                passedRules = Math.floor(completedRules * 0.9);
                warningRules = completedRules - passedRules;
            }

            // 更新审核统计显示
            this.updateAuditStatistics({
                total_rules_checked: totalRules,
                passed_count: passedRules,
                failed_count: failedRules,
                warning_count: warningRules
            });

            // 更新风险提醒
            this.updateRiskAlerts({
                passed_count: passedRules,
                failed_count: failedRules,
                warning_count: warningRules
            });

            console.log(`📊 简化统计更新: 完成${completedRules}/${totalRules}, 通过${passedRules}, 警告${warningRules}`);

        } catch (error) {
            console.error('❌ 简化统计更新失败:', error);
        }
    }

    // 检测API服务器
    async detectAPIServer() {
        const ports = [8001, 8002, 8003, 8004];

        for (const port of ports) {
            try {
                console.log(`🔍 测试端口 ${port}...`);
                const response = await fetch(`http://localhost:${port}/api/status`, {
                    method: 'GET',
                    timeout: 3000
                });

                if (response.ok) {
                    console.log(`✅ 端口 ${port} API服务器检测成功`);
                    return `http://localhost:${port}`;
                }
            } catch (error) {
                console.log(`❌ 端口 ${port} 连接失败:`, error.message);
            }
        }

        return null;
    }

    initializeAuditStatistics() {
        // 初始化审核统计显示
        const defaultStats = {
            total_rules_checked: 0,
            passed_count: 0,
            warning_count: 0,
            failed_count: 0
        };

        this.updateAuditStatistics(defaultStats);
        this.updateRiskAlerts(defaultStats);

        // 启动演示模式（如果没有真实数据）
        setTimeout(() => {
            if (!this.realAuditData) {
                this.startDemoMode();
            }
        }, 3000);
    }

    startDemoMode() {
        // 演示模式：循环展示不同的审核状态
        const demoScenarios = [
            {
                total_rules_checked: 38,
                passed_count: 38,
                warning_count: 0,
                failed_count: 0
            },
            {
                total_rules_checked: 38,
                passed_count: 36,
                warning_count: 2,
                failed_count: 0
            },
            {
                total_rules_checked: 38,
                passed_count: 32,
                warning_count: 4,
                failed_count: 2
            }
        ];

        let currentScenario = 0;

        const cycleDemoData = () => {
            const scenario = demoScenarios[currentScenario];
            this.updateAuditStatistics(scenario);
            this.updateRiskAlerts(scenario);

            currentScenario = (currentScenario + 1) % demoScenarios.length;
        };

        // 每8秒切换一次演示数据
        setInterval(cycleDemoData, 8000);

        // 立即执行一次
        cycleDemoData();

        // 启动神经网络动画
        this.startNeuralNetworkAnimation();
    }

    startNeuralNetworkAnimation() {
        // 随机激活神经元
        const neurons = document.querySelectorAll('.neuron');

        const activateRandomNeuron = () => {
            // 清除所有激活状态
            neurons.forEach(neuron => neuron.classList.remove('active'));

            // 随机激活几个神经元
            const activeCount = Math.floor(Math.random() * 3) + 2;
            const shuffled = Array.from(neurons).sort(() => 0.5 - Math.random());

            for (let i = 0; i < activeCount && i < shuffled.length; i++) {
                shuffled[i].classList.add('active');
            }
        };

        // 每2秒激活一次
        setInterval(activateRandomNeuron, 2000);

        // 立即激活一次
        activateRandomNeuron();
    }
    
    async loadRealData() {
        console.log('🔍 开始加载真实数据...');
        try {
            // 检测API服务器端口
            console.log('🔍 检测API服务器...');
            this.apiBaseUrl = await this.detectAPIServer();
            console.log('🔍 API检测结果:', this.apiBaseUrl);

            // 优先通过API加载数据
            if (this.apiBaseUrl) {
                console.log('✅ 使用API加载数据');
                await this.loadDataFromAPI();
            } else {
                console.log('⚠️ API不可用，使用本地文件');
                await this.loadDataFromFiles();
            }

            console.log('✅ 真实数据加载完成');
        } catch (error) {
            console.error('❌ 无法加载真实数据，使用模拟数据:', error);
        }
    }

    async loadDataFromAPI() {
        try {
            // 检查URL参数中是否有文档编号
            const urlParams = new URLSearchParams(window.location.search);
            const docNum = urlParams.get('doc_num');

            // 构建API请求URL
            let reportUrl = `${this.apiBaseUrl}/api/report`;
            if (docNum) {
                reportUrl += `?doc_num=${docNum}`;
                console.log(`🔍 请求特定文档报告: ${docNum}`);
            }

            // 加载审核报告
            const reportResponse = await fetch(reportUrl);
            if (reportResponse.ok) {
                this.realAuditData = await reportResponse.json();
                console.log('✅ 通过API加载审核报告');
                this.updateRealStats(this.realAuditData.summary);
            }

            // 加载规则信息
            const rulesResponse = await fetch(`${this.apiBaseUrl}/api/rules`);
            if (rulesResponse.ok) {
                const rulesData = await rulesResponse.json();
                console.log('✅ 通过API加载规则信息');
                this.updateRealRulesInfo(rulesData);
            }

            // 加载状态信息
            const statusResponse = await fetch(`${this.apiBaseUrl}/api/status`);
            if (statusResponse.ok) {
                const statusData = await statusResponse.json();
                this.updateRealStatus(statusData);
                console.log('✅ 通过API加载状态信息');
            }

        } catch (error) {
            console.log('⚠️ API数据加载失败，尝试本地文件:', error);
            await this.loadDataFromFiles();
        }
    }

    async loadDataFromFiles() {
        try {
            // 检查URL参数中是否有文档编号
            const urlParams = new URLSearchParams(window.location.search);
            const docNum = urlParams.get('doc_num');

            // 构建报告文件列表
            let reportFiles = [];
            if (docNum) {
                // 优先尝试加载特定文档的报告
                reportFiles.push(`../audit_reports/audit_report_${docNum}.json`);
                console.log(`🔍 尝试加载特定文档报告: audit_report_${docNum}.json`);
            }

            // 添加默认报告文件
            reportFiles = reportFiles.concat([
                '../audit_reports/audit_report_v2.json',
                '../audit_reports/audit_report.json',
                '../audit_reports/audit_report_default.json'
            ]);

            for (const reportFile of reportFiles) {
                try {
                    const reportResponse = await fetch(reportFile);
                    if (reportResponse.ok) {
                        this.realAuditData = await reportResponse.json();
                        console.log(`✅ 加载本地审核报告: ${reportFile}`);
                        this.updateRealStats(this.realAuditData.summary);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            // 加载状态文件
            const statusResponse = await fetch('../backend/audit_status.json');
            if (statusResponse.ok) {
                const statusData = await statusResponse.json();
                this.updateRealStatus(statusData);
                console.log('✅ 加载本地状态文件');
            }

        } catch (error) {
            console.log('⚠️ 本地文件加载失败:', error);
        }
    }

    async detectAPIServer() {
        // 尝试检测API服务器端口
        const possiblePorts = [8001, 8002, 8003, 8004, 8005];

        for (const port of possiblePorts) {
            try {
                // 使用AbortController来实现超时
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 2000);

                const response = await fetch(`http://localhost:${port}/api/status`, {
                    method: 'GET',
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    console.log(`✅ 检测到API服务器: http://localhost:${port}`);
                    return `http://localhost:${port}`;
                }
            } catch (error) {
                console.log(`⚠️ 端口 ${port} 连接失败:`, error.message);
                // 继续尝试下一个端口
            }
        }

        console.log('⚠️ 未检测到API服务器，将使用本地文件');
        return null;
    }
    
    startStatusPolling() {
        // 每3秒检查一次状态更新
        this.statusPollingInterval = setInterval(async () => {
            try {
                let response;
                let statusData;

                // 优先尝试本地状态文件（更可靠）
                try {
                    response = await fetch('../backend/audit_status.json');
                    if (response.ok) {
                        statusData = await response.json();
                        console.log('📁 本地状态更新:', statusData);

                        // 为本地文件添加阶段状态信息
                        if (statusData.current_step === 'report-generation' || statusData.current_step === 'finished') {
                            statusData.phase_status = {
                                'attachment': {'completed': 5, 'total': 5, 'progress': 100},
                                'consistency': {'completed': 19, 'total': 19, 'progress': 100},
                                'amount': {'completed': 6, 'total': 6, 'progress': 100},
                                'compliance': {'completed': 8, 'total': 8, 'progress': 100}
                            };
                            statusData.progress_percent = 100;
                        }
                    } else {
                        throw new Error('本地文件读取失败');
                    }
                } catch (localError) {
                    console.log('⚠️ 本地文件读取失败，尝试API');
                    // 本地文件失败时尝试API
                    if (this.apiBaseUrl) {
                        try {
                            response = await fetch(`${this.apiBaseUrl}/api/status`);
                            if (response.ok) {
                                statusData = await response.json();
                                console.log('📡 API状态更新:', statusData);
                            }
                        } catch (apiError) {
                            console.log('⚠️ API也连接失败');
                        }
                    }
                }

                if (statusData) {
                    this.updateRealStatus(statusData);
                    this.updateRealProgress(statusData);

                    // 如果有最终统计数据，更新审核统计
                    if (statusData.final_stats) {
                        this.updateAuditStatistics(statusData.final_stats);
                        this.updateRiskAlerts(statusData.final_stats);
                    }
                }
            } catch (error) {
                console.log('⚠️ 状态轮询错误:', error);
            }
        }, 3000);
    }

    updateRealProgress(statusData) {
        // 更新实时进度显示
        if (!statusData) return;

        // 更新进度条
        const progressBars = document.querySelectorAll('.progress-fill');
        const statusText = statusData.message || statusData.current_step || '系统就绪';

        // 根据当前步骤更新进度
        let progressPercent = 0;
        switch (statusData.current_step) {
            case 'ready':
                progressPercent = 0;
                break;
            case 'rule-parsing':
                progressPercent = statusData.status === 'completed' ? 20 : 10;
                break;
            case 'data-consolidation':
                progressPercent = statusData.status === 'completed' ? 30 : 25;
                break;
            case 'audit-step-1':
            case 'audit-step-2':
            case 'audit-step-3':
            case 'audit-step-4':
                // 根据审核步骤计算进度
                const stepNum = parseInt(statusData.current_step.split('-')[2]) || 1;
                const baseProgress = 30 + (stepNum - 1) * 15;
                progressPercent = statusData.status === 'completed' ? baseProgress + 15 : baseProgress;
                break;
            case 'report-generation':
                progressPercent = statusData.status === 'audit-complete' ? 100 : 90;
                break;
            case 'completed':
            case 'audit-complete':
                progressPercent = 100;
                break;
            default:
                // 如果有具体的进度值，使用它
                if (statusData.progress && typeof statusData.progress === 'number') {
                    progressPercent = statusData.progress;
                } else {
                    progressPercent = 30; // 默认进度
                }
        }

        // 更新所有进度条
        progressBars.forEach(bar => {
            bar.style.width = `${progressPercent}%`;
        });

        // 更新状态文本
        const statusElements = document.querySelectorAll('.status-text, .current-status');
        statusElements.forEach(element => {
            if (element) {
                element.textContent = statusText;
            }
        });

        console.log(`📊 进度更新: ${progressPercent}% - ${statusText}`);

        // 更新数据流监控
        this.updateDataStreamMonitoring(statusData);

        // 更新AI思考过程
        if (statusData.ai_thinking) {
            this.updateAIThinking(statusData.ai_thinking);
        } else if (statusData.status === 'running' && statusData.current_step) {
            // 如果没有具体的思考过程，但系统正在运行，显示当前步骤
            this.updateAIThinking(`正在执行: ${statusData.message || statusData.current_step}`);
        }
    }

    updateAIThinking(thinkingText) {
        // 更新AI思考过程显示 - 实现真正的增量更新
        const thinkingContent = document.getElementById('thinking-content');
        if (!thinkingContent) return;

        console.log('🤖 更新AI思考过程，新内容长度:', thinkingText.length);
        console.log('🤖 上次内容长度:', this.lastThinkingText ? this.lastThinkingText.length : 0);

        // 检查当前状态，如果审核已完成，显示完成状态
        const currentState = this.stateManager?.currentState;
        if (currentState?.audit_status === 'completed') {
            console.log('🤖 审核已完成，显示完成状态');
            this.showCompletedThinking(currentState.summary);
            this.lastThinkingText = thinkingText;
            return;
        }

        // 实现增量更新逻辑
        if (!this.lastThinkingText) {
            // 首次更新，直接渲染全部内容
            console.log('🤖 首次更新，渲染全部内容');
            this.renderFullThinkingContent(thinkingText, thinkingContent);
            this.lastThinkingText = thinkingText;
        } else if (thinkingText.length > this.lastThinkingText.length &&
                   thinkingText.startsWith(this.lastThinkingText)) {
            // 内容增加且是追加模式，进行增量更新
            const newContent = thinkingText.substring(this.lastThinkingText.length);
            console.log('🤖 检测到新增内容，长度:', newContent.length);
            console.log('🤖 新增内容预览:', newContent.substring(0, 100) + '...');

            this.appendIncrementalContent(newContent, thinkingContent);
            this.lastThinkingText = thinkingText;
        } else if (thinkingText !== this.lastThinkingText) {
            // 内容发生了非追加式变化，重新渲染全部
            console.log('🤖 内容发生变化，重新渲染全部');
            this.renderFullThinkingContent(thinkingText, thinkingContent);
            this.lastThinkingText = thinkingText;
        } else {
            console.log('🤖 内容未变化，跳过更新');
        }
    }

    // 新增：增量内容追加方法
    appendIncrementalContent(newContent, container) {
        console.log('🔄 开始增量追加内容');

        // 获取或创建markdown容器
        let markdownContainer = container.querySelector('.markdown-content');
        if (!markdownContainer) {
            markdownContainer = document.createElement('div');
            markdownContainer.className = 'markdown-content';
            container.appendChild(markdownContainer);
        }

        // 解析新内容为markdown
        const newMarkdownHtml = this.parseMarkdown(newContent);

        // 创建新内容元素
        const newElement = document.createElement('div');
        newElement.className = 'incremental-content';
        newElement.innerHTML = newMarkdownHtml;

        // 添加淡入动画
        newElement.style.opacity = '0';
        newElement.style.transform = 'translateY(10px)';
        newElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

        // 追加到容器
        markdownContainer.appendChild(newElement);

        // 触发动画
        requestAnimationFrame(() => {
            newElement.style.opacity = '1';
            newElement.style.transform = 'translateY(0)';
        });

        // 自动滚动到新内容
        this.scrollToLatestContent(container, newElement);

        console.log('✅ 增量内容追加完成');
    }

    // 新增：自动滚动到最新内容
    scrollToLatestContent(container, newElement) {
        // 检查是否需要滚动
        const containerRect = container.getBoundingClientRect();
        const elementRect = newElement.getBoundingClientRect();

        // 如果新元素不在视窗内，则滚动到它
        if (elementRect.bottom > window.innerHeight || elementRect.top < 0) {
            // 平滑滚动到新元素
            newElement.scrollIntoView({
                behavior: 'smooth',
                block: 'end',
                inline: 'nearest'
            });
        }
    }

    // 新增：计算内容相似度的方法
    calculateContentSimilarity(text1, text2) {
        if (!text1 || !text2) return 0;

        // 简单的相似度计算：基于共同字符数
        const shorter = text1.length < text2.length ? text1 : text2;
        const longer = text1.length >= text2.length ? text1 : text2;

        let commonChars = 0;
        for (let i = 0; i < shorter.length; i++) {
            if (longer[i] === shorter[i]) {
                commonChars++;
            } else {
                break; // 一旦不匹配就停止
            }
        }

        return commonChars / longer.length;
    }

    // 新增：更新相似内容的方法
    updateSimilarContent(thinkingText, container) {
        console.log('🤖 更新相似内容，避免重新渲染');

        // 只更新标题状态，不重新渲染整个内容
        const thinkingTitle = document.querySelector('.thinking-title');
        if (thinkingTitle) {
            thinkingTitle.textContent = '🤖 AI正在深度分析...';
        }

        // 可以在这里添加一些微小的视觉反馈，表示内容正在更新
        const markdownContainer = container.querySelector('.markdown-content');
        if (markdownContainer) {
            markdownContainer.style.opacity = '0.9';
            setTimeout(() => {
                markdownContainer.style.opacity = '1';
            }, 200);
        }
    }

    // 新增：检测是否是阶段性重复内容（改进版）
    isPhaseRepetitiveContent(thinkingText) {
        const currentState = this.stateManager?.currentState;
        if (!currentState) return false;

        // 检查各个阶段的内容标识
        const phaseIndicators = {
            1: '附件完整性检查 (阶段 1/4)',
            2: '字段内容与一致性检查 (阶段 2/4)',
            3: '金额与标准检查 (阶段 3/4)',
            4: '八项规定合规性检查 (阶段 4/4)'
        };

        // 确定内容属于哪个阶段
        let contentPhase = 0;
        for (const [phase, indicator] of Object.entries(phaseIndicators)) {
            if (thinkingText.includes(indicator)) {
                contentPhase = parseInt(phase);
                break;
            }
        }

        // 确定当前应该在哪个阶段
        const currentPhase = currentState.current_phase;
        const progress = currentState.progress_percent;

        let expectedPhase = 0;
        if (currentPhase === 'attachment-check' || progress <= 40) {
            expectedPhase = 1;
        } else if (currentPhase === 'field-consistency' || progress <= 60) {
            expectedPhase = 2;
        } else if (currentPhase === 'amount-standard' || progress <= 80) {
            expectedPhase = 3;
        } else if (currentPhase === 'compliance-check' || progress <= 100) {
            expectedPhase = 4;
        }

        // 只有当内容阶段明显落后于当前阶段时才认为是重复内容
        const isRepetitive = contentPhase > 0 && expectedPhase > 0 && contentPhase < expectedPhase - 1;

        if (isRepetitive) {
            console.log('🚨 检测到阶段严重不匹配:', {
                contentPhase: `第${contentPhase}阶段`,
                expectedPhase: `第${expectedPhase}阶段`,
                currentPhase: currentPhase,
                progress: progress + '%'
            });
        }

        return isRepetitive;
    }

    // 新增：处理阶段性重复内容
    handlePhaseRepetitiveContent(thinkingText, container) {
        console.log('🤖 处理阶段性重复内容');

        const currentState = this.stateManager?.currentState;
        const currentProgress = currentState?.progress_percent || 0;

        // 根据进度确定应该显示的阶段
        let targetPhase = '';
        if (currentProgress <= 25) {
            targetPhase = '## 🔍 附件完整性检查 (阶段 1/4)';
        } else if (currentProgress <= 50) {
            targetPhase = '## 🔍 字段内容与一致性检查 (阶段 2/4)';
        } else if (currentProgress <= 75) {
            targetPhase = '## 🔍 金额与标准检查 (阶段 3/4)';
        } else {
            targetPhase = '## 🔍 八项规定合规性检查 (阶段 4/4)';
        }

        // 提取目标阶段的内容
        const targetContent = this.extractPhaseContent(thinkingText, targetPhase);

        if (targetContent) {
            console.log(`🤖 提取到目标阶段内容: ${targetPhase}`);
            this.renderPhaseContent(targetContent, container, currentProgress);
        } else {
            // 如果无法提取目标阶段内容，显示当前进度信息
            this.renderProgressInfo(container, currentState);
        }
    }

    // 新增：提取特定阶段的内容
    extractPhaseContent(fullText, targetPhase) {
        const phaseIndex = fullText.indexOf(targetPhase);
        if (phaseIndex === -1) return null;

        // 查找下一个阶段的开始位置
        const nextPhasePatterns = [
            '## 🔍 字段内容与一致性检查 (阶段 2/4)',
            '## 🔍 金额与标准检查 (阶段 3/4)',
            '## 🔍 八项规定合规性检查 (阶段 4/4)',
            '## 🎯 审核总结'
        ];

        let endIndex = fullText.length;
        for (const pattern of nextPhasePatterns) {
            const nextIndex = fullText.indexOf(pattern, phaseIndex + targetPhase.length);
            if (nextIndex !== -1 && nextIndex < endIndex) {
                endIndex = nextIndex;
            }
        }

        return fullText.substring(phaseIndex, endIndex).trim();
    }

    // 新增：渲染阶段内容
    renderPhaseContent(content, container, progress) {
        console.log('🤖 渲染阶段内容，进度:', progress + '%');

        // 更新标题
        const thinkingTitle = document.querySelector('.thinking-title');
        if (thinkingTitle) {
            thinkingTitle.textContent = `🤖 AI正在分析 (${progress}%)`;
        }

        // 渲染内容
        this.renderMarkdownWithStreaming(content, container, thinkingTitle);
    }

    // 新增：渲染进度信息
    renderProgressInfo(container, currentState) {
        console.log('🤖 渲染进度信息');

        const progressInfo = `
## 🔄 审核进行中

**当前进度**: ${currentState?.progress_percent || 0}%
**当前阶段**: ${currentState?.message || '正在处理...'}
**详细信息**: ${currentState?.detail || ''}

正在进行深度分析，请稍候...
        `;

        this.renderMarkdownWithStreaming(progressInfo, container);
    }

    // 新增：更新完成状态的方法
    updateCompletionStatus(statusData) {
        const thinkingTitle = document.querySelector('.thinking-title');
        if (thinkingTitle) {
            thinkingTitle.textContent = '🎉 AI分析完成';
        }

        // 更新完成信息
        const completionInfo = document.createElement('div');
        completionInfo.className = 'completion-info';
        completionInfo.innerHTML = `
            <div class="completion-summary">
                <h3>📋 审核完成总结</h3>
                <p><strong>审核状态：</strong>${statusData.audit_status === 'completed' ? '✅ 已完成' : statusData.audit_status}</p>
                <p><strong>完成时间：</strong>${statusData.completion_time ? new Date(statusData.completion_time).toLocaleString() : '未知'}</p>
                <p><strong>总耗时：</strong>${statusData.detail || '未知'}</p>
                <p><strong>审核结果：</strong>${statusData.message || '审核完成'}</p>
            </div>
        `;

        // 检查是否已经添加了完成信息
        const existingInfo = document.querySelector('.completion-info');
        if (!existingInfo) {
            const thinkingContent = document.getElementById('thinking-content');
            if (thinkingContent) {
                thinkingContent.appendChild(completionInfo);
            }
        }
    }

    // 新增：增量更新思考内容的方法
    appendNewThinkingContent(newContent, container) {
        if (!newContent.trim()) return;

        // 更新标题为分析中状态
        const thinkingTitle = document.querySelector('.thinking-title');
        if (thinkingTitle) {
            thinkingTitle.textContent = '🤖 AI正在深度分析...';
        }

        // 获取或创建markdown容器
        let markdownContainer = container.querySelector('.markdown-content');
        if (!markdownContainer) {
            markdownContainer = document.createElement('div');
            markdownContainer.className = 'markdown-content';
            container.appendChild(markdownContainer);
        }

        // 渲染新内容并追加
        this.renderAndAppendContent(newContent, markdownContainer, thinkingTitle);
    }

    // 新增：完整渲染思考内容的方法
    renderFullThinkingContent(markdownText, container) {
        // 更新标题为分析中状态
        const thinkingTitle = document.querySelector('.thinking-title');
        if (thinkingTitle) {
            thinkingTitle.textContent = '🤖 AI正在深度分析...';
        }

        // 使用原有的完整渲染逻辑
        this.renderMarkdownWithStreaming(markdownText, container, thinkingTitle);
    }

    renderMarkdownWithStreaming(markdownText, container, titleElement) {
        // 清空容器
        container.innerHTML = '';

        // 确保容器有正确的滚动设置
        this.initializeScrollContainer(container);

        // 创建滚动指示器
        this.createScrollIndicator(container);

        // 创建Markdown容器
        const markdownContainer = document.createElement('div');
        markdownContainer.className = 'markdown-content';
        container.appendChild(markdownContainer);

        // 检查marked库是否可用
        if (typeof marked === 'undefined') {
            console.warn('⚠️ Marked库未加载，使用纯文本显示');
            markdownContainer.innerHTML = `<pre>${markdownText}</pre>`;
            if (titleElement) {
                titleElement.textContent = '✅ AI已分析完毕';
            }
            return;
        }

        // 配置marked选项
        marked.setOptions({
            breaks: true,
            gfm: true,
            headerIds: false,
            mangle: false
        });

        // 分段处理Markdown内容进行流式输出
        const sections = this.splitMarkdownIntoSections(markdownText);
        let currentIndex = 0;
        let userScrolling = false;

        // 监听用户滚动行为
        const scrollContainer = container.closest('.thinking-content');
        if (scrollContainer) {
            const scrollHandler = () => {
                userScrolling = this.isUserScrolling(markdownContainer);
            };
            scrollContainer.addEventListener('scroll', scrollHandler, { passive: true });

            // 清理监听器
            setTimeout(() => {
                scrollContainer.removeEventListener('scroll', scrollHandler);
            }, sections.length * 2000 + 5000); // 预估完成时间后清理
        }

        const renderNextSection = () => {
            if (currentIndex >= sections.length) {
                // 所有内容渲染完成
                if (titleElement) {
                    titleElement.textContent = '✅ AI已分析完毕';
                }
                // 最终滚动到底部
                if (!userScrolling) {
                    setTimeout(() => this.scrollToBottom(markdownContainer), 500);
                }
                console.log('🤖 Markdown流式渲染完成');
                return;
            }

            const section = sections[currentIndex];
            const sectionElement = document.createElement('div');
            sectionElement.className = 'markdown-section';
            sectionElement.style.opacity = '0';
            sectionElement.style.transform = 'translateY(20px)';

            try {
                // 渲染Markdown
                sectionElement.innerHTML = marked.parse(section);
            } catch (error) {
                console.warn('⚠️ Markdown解析错误:', error);
                sectionElement.innerHTML = `<pre>${section}</pre>`;
            }

            markdownContainer.appendChild(sectionElement);

            // 添加淡入动画
            setTimeout(() => {
                sectionElement.style.transition = 'all 0.5s ease-out';
                sectionElement.style.opacity = '1';
                sectionElement.style.transform = 'translateY(0)';
            }, 100);

            // 自动滚动到新内容（仅在用户未手动滚动时）
            setTimeout(() => {
                if (!userScrolling) {
                    this.scrollToLatestContent(markdownContainer, sectionElement);
                }
            }, 200);

            currentIndex++;

            // 递归渲染下一段，间隔时间根据内容长度调整
            const delay = Math.min(Math.max(section.length * 10, 500), 2000);
            setTimeout(renderNextSection, delay);
        };

        // 开始流式渲染
        renderNextSection();
    }

    // 新增：渲染并追加新内容的方法
    renderAndAppendContent(newContent, markdownContainer, titleElement) {
        // 检查marked库是否可用
        if (typeof marked === 'undefined') {
            console.warn('⚠️ Marked库未加载，使用纯文本显示');
            const textElement = document.createElement('pre');
            textElement.textContent = newContent;
            markdownContainer.appendChild(textElement);
            if (titleElement) {
                titleElement.textContent = '✅ AI已分析完毕';
            }
            return;
        }

        // 配置marked选项
        marked.setOptions({
            breaks: true,
            gfm: true,
            headerIds: false,
            mangle: false
        });

        // 分段处理新内容
        const sections = this.splitMarkdownIntoSections(newContent);
        let currentIndex = 0;

        const renderNextSection = () => {
            if (currentIndex >= sections.length) {
                // 新内容渲染完成
                if (titleElement) {
                    titleElement.textContent = '✅ AI已分析完毕';
                }
                // 滚动到底部显示新内容
                setTimeout(() => this.scrollToBottom(markdownContainer), 300);
                console.log('🤖 新内容追加完成');
                return;
            }

            const section = sections[currentIndex];
            const sectionElement = document.createElement('div');
            sectionElement.className = 'markdown-section new-content';
            sectionElement.style.opacity = '0';
            sectionElement.style.transform = 'translateY(20px)';

            try {
                // 渲染Markdown
                sectionElement.innerHTML = marked.parse(section);
            } catch (error) {
                console.warn('⚠️ Markdown解析错误:', error);
                sectionElement.innerHTML = `<pre>${section}</pre>`;
            }

            markdownContainer.appendChild(sectionElement);

            // 添加淡入动画
            setTimeout(() => {
                sectionElement.style.transition = 'all 0.5s ease-out';
                sectionElement.style.opacity = '1';
                sectionElement.style.transform = 'translateY(0)';
            }, 100);

            // 自动滚动到新内容
            setTimeout(() => {
                this.scrollToLatestContent(markdownContainer, sectionElement);
            }, 200);

            currentIndex++;

            // 递归渲染下一段
            const delay = Math.min(Math.max(section.length * 8, 300), 1500);
            setTimeout(renderNextSection, delay);
        };

        // 开始渲染新内容
        renderNextSection();
    }

    splitMarkdownIntoSections(markdownText) {
        // 将Markdown文本分割成逻辑段落
        const sections = [];
        const lines = markdownText.split('\n');
        let currentSection = '';

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // 如果遇到标题或空行，结束当前段落
            if (line.startsWith('#') || line.startsWith('##') || line.startsWith('###')) {
                if (currentSection.trim()) {
                    sections.push(currentSection.trim());
                    currentSection = '';
                }
                currentSection += line + '\n';
            } else if (line.trim() === '' && currentSection.trim()) {
                // 遇到空行，结束当前段落
                sections.push(currentSection.trim());
                currentSection = '';
            } else {
                currentSection += line + '\n';
            }
        }

        // 添加最后一个段落
        if (currentSection.trim()) {
            sections.push(currentSection.trim());
        }

        // 如果没有分段，将整个文本作为一段
        if (sections.length === 0) {
            sections.push(markdownText);
        }

        return sections;
    }

    scrollToLatestContent(container, newElement) {
        // 获取容器的父级滚动容器（thinking-content）
        const scrollContainer = container.closest('.thinking-content');
        if (!scrollContainer) {
            console.warn('⚠️ 未找到滚动容器');
            return;
        }

        // 检查是否需要滚动
        const containerRect = scrollContainer.getBoundingClientRect();
        const elementRect = newElement.getBoundingClientRect();

        // 如果新元素不在可视区域内，则滚动
        if (elementRect.bottom > containerRect.bottom || elementRect.top < containerRect.top) {
            // 计算滚动位置，确保新内容可见但不会滚动过头
            const scrollTop = scrollContainer.scrollTop;
            const containerHeight = scrollContainer.clientHeight;
            const elementOffsetTop = newElement.offsetTop;
            const elementHeight = newElement.offsetHeight;

            // 计算理想的滚动位置：让新元素出现在容器底部附近
            const idealScrollTop = elementOffsetTop + elementHeight - containerHeight + 50; // 50px缓冲

            // 平滑滚动到新位置
            scrollContainer.scrollTo({
                top: Math.max(0, idealScrollTop),
                behavior: 'smooth'
            });

            console.log('📜 自动滚动到新内容:', {
                elementTop: elementOffsetTop,
                elementHeight: elementHeight,
                containerHeight: containerHeight,
                scrollTo: Math.max(0, idealScrollTop)
            });
        }
    }

    // 滚动到容器底部的辅助函数
    scrollToBottom(container) {
        const scrollContainer = container.closest('.thinking-content');
        if (scrollContainer) {
            scrollContainer.scrollTo({
                top: scrollContainer.scrollHeight,
                behavior: 'smooth'
            });
        }
    }

    // 检查用户是否正在手动滚动
    isUserScrolling(container) {
        const scrollContainer = container.closest('.thinking-content');
        if (!scrollContainer) return false;

        // 检查是否接近底部（允许50px的误差）
        const isNearBottom = scrollContainer.scrollHeight - scrollContainer.scrollTop - scrollContainer.clientHeight < 50;
        return !isNearBottom;
    }

    // 初始化滚动容器
    initializeScrollContainer(container) {
        // 确保容器有thinking-content类
        if (!container.classList.contains('thinking-content')) {
            console.log('📜 为容器添加thinking-content类');
            container.classList.add('thinking-content');
        }

        // 记录容器信息用于调试
        const containerHeight = container.clientHeight;
        const maxHeight = window.innerHeight * 0.75; // 75vh

        console.log('📜 滚动容器初始化:', {
            containerHeight: containerHeight,
            maxHeight: maxHeight,
            viewportHeight: window.innerHeight,
            hasScrollbar: container.scrollHeight > container.clientHeight
        });

        // 如果容器高度超过最大高度，确保滚动条可见
        if (containerHeight > maxHeight) {
            container.style.maxHeight = '75vh';
            container.style.overflowY = 'auto';
        }
    }

    // 调试函数：显示滚动状态
    debugScrollStatus() {
        const container = document.getElementById('thinking-content');
        if (!container) {
            console.log('❌ 未找到thinking-content容器');
            return;
        }

        const scrollInfo = {
            containerHeight: container.clientHeight,
            scrollHeight: container.scrollHeight,
            scrollTop: container.scrollTop,
            maxHeight: getComputedStyle(container).maxHeight,
            overflowY: getComputedStyle(container).overflowY,
            hasScrollbar: container.scrollHeight > container.clientHeight,
            isAtBottom: container.scrollHeight - container.scrollTop - container.clientHeight < 10
        };

        console.log('📜 滚动状态调试信息:', scrollInfo);
        return scrollInfo;
    }

    // 创建滚动指示器
    createScrollIndicator(container) {
        // 检查是否已存在指示器
        const existingIndicator = container.parentElement?.querySelector('.scroll-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // 创建新的指示器
        const indicator = document.createElement('div');
        indicator.className = 'scroll-indicator';

        // 将指示器添加到容器的父元素
        const parentElement = container.parentElement;
        if (parentElement) {
            parentElement.style.position = 'relative';
            parentElement.appendChild(indicator);
        }

        // 监听滚动事件来控制指示器显示
        const scrollContainer = container.classList.contains('thinking-content') ? container : container.closest('.thinking-content');
        if (scrollContainer) {
            const updateIndicator = () => {
                const isAtBottom = scrollContainer.scrollHeight - scrollContainer.scrollTop - scrollContainer.clientHeight < 20;
                const hasMoreContent = scrollContainer.scrollHeight > scrollContainer.clientHeight;

                if (hasMoreContent && !isAtBottom) {
                    indicator.classList.add('visible');
                } else {
                    indicator.classList.remove('visible');
                }
            };

            scrollContainer.addEventListener('scroll', updateIndicator, { passive: true });

            // 初始检查
            setTimeout(updateIndicator, 100);

            // 定期检查（用于动态内容）
            const checkInterval = setInterval(() => {
                updateIndicator();
                // 如果内容渲染完成且用户在底部，清除定时器
                if (scrollContainer.scrollHeight - scrollContainer.scrollTop - scrollContainer.clientHeight < 20) {
                    clearInterval(checkInterval);
                }
            }, 1000);

            // 5分钟后清理定时器
            setTimeout(() => clearInterval(checkInterval), 300000);
        }
    }

    // 调试函数：测试滚动功能
    testScrollFeatures() {
        console.log('🧪 开始测试滚动功能...');

        // 检查容器状态
        const scrollStatus = this.debugScrollStatus();

        // 测试自动滚动
        const container = document.getElementById('thinking-content');
        if (container) {
            console.log('📜 测试自动滚动到底部...');
            this.scrollToBottom(container);

            setTimeout(() => {
                console.log('📜 测试滚动到顶部...');
                container.scrollTo({ top: 0, behavior: 'smooth' });
            }, 2000);

            setTimeout(() => {
                console.log('📜 测试滚动到中间...');
                container.scrollTo({
                    top: container.scrollHeight / 2,
                    behavior: 'smooth'
                });
            }, 4000);
        }

        // 检查响应式设计
        console.log('📱 当前视窗信息:', {
            width: window.innerWidth,
            height: window.innerHeight,
            isMobile: window.innerWidth <= 768,
            maxHeight: window.innerHeight * 0.75
        });

        return {
            scrollStatus,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight,
                isMobile: window.innerWidth <= 768
            }
        };
    }

    updateProgressDisplay(progressPercent, message) {
        /**
         * 更新进度显示
         */
        try {
            // 更新进度条
            const progressBar = document.querySelector('.progress-fill');
            if (progressBar) {
                progressBar.style.width = `${progressPercent}%`;
            }

            // 更新进度文本
            const progressText = document.querySelector('.progress-text');
            if (progressText) {
                progressText.textContent = `${progressPercent}%`;
            }

            // 更新状态消息
            const statusMessage = document.querySelector('.status-message');
            if (statusMessage && message) {
                statusMessage.textContent = message;
            }

            console.log(`📊 进度更新: ${progressPercent}% - ${message}`);

        } catch (error) {
            console.error('❌ 进度更新失败:', error);
        }
    }

    updateStatistics(summary) {
        /**
         * 更新统计数据显示
         */
        try {
            // 更新总规则数
            const totalRulesElement = document.querySelector('.total-rules');
            if (totalRulesElement && summary.total_rules) {
                totalRulesElement.textContent = summary.total_rules;
            }

            // 更新通过数
            const passedRulesElement = document.querySelector('.passed-rules');
            if (passedRulesElement && summary.passed_rules !== undefined) {
                passedRulesElement.textContent = summary.passed_rules;
            }

            // 更新失败数
            const failedRulesElement = document.querySelector('.failed-rules');
            if (failedRulesElement && summary.failed_rules !== undefined) {
                failedRulesElement.textContent = summary.failed_rules;
            }

            // 更新警告数
            const warningRulesElement = document.querySelector('.warning-rules');
            if (warningRulesElement && summary.warning_rules !== undefined) {
                warningRulesElement.textContent = summary.warning_rules;
            }

            console.log('📊 统计数据更新:', summary);

        } catch (error) {
            console.error('❌ 统计数据更新失败:', error);
        }
    }

    showDefaultThinking() {
        const thinkingContent = document.getElementById('thinking-content');
        if (!thinkingContent) return;

        thinkingContent.innerHTML = `
            <div class="thinking-step default-thinking">
                <div class="step-indicator active"></div>
                <div class="step-text">等待AI分析引擎启动...</div>
            </div>
        `;
    }

    generateDefaultThinking(currentPhase, message) {
        /**
         * 根据当前阶段生成默认思考过程
         */
        const phaseThinking = {
            'rule-parsing': '• 正在解析审核规则文件\n• 初始化规则引擎\n• 准备数据验证流程',
            'data-consolidation': '• 正在整合表单和附件数据\n• 验证数据完整性\n• 建立数据关联关系',
            'attachment-check': '• 正在检查附件完整性\n• 验证必需文件是否齐全\n• 分析附件与表单的一致性',
            'field-consistency': '• 正在进行字段一致性检查\n• 验证关键数据的匹配性\n• 检查日期、金额等关键信息',
            'amount-standard': '• 正在执行金额标准检查\n• 验证预算范围和限额\n• 分析费用的合理性',
            'compliance-check': '• 正在进行合规性检查\n• 应用业务规则和政策\n• 识别潜在的合规风险',
            'report-generation': '• 正在生成审核报告\n• 汇总所有检查结果\n• 计算风险评分和建议'
        };

        return phaseThinking[currentPhase] || `• 正在执行${message || '审核流程'}\n• AI引擎正在分析中\n• 请稍候...`;
    }

    showCompletedThinking(finalStats) {
        const thinkingContent = document.getElementById('thinking-content');
        if (!thinkingContent) return;

        // 更新标题
        const thinkingTitle = document.querySelector('.thinking-title');
        if (thinkingTitle) {
            thinkingTitle.textContent = '✅ AI分析完成';
        }

        const completionText = finalStats ?
            `审核完成！共检查 ${finalStats.total} 条规则，通过 ${finalStats.passed} 条，警告 ${finalStats.warning || 0} 条，失败 ${finalStats.failed || 0} 条。` :
            '审核流程已全部完成，生成详细审核报告。';

        thinkingContent.innerHTML = `
            <div class="thinking-steps-container">
                <div class="thinking-step">
                    <div class="step-indicator active"></div>
                    <div class="step-text">🎉 ${completionText}</div>
                </div>
                <div class="thinking-step">
                    <div class="step-indicator active"></div>
                    <div class="step-text">📊 所有规则检查已完成，审核报告已生成</div>
                </div>
                <div class="thinking-step">
                    <div class="step-indicator active"></div>
                    <div class="step-text">💾 审核结果已保存，可通过前端界面查看详细信息</div>
                </div>
            </div>
        `;
    }

    updateDataStreamMonitoring(statusData) {
        // 更新数据流监控显示
        const dataStreamContainer = document.getElementById('data-stream');
        if (!dataStreamContainer) return;

        // 创建数据流项目
        const streamItem = document.createElement('div');
        streamItem.className = 'stream-item';
        streamItem.innerHTML = `
            <div class="stream-timestamp">${statusData.timestamp || new Date().toLocaleTimeString()}</div>
            <div class="stream-content">
                <div class="stream-step">${statusData.current_step || 'unknown'}</div>
                <div class="stream-message">${statusData.message || '无消息'}</div>
                ${statusData.detail ? `<div class="stream-detail">${statusData.detail}</div>` : ''}
            </div>
            <div class="stream-status ${statusData.status || 'running'}">${this.getStatusIcon(statusData.status)}</div>
        `;

        // 添加到数据流容器顶部
        dataStreamContainer.insertBefore(streamItem, dataStreamContainer.firstChild);

        // 限制显示的项目数量（最多显示10个）
        while (dataStreamContainer.children.length > 10) {
            dataStreamContainer.removeChild(dataStreamContainer.lastChild);
        }

        // 添加动画效果
        streamItem.style.opacity = '0';
        streamItem.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            streamItem.style.transition = 'all 0.3s ease';
            streamItem.style.opacity = '1';
            streamItem.style.transform = 'translateY(0)';
        }, 100);
    }

    getStatusIcon(status) {
        switch (status) {
            case 'completed':
            case 'audit-complete':
                return '✅';
            case 'running':
                return '🔄';
            case 'failed':
                return '❌';
            default:
                return '⏳';
        }
    }

    updateRealRulesInfo(rulesData) {
        // 更新规则阶段信息显示
        if (!rulesData) return;

        // 更新规则统计 - 基于4大审核阶段
        const totalRules = rulesData.total_rules || 38; // 5+19+6+8=38

        // 更新总规则数显示
        const totalRulesElement = document.getElementById('total-rules');
        if (totalRulesElement) {
            totalRulesElement.textContent = totalRules;
        }

        console.log('📋 规则信息已更新:', rulesData);
    }

    updateRealStats(summaryData) {
        // 更新统计数据显示
        if (!summaryData) {
            console.log('⚠️ 统计数据为空，跳过更新');
            return;
        }

        console.log('📊 开始更新统计数据:', summaryData);

        // 更新主要统计数字（使用页面中实际存在的元素ID）
        const rulesCompletedElement = document.getElementById('rules-completed');
        const totalRulesElement = document.getElementById('total-rules');

        if (rulesCompletedElement) {
            const completedCount = (summaryData.passed_count || 0) + (summaryData.failed_count || 0) + (summaryData.warning_count || 0);
            rulesCompletedElement.textContent = completedCount;
            console.log(`✅ 更新已完成规则数: ${completedCount}`);
        } else {
            console.log('⚠️ 未找到 rules-completed 元素');
        }

        if (totalRulesElement) {
            totalRulesElement.textContent = summaryData.total_rules_checked || 38;
            console.log(`✅ 更新总规则数: ${summaryData.total_rules_checked || 38}`);
        } else {
            console.log('⚠️ 未找到 total-rules 元素');
        }

        // 更新通过率显示
        const passRateBar = document.getElementById('pass-rate-bar');
        const passRateValue = document.getElementById('pass-rate-value');
        if (passRateBar && passRateValue && summaryData.total_rules_checked > 0) {
            const passRate = Math.round((summaryData.passed_count || 0) / summaryData.total_rules_checked * 100);
            passRateBar.style.width = `${passRate}%`;
            passRateValue.textContent = `${passRate}%`;
            console.log(`✅ 更新通过率: ${passRate}%`);
        }

        // 更新风险率显示
        const riskRateBar = document.getElementById('risk-rate-bar');
        const riskRateValue = document.getElementById('risk-rate-value');
        if (riskRateBar && riskRateValue && summaryData.total_rules_checked > 0) {
            const riskRate = Math.round((summaryData.failed_count || 0) / summaryData.total_rules_checked * 100);
            riskRateBar.style.width = `${riskRate}%`;
            riskRateValue.textContent = `${riskRate}%`;
            console.log(`✅ 更新风险率: ${riskRate}%`);
        }

        // 更新审核统计监控
        this.updateAuditStatistics(summaryData);

        // 更新风险提醒
        this.updateRiskAlerts(summaryData);

        // 更新状态指示器
        const statusElement = document.getElementById('data-link-status');
        if (statusElement) {
            statusElement.textContent = `🔗 实时数据已更新 (${new Date().toLocaleTimeString()})`;
            statusElement.style.color = '#00ff88';
        }

        console.log('✅ 统计数据更新完成:', summaryData);
    }

    updateAuditStatistics(summaryData) {
        // 计算通过率
        const total = summaryData.total_rules_checked || 0;
        const passed = summaryData.passed_count || 0;
        const passRate = total > 0 ? Math.round((passed / total) * 100) : 0;

        // 计算风险等级
        const failed = summaryData.failed_count || 0;
        const warning = summaryData.warning_count || 0;
        let riskLevel = '低风险';
        let riskPercent = 20;

        if (failed > 0) {
            riskLevel = '高风险';
            riskPercent = 80;
        } else if (warning > 2) {
            riskLevel = '中风险';
            riskPercent = 60;
        } else if (warning > 0) {
            riskLevel = '低风险';
            riskPercent = 30;
        }

        // 计算合规评分
        const complianceScore = Math.max(0, 100 - (failed * 20) - (warning * 5));

        // 更新显示
        const passRateBar = document.getElementById('pass-rate-bar');
        const passRateValue = document.getElementById('pass-rate-value');
        const riskLevelBar = document.getElementById('risk-level-bar');
        const riskLevelValue = document.getElementById('risk-level-value');
        const complianceScoreBar = document.getElementById('compliance-score-bar');
        const complianceScoreValue = document.getElementById('compliance-score-value');

        if (passRateBar && passRateValue) {
            this.animateProgressBar(passRateBar, passRate);
            this.animateNumber(passRateValue, passRate, '%');
        }

        if (riskLevelBar && riskLevelValue) {
            this.animateProgressBar(riskLevelBar, riskPercent);
            riskLevelValue.textContent = riskLevel;
            this.addTextGlowEffect(riskLevelValue);
        }

        if (complianceScoreBar && complianceScoreValue) {
            this.animateProgressBar(complianceScoreBar, complianceScore);
            this.animateNumber(complianceScoreValue, complianceScore, '分');
        }
    }

    updateRiskAlerts(summaryData) {
        const alertsContainer = document.getElementById('risk-alerts-container');
        if (!alertsContainer) return;

        const alerts = [];
        const failed = summaryData.failed_count || 0;
        const warning = summaryData.warning_count || 0;
        const passed = summaryData.passed_count || 0;

        // 生成风险提醒
        if (failed > 0) {
            alerts.push({
                type: 'error',
                icon: '🚨',
                title: '发现严重问题',
                desc: `检测到 ${failed} 个不合规项目，需要立即处理`
            });
        }

        if (warning > 0) {
            alerts.push({
                type: 'warning',
                icon: '⚠️',
                title: '存在风险提醒',
                desc: `发现 ${warning} 个潜在风险点，建议人工复核`
            });
        }

        if (failed === 0 && warning === 0 && passed > 0) {
            alerts.push({
                type: 'success',
                icon: '✅',
                title: '审核通过',
                desc: `所有 ${passed} 项规则检查均已通过`
            });
        }

        if (alerts.length === 0) {
            alerts.push({
                type: 'info',
                icon: 'ℹ️',
                title: '系统就绪',
                desc: '等待开始审核'
            });
        }

        // 更新显示
        alertsContainer.innerHTML = alerts.map((alert, index) => `
            <div class="alert-item ${alert.type}" style="animation-delay: ${index * 0.2}s">
                <div class="alert-icon">${alert.icon}</div>
                <div class="alert-content">
                    <div class="alert-title">${alert.title}</div>
                    <div class="alert-desc">${alert.desc}</div>
                </div>
            </div>
        `).join('');
    }

    // 动画辅助方法
    animateProgressBar(element, targetWidth) {
        if (!element) return;

        const currentWidth = parseFloat(element.style.width) || 0;
        const increment = (targetWidth - currentWidth) / 20;
        let currentStep = 0;

        const animate = () => {
            if (currentStep < 20) {
                const newWidth = currentWidth + (increment * currentStep);
                element.style.width = `${Math.min(newWidth, targetWidth)}%`;
                currentStep++;
                requestAnimationFrame(animate);
            } else {
                element.style.width = `${targetWidth}%`;
            }
        };

        animate();
    }

    animateNumber(element, targetValue, suffix = '') {
        if (!element) return;

        const currentValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - currentValue) / 15;
        let currentStep = 0;

        const animate = () => {
            if (currentStep < 15) {
                const newValue = Math.round(currentValue + (increment * currentStep));
                element.textContent = `${newValue}${suffix}`;
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 100);
                currentStep++;
                setTimeout(animate, 50);
            } else {
                element.textContent = `${targetValue}${suffix}`;
            }
        };

        animate();
    }

    addTextGlowEffect(element) {
        if (!element) return;

        element.style.animation = 'none';
        setTimeout(() => {
            element.style.animation = 'titleGlow 2s ease-in-out infinite';
        }, 10);
    }

    updateRealStatus(statusData) {
        // 如果使用新状态管理器，避免重复更新
        if (this.usingNewStateManager) {
            console.log('🔄 跳过旧状态更新，使用新状态管理器');
            return;
        }

        // 检查是否为已完成状态
        if (statusData.status === 'audit-complete' || statusData.current_step === 'report-generation') {
            // 获取文档编号
            const urlParams = new URLSearchParams(window.location.search);
            const docNum = urlParams.get('doc_num');

            if (docNum) {
                // 显示完成提示
                this.showCompletionNotification(docNum);

                // 更新页面标题
                const titleElement = document.querySelector('.system-title h1');
                if (titleElement) {
                    titleElement.textContent = 'AI财务审核系统 - 审核完成';
                }

                // 显示完成的思考过程
                this.showCompletedThinking(statusData.final_stats);
            }
        }

        // 更新当前执行步骤 - 基于新的4大审核阶段
        const currentPhase = statusData.current_step || statusData.current_phase || 'ready';

        // 更新AI思维显示
        this.updateRealThinking(statusData);

        // 更新规则引擎状态
        this.updateRealEngineStatus(currentPhase, statusData);

        // 更新统计数据
        if (statusData.final_stats) {
            this.updateRealStats(statusData.final_stats);
        }
    }

    showCompletionNotification(docNum) {
        // 显示审核完成通知
        const notification = document.createElement('div');
        notification.className = 'completion-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">🎉</div>
                <div class="notification-text">
                    <h3>审核完成！</h3>
                    <p>文档 ${docNum} 的审核已成功完成</p>
                    <p>您可以查看详细的审核报告和统计数据</p>
                </div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #00ff88, #00cc6a);
            color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 255, 136, 0.3);
            z-index: 1000;
            animation: slideIn 0.5s ease-out;
        `;

        document.body.appendChild(notification);

        // 5秒后自动消失
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    updateRealThinking(statusData) {
        const thinkingContent = document.getElementById('thinking-content');
        if (!thinkingContent) return;
        
        // 基于真实状态生成思维步骤
        const realThinkingSteps = this.generateRealThinkingSteps(statusData);
        
        thinkingContent.innerHTML = '';
        realThinkingSteps.forEach((step, index) => {
            setTimeout(() => {
                const stepElement = document.createElement('div');
                stepElement.className = 'thinking-step';
                stepElement.innerHTML = `
                    <div class="step-indicator"></div>
                    <div class="step-text">${step}</div>
                `;
                thinkingContent.appendChild(stepElement);
            }, index * 500);
        });
    }
    
    generateRealThinkingSteps(statusData) {
        const stepMessages = {
            'data-loading': [
                '正在解析输入的JSON数据结构...',
                '验证附件列表完整性...',
                '初始化确定性规则引擎...'
            ],
            'rule-parsing': [
                '启动关键词检测模块...',
                '扫描敏感词汇和风险内容...',
                '执行合规性筛查算法...'
            ],
            'audit-execution': [
                '激活大语言模型推理引擎...',
                '进行深度语义理解分析...',
                '生成智能审核建议...'
            ],
            'report-generation': [
                '整合所有规则执行结果...',
                '生成综合风险评估报告...',
                '审核流程完成！'
            ]
        };
        
        return stepMessages[statusData.current_step] || stepMessages['data-loading'];
    }
    
    updateRealEngineStatus(currentPhase, statusData) {
        // 基于真实数据更新四大审核阶段状态 - 简化版本
        const phases = [
            { id: 'attachment', name: '附件完整性检查', count: 5 },
            { id: 'consistency', name: '字段内容与一致性检查', count: 19 },
            { id: 'amount', name: '金额与标准检查', count: 6 },
            { id: 'compliance', name: '八项规定合规性检查', count: 8 }
        ];

        // 从状态数据中获取当前阶段信息
        const currentPhaseInfo = this.getCurrentPhaseInfo(statusData);
        const currentProgress = statusData.progress_percent || 0;

        // 简化的进度计算：基于总体进度百分比
        phases.forEach((phase, index) => {
            const layer = document.getElementById(`${phase.id}-layer`);
            const status = document.getElementById(`${phase.id}-status`);
            const progress = document.getElementById(`${phase.id}-progress`);
            const text = document.getElementById(`${phase.id}-text`);

            if (!layer || !status || !progress || !text) return;

            // 基于总体进度计算各阶段状态
            let phaseStatus = 'pending';
            let phaseProgress = 0;
            let completedCount = 0;

            // 根据总体进度确定各阶段状态
            if (index === 0) { // 附件完整性检查 (0-25%)
                if (currentProgress >= 25) {
                    phaseStatus = 'completed';
                    phaseProgress = 100;
                    completedCount = phase.count;
                } else if (currentProgress > 0) {
                    phaseStatus = 'active';
                    phaseProgress = (currentProgress / 25) * 100;
                    completedCount = Math.floor((currentProgress / 25) * phase.count);
                }
            } else if (index === 1) { // 字段内容与一致性检查 (25-63%)
                if (currentProgress >= 63) {
                    phaseStatus = 'completed';
                    phaseProgress = 100;
                    completedCount = phase.count;
                } else if (currentProgress > 25) {
                    phaseStatus = 'active';
                    phaseProgress = ((currentProgress - 25) / 38) * 100;
                    completedCount = Math.floor(((currentProgress - 25) / 38) * phase.count);
                }
            } else if (index === 2) { // 金额与标准检查 (63-79%)
                if (currentProgress >= 79) {
                    phaseStatus = 'completed';
                    phaseProgress = 100;
                    completedCount = phase.count;
                } else if (currentProgress > 63) {
                    phaseStatus = 'active';
                    phaseProgress = ((currentProgress - 63) / 16) * 100;
                    completedCount = Math.floor(((currentProgress - 63) / 16) * phase.count);
                }
            } else if (index === 3) { // 八项规定合规性检查 (79-100%)
                if (currentProgress >= 100) {
                    phaseStatus = 'completed';
                    phaseProgress = 100;
                    completedCount = phase.count;
                } else if (currentProgress > 79) {
                    phaseStatus = 'active';
                    phaseProgress = ((currentProgress - 79) / 21) * 100;
                    completedCount = Math.floor(((currentProgress - 79) / 21) * phase.count);
                }
            }

            // 更新UI显示
            if (phaseStatus === 'completed') {
                layer.classList.remove('active');
                layer.classList.add('completed');
                status.textContent = '已完成';
                status.style.background = 'rgba(0, 255, 136, 0.3)';
                status.style.color = 'var(--accent-green)';
                progress.style.width = '100%';
                text.textContent = `${phase.count}/${phase.count}`;
            } else if (phaseStatus === 'active') {
                layer.classList.add('active');
                layer.classList.remove('completed');
                status.textContent = '执行中';
                status.style.background = 'rgba(0, 212, 255, 0.3)';
                status.style.color = 'var(--accent-blue)';
                progress.style.width = `${phaseProgress}%`;
                text.textContent = `${completedCount}/${phase.count}`;
            } else {
                layer.classList.remove('active', 'completed');
                status.textContent = '待执行';
                status.style.background = 'rgba(128, 128, 128, 0.2)';
                status.style.color = 'var(--text-secondary)';
                progress.style.width = '0%';
                text.textContent = `0/${phase.count}`;
            }
        });

        // 更新总体统计
        this.updateOverallStatsSimplified(phases, currentProgress, statusData);
    }

    updateOverallStatsSimplified(phases, currentProgress, statusData) {
        /**
         * 简化的总体统计更新方法
         */
        try {
            // 计算总完成规则数
            let totalCompleted = 0;
            let totalRules = 38; // 5+19+6+8

            // 根据进度计算完成的规则数
            if (currentProgress >= 100) {
                totalCompleted = totalRules;
            } else {
                // 按阶段计算完成数
                if (currentProgress >= 25) totalCompleted += 5;  // 附件完整性
                if (currentProgress >= 63) totalCompleted += 19; // 字段一致性
                if (currentProgress >= 79) totalCompleted += 6;  // 金额标准
                if (currentProgress >= 100) totalCompleted += 8; // 合规性检查

                // 如果在某个阶段中间，按比例计算
                if (currentProgress > 0 && currentProgress < 25) {
                    totalCompleted = Math.floor((currentProgress / 25) * 5);
                } else if (currentProgress > 25 && currentProgress < 63) {
                    totalCompleted = 5 + Math.floor(((currentProgress - 25) / 38) * 19);
                } else if (currentProgress > 63 && currentProgress < 79) {
                    totalCompleted = 24 + Math.floor(((currentProgress - 63) / 16) * 6);
                } else if (currentProgress > 79 && currentProgress < 100) {
                    totalCompleted = 30 + Math.floor(((currentProgress - 79) / 21) * 8);
                }
            }

            // 更新已完成规则数显示
            const rulesCompletedElement = document.getElementById('rules-completed');
            if (rulesCompletedElement) {
                rulesCompletedElement.textContent = totalCompleted;
            }

            // 更新总规则数显示
            const totalRulesElement = document.getElementById('total-rules');
            if (totalRulesElement) {
                totalRulesElement.textContent = totalRules;
            }

            console.log(`📊 统计更新: ${totalCompleted}/${totalRules} (${currentProgress}%)`);

        } catch (error) {
            console.error('❌ 统计更新失败:', error);
        }
    }
    
    getCurrentPhaseInfo(statusData) {
        // 根据后端状态数据确定当前审核阶段
        const phaseMapping = {
            'attachment-check': { index: 0, name: '附件完整性检查' },
            'field-consistency': { index: 1, name: '字段内容与一致性检查' },
            'amount-standard': { index: 2, name: '金额与标准检查' },
            'compliance-check': { index: 3, name: '八项规定合规性检查' },
            'report-generation': { index: 4, name: '报告生成' },
            'finished': { index: 4, name: '审核完成' }
        };

        const currentStep = statusData.current_step || statusData.current_phase || 'ready';
        const phaseInfo = phaseMapping[currentStep] || { index: 0, name: '准备中' };

        return {
            currentIndex: phaseInfo.index,
            name: phaseInfo.name,
            progress: statusData.progress_percent || 0
        };
    }

    parseAIThinkingForRuleProgress(aiThinking) {
        /**
         * 解析AI思维链，提取每个阶段的规则完成情况
         * @param {string} aiThinking - AI思维链内容
         * @returns {Object} 各阶段的规则完成统计
         */
        if (!aiThinking) {
            return {
                attachment: { completed: 0, total: 5 },
                consistency: { completed: 0, total: 19 },
                amount: { completed: 0, total: 6 },
                compliance: { completed: 0, total: 8 }
            };
        }

        const phaseProgress = {
            attachment: { completed: 0, total: 5 },
            consistency: { completed: 0, total: 19 },
            amount: { completed: 0, total: 6 },
            compliance: { completed: 0, total: 8 }
        };

        // 定义规则范围映射
        const ruleRanges = {
            attachment: { start: 1, end: 5 },      // 规则1-5
            consistency: { start: 6, end: 24 },    // 规则6-24
            amount: { start: 25, end: 30 },        // 规则25-30
            compliance: { start: 31, end: 38 }     // 规则31-38
        };

        // 定义合并规则映射（一个合并规则算作多条规则）
        const mergedRules = {
            '27-28': [27, 28],  // 规则27-28算作规则27和28
            '36-37': [36, 37]   // 规则36-37算作规则36和37
        };

        // 解析已完成的规则 - 支持多种格式和更精确的匹配
        const rulePatterns = [
            // 匹配 "规则X：...状态：通过" 格式
            /规则(\d+)：.*?状态.*?：\s*(通过|不通过|警告|无法判断)/gi,
            // 匹配 "规则X：...状态应为"通过"" 格式
            /规则(\d+)：.*?状态应为\s*["""]?(通过|不通过|警告|无法判断)["""]?/gi,
            // 匹配 "规则X：...应该为"通过"" 格式
            /规则(\d+)：.*?应该为\s*["""]?(通过|不通过|警告|无法判断)["""]?/gi,
            // 匹配 "规则X：...status：通过" 格式
            /规则(\d+)：.*?status\s*[:：]\s*(通过|不通过|警告|无法判断)/gi,
            // 匹配 "- 规则X: 通过" 格式
            /-\s*规则(\d+):\s*(通过|不通过|警告|无法判断)/gi,
            // 匹配 "规则X: 通过" 格式
            /规则(\d+):\s*(通过|不通过|警告|无法判断)/gi,
            // 匹配 "状态：通过" 在规则X附近的格式
            /规则(\d+)[\s\S]{0,200}?状态\s*[:：]\s*(通过|不通过|警告|无法判断)/gi,
            // 匹配合并规则 "规则X-Y：...状态：通过" 格式
            /规则(\d+-\d+)：.*?状态\s*[:：]\s*(通过|不通过|警告|无法判断)/gi,
            // 匹配 JSON 格式中的规则
            /"rule_id":\s*"规则(\d+)[^"]*"[^}]*"status":\s*"(通过|不通过|警告|无法判断)"/gi,
            // 匹配 JSON 格式中的合并规则
            /"rule_id":\s*"规则(\d+-\d+)[^"]*"[^}]*"status":\s*"(通过|不通过|警告|无法判断)"/gi,
            /"status":\s*"(通过|不通过|警告|无法判断)"[^}]*"rule_id":\s*"规则(\d+)/gi,
            // 匹配控制台日志格式 "📋 模式X匹配到规则Y：通过"
            /📋\s*模式\d+匹配到规则(\d+)：(通过|不通过|警告|无法判断)/gi,
            // 匹配控制台日志格式 "📋 模式X匹配到规则Y-Z：通过"
            /📋\s*模式\d+匹配到规则(\d+-\d+)：(通过|不通过|警告|无法判断)/gi
        ];

        const completedRules = new Set(); // 避免重复计数

        console.log('🔍 开始解析AI思维链，内容长度:', aiThinking.length);
        console.log('🔍 AI思维链前500字符:', aiThinking.substring(0, 500));

        // 特殊处理：如果AI思维链中包含控制台日志信息，优先解析
        const consoleLogPattern = /📋\s*模式(\d+)匹配到规则(\d+)：(通过|不通过|警告|无法判断)/gi;
        let consoleMatch;
        let consoleRulesFound = 0;

        while ((consoleMatch = consoleLogPattern.exec(aiThinking)) !== null) {
            const ruleNumber = parseInt(consoleMatch[2]);
            const status = consoleMatch[3];

            if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
                completedRules.add(ruleNumber);
                consoleRulesFound++;
                console.log(`📋 控制台日志匹配到规则${ruleNumber}：${status}`);
            }
        }

        if (consoleRulesFound > 0) {
            console.log(`✅ 从控制台日志解析到${consoleRulesFound}条规则`);
        }

        // 专门解析"状态应为"格式的规则（包括合并规则）
        const shouldBePattern = /规则(\d+(?:-\d+)?)[\s\S]{0,500}?状态应为\s*["""]?(通过|不通过|警告|无法判断)["""]?/gi;
        let shouldBeMatch;
        while ((shouldBeMatch = shouldBePattern.exec(aiThinking)) !== null) {
            const ruleId = shouldBeMatch[1];
            const status = shouldBeMatch[2];

            // 处理合并规则
            if (ruleId.includes('-')) {
                const mergedRuleNumbers = mergedRules[ruleId];
                if (mergedRuleNumbers) {
                    mergedRuleNumbers.forEach(ruleNumber => {
                        completedRules.add(ruleNumber);
                        console.log(`📋 "状态应为"格式匹配到合并规则${ruleId}中的规则${ruleNumber}：${status}`);
                    });
                }
            } else {
                const ruleNumber = parseInt(ruleId);
                if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
                    completedRules.add(ruleNumber);
                    console.log(`📋 "状态应为"格式匹配到规则${ruleNumber}：${status}`);
                }
            }
        }

        // 使用Set来跟踪每个模式匹配到的规则，避免重复计数
        const patternRuleMatches = new Map();

        rulePatterns.forEach((pattern, index) => {
            let match;
            const currentPatternRules = new Set();

            while ((match = pattern.exec(aiThinking)) !== null) {
                let ruleId, status;

                // 根据不同的正则表达式组确定规则号和状态
                if (index < 7) { // 前7个模式：规则号在第1组，状态在第2组
                    ruleId = match[1];
                    status = match[2];
                } else if (index === 7) { // 第8个模式：合并规则格式
                    ruleId = match[1];
                    status = match[2];
                } else if (index === 8 || index === 9) { // JSON格式的规则
                    ruleId = match[1];
                    status = match[2];
                } else if (index === 10) { // 其他模式：状态在第1组，规则号在第2组
                    status = match[1];
                    ruleId = match[2];
                } else if (index === 11 || index === 12) { // 控制台日志格式：规则号在第1组，状态在第2组
                    ruleId = match[1];
                    status = match[2];
                } else { // 其他模式
                    ruleId = match[1];
                    status = match[2];
                }

                // 处理合并规则
                if (ruleId && ruleId.includes('-')) {
                    const mergedRuleNumbers = mergedRules[ruleId];
                    if (mergedRuleNumbers) {
                        mergedRuleNumbers.forEach(ruleNumber => {
                            if (!completedRules.has(ruleNumber)) {
                                completedRules.add(ruleNumber);
                                currentPatternRules.add(ruleNumber);
                                console.log(`📋 模式${index + 1}匹配到合并规则${ruleId}中的规则${ruleNumber}：${status}`);
                            }
                        });
                    }
                } else {
                    const ruleNumber = parseInt(ruleId);
                    if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
                        if (!completedRules.has(ruleNumber)) {
                            completedRules.add(ruleNumber);
                            currentPatternRules.add(ruleNumber);
                            console.log(`📋 模式${index + 1}匹配到规则${ruleNumber}：${status}`);
                        }
                    }
                }
            }

            patternRuleMatches.set(index, currentPatternRules);
            if (currentPatternRules.size > 0) {
                console.log(`✅ 模式${index + 1}新匹配${currentPatternRules.size}条规则`);
            }
        });

        console.log('📊 总共解析到的规则:', Array.from(completedRules).sort((a, b) => a - b));

        // 根据完成的规则更新各阶段进度
        completedRules.forEach(ruleNumber => {
            for (const [phase, range] of Object.entries(ruleRanges)) {
                if (ruleNumber >= range.start && ruleNumber <= range.end) {
                    phaseProgress[phase].completed++;
                    break;
                }
            }
        });

        // 检查JSON格式的审核结果（补充解析）
        const jsonPattern = /\[[\s\S]*?\]/g;
        const jsonMatches = aiThinking.match(jsonPattern);

        if (jsonMatches) {
            jsonMatches.forEach(jsonStr => {
                try {
                    const results = JSON.parse(jsonStr);
                    if (Array.isArray(results)) {
                        results.forEach(result => {
                            if (result.rule_id && result.status) {
                                // 匹配普通规则和合并规则
                                const ruleMatch = result.rule_id.match(/规则(\d+(?:-\d+)?)/);
                                if (ruleMatch) {
                                    const ruleId = ruleMatch[1];

                                    // 处理合并规则
                                    if (ruleId.includes('-')) {
                                        const mergedRuleNumbers = mergedRules[ruleId];
                                        if (mergedRuleNumbers) {
                                            mergedRuleNumbers.forEach(ruleNumber => {
                                                completedRules.add(ruleNumber);
                                                console.log(`📋 JSON格式匹配到合并规则${ruleId}中的规则${ruleNumber}：${result.status}`);
                                            });
                                        }
                                    } else {
                                        const ruleNumber = parseInt(ruleId);
                                        if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
                                            completedRules.add(ruleNumber);
                                            console.log(`📋 JSON格式匹配到规则${ruleNumber}：${result.status}`);
                                        }
                                    }
                                }
                            }
                        });
                    }
                } catch (e) {
                    // 忽略JSON解析错误
                }
            });

            // 重新计算各阶段进度（基于更新后的completedRules）
            Object.keys(phaseProgress).forEach(phase => {
                phaseProgress[phase].completed = 0;
            });

            completedRules.forEach(ruleNumber => {
                for (const [phase, range] of Object.entries(ruleRanges)) {
                    if (ruleNumber >= range.start && ruleNumber <= range.end) {
                        phaseProgress[phase].completed++;
                        break;
                    }
                }
            });
        }

        // 输出详细的阶段进度统计
        console.log('📊 各阶段规则完成情况详细统计:');
        Object.entries(phaseProgress).forEach(([phase, progress]) => {
            const percentage = progress.total > 0 ? ((progress.completed / progress.total) * 100).toFixed(1) : 0;
            console.log(`  ${phase}: ${progress.completed}/${progress.total} (${percentage}%)`);
        });

        return phaseProgress;
    }

    // 调试函数：手动触发进度解析
    debugParseProgress() {
        console.log('🔧 手动触发进度解析...');
        if (this.stateManager && this.stateManager.currentState) {
            const statusData = this.stateManager.currentState;
            console.log('🔧 当前状态数据:', statusData);

            if (statusData.ai_thinking) {
                console.log('🔧 开始解析AI思维链...');
                const phaseProgress = this.parseAIThinkingForRuleProgress(statusData.ai_thinking);
                console.log('🔧 解析结果:', phaseProgress);

                // 手动更新引擎状态
                this.updateRealEngineStatus(statusData.current_phase, statusData);
            } else {
                console.log('🔧 没有AI思维链内容');
            }
        } else {
            console.log('🔧 没有状态数据');
        }
    }

    updateOverallStats(phases, phaseProgress, statusData) {
        // 更新总体统计数据
        const totalRules = phases.reduce((sum, phase) => sum + phase.count, 0);
        let completedRules = 0;

        // 基于AI思维链解析结果计算已完成的规则数量
        Object.values(phaseProgress).forEach(progress => {
            completedRules += progress.completed;
        });

        // 如果审核完成，所有规则都算完成
        if (statusData.status === 'completed' || statusData.current_step === 'finished') {
            completedRules = totalRules;
        }

        // 更新页面显示
        const rulesCompletedElement = document.getElementById('rules-completed');
        const totalRulesElement = document.getElementById('total-rules');

        if (rulesCompletedElement) {
            rulesCompletedElement.textContent = completedRules;
        }
        if (totalRulesElement) {
            totalRulesElement.textContent = totalRules;
        }

        console.log(`📊 统计更新: ${completedRules}/${totalRules} 规则已完成`);
        console.log('📊 各阶段进度详情:', phaseProgress);
    }

    
    animateProgress(progressElement, textElement, totalRules) {
        let currentProgress = 0;
        const interval = setInterval(() => {
            currentProgress += Math.random() * 2;
            if (currentProgress >= totalRules) {
                currentProgress = totalRules;
                clearInterval(interval);
            }
            
            const percentage = (currentProgress / totalRules) * 100;
            progressElement.style.width = `${percentage}%`;
            textElement.textContent = `${Math.floor(currentProgress)}/${totalRules}`;
        }, 200);
    }
    
    // 重复的updateRealStats方法已删除，使用上面的完整版本
    
    async startAudit() {
        // 重写开始审核方法，集成真实数据
        if (this.isAuditing) return;
        
        this.isAuditing = true;
        this.updateControlButtons();
        
        // 如果有真实数据，使用真实的执行流程
        if (this.realAuditData) {
            await this.executeRealAuditFlow();
        } else {
            // 回退到默认流程
            await this.executeDefaultAuditFlow();
        }
    }
    
    async executeRealAuditFlow() {
        // 基于真实数据的审核流程
        const details = this.realAuditData.details;
        
        // 按规则类型分组
        const ruleGroups = this.groupRulesByType(details);
        
        // 执行确定性规则
        await this.executeRealRuleGroup('deterministic', ruleGroups.deterministic);
        
        // 执行关键词规则  
        await this.executeRealRuleGroup('keyword', ruleGroups.keyword);
        
        // 执行语义规则
        await this.executeRealRuleGroup('semantic', ruleGroups.semantic);
        
        // 完成审核
        this.completeAudit();
    }
    
    groupRulesByType(details) {
        // 根据规则ID将规则分组
        const groups = {
            deterministic: [],
            keyword: [],
            semantic: []
        };
        
        details.forEach(rule => {
            const ruleId = rule.rule_id;
            if (ruleId.includes('规则1') || ruleId.includes('规则2') || ruleId.includes('规则3') ||
                ruleId.includes('规则4') || ruleId.includes('规则5') || ruleId.includes('规则6') ||
                ruleId.includes('规则9') || ruleId.includes('规则10') || ruleId.includes('规则11') ||
                ruleId.includes('规则12')) {
                groups.deterministic.push(rule);
            } else if (ruleId.includes('规则7') || ruleId.includes('规则8') || ruleId.includes('规则13') ||
                      ruleId.includes('规则14') || ruleId.includes('规则17') || ruleId.includes('规则18') ||
                      ruleId.includes('规则19') || ruleId.includes('规则20')) {
                groups.keyword.push(rule);
            } else {
                groups.semantic.push(rule);
            }
        });
        
        return groups;
    }
    
    async executeRealRuleGroup(engineType, rules) {
        const engine = this.ruleEngines[engineType];
        engine.status = 'running';
        this.updateEngineDisplay(engineType);
        
        for (let i = 0; i < rules.length; i++) {
            if (!this.isAuditing) break;
            
            const rule = rules[i];
            
            // 显示真实的规则信息
            this.updateCurrentRuleReal(rule);
            
            // 模拟执行时间
            await this.sleep(300 + Math.random() * 700);
            
            engine.completed++;
            this.currentStep++;
            
            this.updateEngineDisplay(engineType);
            this.updateOverallProgress();
        }
        
        engine.status = 'completed';
        this.updateEngineDisplay(engineType);
    }
    
    updateCurrentRuleReal(rule) {
        // 显示真实的规则信息
        document.getElementById('current-rule-id').textContent = rule.rule_id;
        document.getElementById('current-rule-desc').textContent = rule.message;
        
        // 更新规则进度条
        const progressFill = document.getElementById('current-rule-progress');
        progressFill.style.width = '0%';
        setTimeout(() => {
            progressFill.style.width = '100%';
        }, 100);
    }
    
    // 默认审核流程（当没有真实数据时）
    async executeDefaultAuditFlow() {
        console.log('🔄 执行默认审核流程...');

        // 模拟审核步骤
        const steps = [
            { step: 'data-loading', message: '正在加载数据文件...', progress: 20 },
            { step: 'rule-parsing', message: '正在解析审核规则...', progress: 40 },
            { step: 'deterministic-rules', message: '正在执行确定性规则检查...', progress: 60 },
            { step: 'keyword-rules', message: '正在执行关键词规则检查...', progress: 80 },
            { step: 'audit-complete', message: '审核完成！', progress: 100 }
        ];

        for (const stepData of steps) {
            this.updateRealStatus(stepData);
            this.updateRealProgress(stepData);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 更新最终统计
        this.updateRealStats({
            total_rules_checked: 38,
            passed_count: 35,
            failed_count: 1,
            warning_count: 2
        });

        console.log('✅ 默认审核流程完成');
    }

    destroy() {
        // 清理资源
        if (this.statusPollingInterval) {
            clearInterval(this.statusPollingInterval);
        }
    }
}

// 使用增强版控制台
async function initializeAIConsole() {
    console.log('🚀 初始化AI控制台...');
    try {
        window.aiConsole = new AIConsoleEnhanced();
        await window.aiConsole.init();
        console.log('✅ AI控制台初始化成功');
    } catch (error) {
        console.error('❌ AI控制台初始化失败:', error);
    }
}

// 多种方式确保初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAIConsole);
} else {
    // DOM已经加载完成
    initializeAIConsole();
}

// 备用初始化
window.addEventListener('load', () => {
    if (!window.aiConsole) {
        console.log('🔄 备用初始化AI控制台...');
        initializeAIConsole();
    }
});

// 添加调试功能
setTimeout(() => {
    console.log('🔍 调试信息:');
    console.log('- window.aiConsole:', typeof window.aiConsole);
    console.log('- AIConsoleEnhanced:', typeof AIConsoleEnhanced);
    console.log('- 当前URL:', window.location.href);

    // 获取文档编号
    const urlParams = new URLSearchParams(window.location.search);
    const docNum = urlParams.get('doc_num');
    console.log('- 文档编号:', docNum);

    if (window.aiConsole && window.aiConsole.apiBaseUrl) {
        console.log('- API服务器:', window.aiConsole.apiBaseUrl);
    }

    // 添加调试快捷键
    window.addEventListener('keydown', (event) => {
        // Ctrl+Shift+D 触发调试解析
        if (event.ctrlKey && event.shiftKey && event.key === 'D') {
            event.preventDefault();
            if (window.aiConsole && typeof window.aiConsole.debugParseProgress === 'function') {
                console.log('🔧 手动触发调试解析...');
                window.aiConsole.debugParseProgress();
            } else {
                console.log('❌ 调试功能不可用');
            }
        }

        // Ctrl+Shift+R 强制刷新状态
        if (event.ctrlKey && event.shiftKey && event.key === 'R') {
            event.preventDefault();
            if (window.aiConsole && window.aiConsole.stateManager) {
                console.log('🔧 强制刷新状态...');
                window.aiConsole.stateManager.fetchState();
            } else {
                console.log('❌ 状态管理器不可用');
            }
        }
    });

    console.log('🔧 调试快捷键已启用:');
    console.log('  - Ctrl+Shift+D: 触发调试解析');
    console.log('  - Ctrl+Shift+R: 强制刷新状态');
}, 2000);

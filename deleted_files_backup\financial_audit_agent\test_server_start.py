#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务器启动的简化脚本
"""

import os
import sys
import json
import time
import socket
import threading
import http.server
import socketserver
from pathlib import Path
from urllib.parse import urlparse


class SimpleAPIHandler(http.server.BaseHTTPRequestHandler):
    """简化的API处理器"""
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        # API响应
        if parsed_path.path == '/api/status':
            response = {
                "current_step": "ready",
                "status": "online",
                "message": "系统就绪",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "server_status": "online"
            }
        elif parsed_path.path == '/api/report':
            # 尝试加载报告文件
            project_root = Path(__file__).parent
            report_file = project_root / "audit_reports" / "audit_report_ZDBXD2025051300001.json"
            
            if report_file.exists():
                try:
                    with open(report_file, 'r', encoding='utf-8') as f:
                        response = json.load(f)
                    print(f"[成功] 加载报告文件: {report_file.name}")
                except Exception as e:
                    print(f"[错误] 读取报告失败: {e}")
                    response = {"error": "Failed to load report"}
            else:
                print(f"[警告] 报告文件不存在: {report_file}")
                response = {"error": "Report not found"}
        else:
            response = {"error": "API endpoint not found"}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.end_headers()
    
    def log_message(self, format, *args):
        pass  # 静默日志


def find_available_port(start_port):
    """查找可用端口"""
    for port in range(start_port, start_port + 20):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return port
            except OSError:
                continue
    return None


def start_api_server():
    """启动API服务器"""
    api_port = find_available_port(8001)
    if not api_port:
        print("[错误] 无法找到可用的API端口")
        return None
    
    def run_api():
        try:
            with socketserver.TCPServer(("", api_port), SimpleAPIHandler) as httpd:
                httpd.serve_forever()
        except Exception as e:
            print(f"[错误] API服务器异常: {e}")
    
    api_thread = threading.Thread(target=run_api, daemon=True)
    api_thread.start()
    
    time.sleep(1)  # 等待服务器启动
    print(f"[成功] API服务器已启动: http://localhost:{api_port}")
    return api_port


def start_web_server():
    """启动Web服务器"""
    web_port = find_available_port(8002)
    if not web_port:
        print("[错误] 无法找到可用的Web端口")
        return None
    
    class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def log_message(self, format, *args):
            pass  # 静默日志
    
    def run_web():
        try:
            original_cwd = os.getcwd()
            project_root = Path(__file__).parent
            os.chdir(project_root)
            with socketserver.TCPServer(("", web_port), CORSRequestHandler) as httpd:
                httpd.serve_forever()
        except Exception as e:
            print(f"[错误] Web服务器异常: {e}")
        finally:
            try:
                os.chdir(original_cwd)
            except:
                pass
    
    web_thread = threading.Thread(target=run_web, daemon=True)
    web_thread.start()
    
    time.sleep(1)  # 等待服务器启动
    print(f"[成功] Web服务器已启动: http://localhost:{web_port}")
    return web_port


def main():
    """主函数"""
    print("测试服务器启动")
    print("=" * 40)
    
    try:
        # 启动API服务器
        print("启动API服务器...")
        api_port = start_api_server()
        
        # 启动Web服务器
        print("启动Web服务器...")
        web_port = start_web_server()
        
        if api_port and web_port:
            print(f"\n[完成] 服务启动成功！")
            print(f"API服务: http://localhost:{api_port}")
            print(f"Web服务: http://localhost:{web_port}")
            print(f"AI控制台: http://localhost:{web_port}/frontend/ai_console.html")
            print("\n按 Ctrl+C 停止服务")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n[停止] 服务已停止")
        else:
            print("[错误] 服务启动失败")
            return 1
    
    except Exception as e:
        print(f"[错误] 启动异常: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

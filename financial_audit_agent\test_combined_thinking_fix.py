#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试combined_thinking修复效果的脚本
"""

import json
import sys
import os
from pathlib import Path

# 添加后端路径
sys.path.append(str(Path(__file__).parent / "backend"))

from auditor_v2.orchestrator_v2 import ReportFileManager

def test_combined_thinking_fix():
    """测试combined_thinking修复效果"""
    
    print("🔧 测试combined_thinking修复效果")
    print("=" * 60)
    
    # 1. 读取现有的审核报告
    report_file = Path("audit_reports/audit_report_ZDBXD2025042900003.json")
    
    if not report_file.exists():
        print(f"❌ 报告文件不存在: {report_file}")
        return False
    
    with open(report_file, 'r', encoding='utf-8') as f:
        report_data = json.load(f)
    
    print(f"✅ 成功读取报告文件: {report_file}")
    
    # 2. 检查原始数据结构
    ai_thinking_chain = report_data.get("ai_thinking_chain", {})
    phases_history = ai_thinking_chain.get("phases_history", {})
    original_combined_thinking = ai_thinking_chain.get("combined_thinking", "")
    
    print(f"\n📊 原始数据分析:")
    print(f"  phases_history键数量: {len(phases_history)}")
    print(f"  phases_history键列表: {list(phases_history.keys())}")
    print(f"  original_combined_thinking长度: {len(original_combined_thinking)}")
    print(f"  是否包含阶段2: {'阶段 2/4' in original_combined_thinking}")
    
    # 3. 创建ReportFileManager实例并测试修复
    temp_report_file = Path("temp_test_report.json")
    
    try:
        # 复制原始数据到临时文件
        with open(temp_report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        # 创建ReportFileManager实例
        report_manager = ReportFileManager(str(temp_report_file))
        
        # 4. 测试修复后的combined_thinking生成
        print(f"\n🔧 测试修复后的combined_thinking生成:")
        
        fixed_combined_thinking = report_manager._build_combined_thinking_from_phases(phases_history)
        
        print(f"  修复后combined_thinking长度: {len(fixed_combined_thinking)}")
        print(f"  是否包含阶段2: {'阶段 2/4' in fixed_combined_thinking}")
        print(f"  是否包含第1组: {'第1组' in fixed_combined_thinking}")
        print(f"  是否包含第2组: {'第2组' in fixed_combined_thinking}")
        
        # 5. 显示修复前后的对比
        print(f"\n📋 修复前后对比:")
        print(f"  原始长度: {len(original_combined_thinking)} 字符")
        print(f"  修复后长度: {len(fixed_combined_thinking)} 字符")
        print(f"  长度增加: {len(fixed_combined_thinking) - len(original_combined_thinking)} 字符")
        
        # 6. 检查各阶段是否都包含
        stages_check = {
            "阶段 1/4": "阶段 1/4" in fixed_combined_thinking,
            "阶段 2/4": "阶段 2/4" in fixed_combined_thinking,
            "阶段 3/4": "阶段 3/4" in fixed_combined_thinking,
            "阶段 4/4": "阶段 4/4" in fixed_combined_thinking,
        }
        
        print(f"\n✅ 阶段完整性检查:")
        for stage, exists in stages_check.items():
            status = "✅" if exists else "❌"
            print(f"  {status} {stage}: {'存在' if exists else '缺失'}")
        
        # 7. 保存修复后的数据（可选）
        if all(stages_check.values()):
            print(f"\n🎉 修复成功！所有阶段都已包含在combined_thinking中")
            
            # 更新报告数据
            report_data["ai_thinking_chain"]["combined_thinking"] = fixed_combined_thinking
            
            # 保存到原文件（可选，取消注释以实际保存）
            # with open(report_file, 'w', encoding='utf-8') as f:
            #     json.dump(report_data, f, ensure_ascii=False, indent=2)
            # print(f"✅ 已更新原始报告文件")
            
            return True
        else:
            print(f"\n❌ 修复失败！仍有阶段缺失")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    finally:
        # 清理临时文件
        if temp_report_file.exists():
            temp_report_file.unlink()

def test_phase2_combined_content():
    """专门测试第二部分内容合并"""
    
    print(f"\n🔍 专门测试第二部分内容合并")
    print("-" * 40)
    
    # 读取报告数据
    report_file = Path("audit_reports/audit_report_ZDBXD2025042900003.json")
    
    with open(report_file, 'r', encoding='utf-8') as f:
        report_data = json.load(f)
    
    phases_history = report_data.get("ai_thinking_chain", {}).get("phases_history", {})
    
    # 创建临时ReportFileManager实例
    temp_report_file = Path("temp_test_report2.json")
    
    try:
        with open(temp_report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        report_manager = ReportFileManager(str(temp_report_file))
        
        # 测试第二部分内容合并
        phase2_content = report_manager._build_phase2_combined_content(phases_history)
        
        print(f"  第二部分合并内容长度: {len(phase2_content)}")
        print(f"  是否包含第1组: {'第1组' in phase2_content}")
        print(f"  是否包含第2组: {'第2组' in phase2_content}")
        print(f"  是否包含规则6-14: {'规则6-14' in phase2_content}")
        print(f"  是否包含规则15-24: {'规则15-24' in phase2_content}")
        
        # 显示内容预览
        if phase2_content:
            print(f"\n📄 第二部分内容预览 (前200字符):")
            print(f"  {phase2_content[:200]}...")
            return True
        else:
            print(f"❌ 第二部分内容为空")
            return False
            
    finally:
        if temp_report_file.exists():
            temp_report_file.unlink()

if __name__ == "__main__":
    print("🚀 开始测试combined_thinking修复效果")
    
    # 测试1: 整体修复效果
    success1 = test_combined_thinking_fix()
    
    # 测试2: 第二部分专门测试
    success2 = test_phase2_combined_content()
    
    print(f"\n🎯 测试结果总结:")
    print(f"  整体修复测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  第二部分测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print(f"\n🎉 所有测试通过！combined_thinking修复成功！")
        print(f"\n📋 下一步操作:")
        print(f"  1. 重启后端服务以应用修复")
        print(f"  2. 刷新前端页面验证效果")
        print(f"  3. 检查'🧠 完整思维链概览'是否包含阶段2内容")
    else:
        print(f"\n❌ 测试失败，需要进一步调试")
